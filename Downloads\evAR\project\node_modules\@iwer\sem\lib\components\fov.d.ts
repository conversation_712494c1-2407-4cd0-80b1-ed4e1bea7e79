/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import React from 'react';
import { InputLayer } from '../scene.js';
import { XRDevice } from 'iwer';
interface FOVMenuProps {
    xrDevice: XRDevice;
    inputLayer: InputLayer;
}
export declare const FOVMenu: React.FC<FOVMenuProps>;
export {};
//# sourceMappingURL=fov.d.ts.map