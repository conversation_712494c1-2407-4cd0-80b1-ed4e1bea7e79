{"version": 3, "file": "timestamp.js", "sourceRoot": "", "sources": ["../../../../src/generated/google/protobuf/timestamp.ts"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,YAAY;AACZ,gCAAgC;AAChC,iCAAiC;AACjC,0CAA0C;AAE1C,oBAAoB;AACpB,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAErE,MAAM,CAAC,MAAM,eAAe,GAAG,iBAAiB,CAAC;AA6GjD,SAAS,mBAAmB;IAC1B,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AAClC,CAAC;AAED,MAAM,CAAC,MAAM,SAAS,GAA0B;IAC9C,MAAM,CAAC,OAAkB,EAAE,SAAuB,IAAI,YAAY,EAAE;QAClE,IAAI,OAAO,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC;QACtC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YACxB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC;gBAClB,KAAK,CAAC,CAAC,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;wBACd,MAAM;oBACR,CAAC;oBAED,OAAO,CAAC,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;oBAC/C,SAAS;gBACX,CAAC;gBACD,KAAK,CAAC,CAAC,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,EAAE,EAAE,CAAC;wBACf,MAAM;oBACR,CAAC;oBAED,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAC/B,SAAS;gBACX,CAAC;YACH,CAAC;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM;YACR,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACvB,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACjE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAkB;QACvB,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YAC1B,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;YACxB,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA6C,IAAQ;QACzD,OAAO,SAAS,CAAC,WAAW,CAAC,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAK,EAAU,CAAC,CAAC;IACpD,CAAC;IACD,WAAW,CAA6C,MAAS;;QAC/D,MAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC;QACtC,OAAO,CAAC,OAAO,GAAG,MAAA,MAAM,CAAC,OAAO,mCAAI,CAAC,CAAC;QACtC,OAAO,CAAC,KAAK,GAAG,MAAA,MAAM,CAAC,KAAK,mCAAI,CAAC,CAAC;QAClC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAcF,SAAS,YAAY,CAAC,KAA6B;IACjD,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;IAChD,IAAI,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAC7C,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;IAC7E,CAAC;IACD,IAAI,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAC7C,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAC9E,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,KAAK,CAAC,KAAU;IACvB,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/C,CAAC"}