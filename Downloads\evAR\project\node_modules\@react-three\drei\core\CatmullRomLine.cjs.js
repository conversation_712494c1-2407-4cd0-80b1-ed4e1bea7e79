"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),n=require("./Line.cjs.js");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("@react-three/fiber"),require("three-stdlib");var s=o(e),c=u(t);const l=c.forwardRef((function({points:e,closed:t=!1,curveType:o="centripetal",tension:u=.5,segments:l=20,vertexColors:i,...f},a){const p=c.useMemo((()=>{const n=e.map((e=>e instanceof r.Vector3?e:new r.Vector3(...e)));return new r.CatmullRomCurve3(n,t,o,u)}),[e,t,o,u]),d=c.useMemo((()=>p.getPoints(l)),[p,l]),h=c.useMemo((()=>{if(!i||i.length<2)return;if(i.length===l+1)return i;const e=i.map((e=>e instanceof r.Color?e:new r.Color(...e)));t&&e.push(e[0].clone());const n=[e[0]],o=l/(e.length-1);for(let t=1;t<l;t++){const r=t%o/o,u=Math.floor(t/o);n.push(e[u].clone().lerp(e[u+1],r))}return n.push(e[e.length-1]),n}),[i,l]);return c.createElement(n.Line,s.default({ref:a,points:d,vertexColors:h},f))}));exports.CatmullRomLine=l;
