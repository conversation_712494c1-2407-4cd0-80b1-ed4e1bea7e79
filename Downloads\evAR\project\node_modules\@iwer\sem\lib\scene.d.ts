/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Group, Mesh, PerspectiveCamera, Quaternion, Scene, Vector3, WebGLRenderer } from 'three';
import { TransformControls } from 'three/examples/jsm/controls/TransformControls.js';
import { XRDevice } from 'iwer';
declare const PRIVATE: unique symbol;
export declare class InputLayer {
    [PRIVATE]: {
        canvasContainer: HTMLDivElement;
        renderer: WebGLRenderer;
        scene: Scene;
        camera: PerspectiveCamera;
        playerRig: Group;
        cameraRig: Group;
        resizeObserver: ResizeObserver;
        xrDevice: XRDevice;
        controllerIndicators: {
            [key in 'left' | 'right']: Mesh;
        };
        transformControls: {
            [key in 'left' | 'right']: TransformControls;
        };
        isPointerLocked: boolean;
        vec3: Vector3;
        quat: Quaternion;
        mouseMoveHandler: (event: MouseEvent) => void;
        headsetDefaultPosition: Vector3;
        headsetDefaultQuaternion: Quaternion;
        controllerDefaultPositions: {
            [key in 'left' | 'right']: Vector3;
        };
        controllerDefaultQuaternions: {
            [key in 'left' | 'right']: Quaternion;
        };
        keyState: {
            [key: string]: boolean;
        };
        movePlayerRig: () => void;
        moveInterval: number | null;
    };
    constructor(xrDevice: XRDevice);
    lockPointer(): void;
    pointerLockChangeHandler(): void;
    keyDownHandler(event: KeyboardEvent): void;
    keyUpHandler(event: KeyboardEvent): void;
    movePlayerRig(): void;
    syncFovy(): void;
    resetDeviceTransforms(): void;
    syncDeviceTransforms(): void;
    renderScene(): void;
    get domElement(): HTMLCanvasElement;
    resize(): void;
    dispose(): void;
}
export {};
//# sourceMappingURL=scene.d.ts.map