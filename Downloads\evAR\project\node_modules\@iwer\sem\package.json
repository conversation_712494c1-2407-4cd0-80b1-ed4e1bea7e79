{"name": "@iwer/sem", "version": "0.2.5", "description": "Synthetic Environment Module for IWER", "type": "module", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["build", "lib", "captures"], "scripts": {"prebuild": "node scripts/prebuild.cjs", "build": "tsc && rollup -c"}, "repository": {"type": "git", "url": "git+https://github.com/meta-quest/immersive-web-emulation-runtime.git"}, "keywords": [], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/meta-quest/immersive-web-emulation-runtime/issues"}, "homepage": "https://github.com/meta-quest/immersive-web-emulation-runtime#readme", "dependencies": {"three": "^0.165.0", "ts-proto": "^2.6.0"}, "peerDependencies": {"iwer": "^2.0.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^6.0.1", "@rollup/plugin-terser": "^0.4.4", "@types/three": "^0.170.0", "rollup": "^4.24.3", "rollup-plugin-peer-deps-external": "^2.2.4", "typescript": "^5.6.3"}}