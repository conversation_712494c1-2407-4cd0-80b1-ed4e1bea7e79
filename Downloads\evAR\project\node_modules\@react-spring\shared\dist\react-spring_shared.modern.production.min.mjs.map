{"version": 3, "sources": ["../src/globals.ts", "../src/helpers.ts", "../src/FrameLoop.ts", "../src/clamp.ts", "../src/colors.ts", "../src/colorMatchers.ts", "../src/normalizeColor.ts", "../src/colorToRgba.ts", "../src/createInterpolator.ts", "../src/easings.ts", "../src/fluids.ts", "../src/regexs.ts", "../src/variableToRgba.ts", "../src/stringInterpolation.ts", "../src/deprecations.ts", "../src/isAnimatedString.ts", "../src/dom-events/scroll/index.ts", "../src/dom-events/resize/resizeElement.ts", "../src/dom-events/resize/resizeWindow.ts", "../src/dom-events/resize/index.ts", "../src/progress.ts", "../src/dom-events/scroll/ScrollHandler.ts", "../src/hooks/useConstant.ts", "../src/hooks/useForceUpdate.ts", "../src/hooks/useIsMounted.ts", "../src/hooks/useIsomorphicLayoutEffect.ts", "../src/hooks/useMemoOne.ts", "../src/hooks/useOnce.ts", "../src/hooks/usePrev.ts", "../src/hooks/useReducedMotion.ts", "../src/index.ts"], "sourcesContent": ["import { raf, <PERSON><PERSON><PERSON> } from '@react-spring/rafz'\nimport {\n  OneOrMore,\n  InterpolatorConfig,\n  InterpolatorArgs,\n} from '@react-spring/types'\n\nimport { FluidValue } from './fluids'\nimport type { OpaqueAnimation } from './FrameLoop'\nimport { noop } from './helpers'\n\n//\n// Required\n//\n\nexport let createStringInterpolator: (\n  config: InterpolatorConfig<string>\n) => (input: number) => string\n\n//\n// Optional\n//\n\nexport let to: <Input, Output>(\n  source: OneOrMore<FluidValue>,\n  args: InterpolatorArgs<Input, Output>\n) => FluidValue<Output>\n\nexport let colors = null as { [key: string]: number } | null\n\nexport let skipAnimation = false as boolean\n\nexport let willAdvance: (animation: OpaqueAnimation) => void = noop\n\n//\n// Configuration\n//\n\nexport interface AnimatedGlobals {\n  /** Returns a new `Interpolation` object */\n  to?: typeof to\n  /** Used to measure frame length. Read more [here](https://developer.mozilla.org/en-US/docs/Web/API/Performance/now) */\n  now?: typeof raf.now\n  /** Provide custom color names for interpolation */\n  colors?: typeof colors\n  /** Make all animations instant and skip the frameloop entirely */\n  skipAnimation?: typeof skipAnimation\n  /** Provide custom logic for string interpolation */\n  createStringInterpolator?: typeof createStringInterpolator\n  /** Schedule a function to run on the next frame */\n  requestAnimationFrame?: (cb: () => void) => void\n  /** Event props are called with `batchedUpdates` to reduce extraneous renders */\n  batchedUpdates?: typeof raf.batchedUpdates\n  /** @internal Exposed for testing purposes */\n  willAdvance?: typeof willAdvance\n  /** sets the global frameLoop setting for the global raf instance */\n  frameLoop?: Rafz['frameLoop']\n}\n\nexport const assign = (globals: AnimatedGlobals) => {\n  if (globals.to) to = globals.to\n  if (globals.now) raf.now = globals.now\n  if (globals.colors !== undefined) colors = globals.colors\n  if (globals.skipAnimation != null) skipAnimation = globals.skipAnimation\n  if (globals.createStringInterpolator)\n    createStringInterpolator = globals.createStringInterpolator\n  if (globals.requestAnimationFrame) raf.use(globals.requestAnimationFrame)\n  if (globals.batchedUpdates) raf.batchedUpdates = globals.batchedUpdates\n  if (globals.willAdvance) willAdvance = globals.willAdvance\n  if (globals.frameLoop) raf.frameLoop = globals.frameLoop\n}\n", "import { Lookup, Arrify, AnyFn, Any } from '@react-spring/types'\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop() {}\n\nexport const defineHidden = (obj: any, key: any, value: any) =>\n  Object.defineProperty(obj, key, { value, writable: true, configurable: true })\n\ntype IsType<U> = <T>(arg: T & any) => arg is Narrow<T, U>\ntype Narrow<T, U> = [T] extends [Any] ? U : [T] extends [U] ? Extract<T, U> : U\n\ntype PlainObject<T> = Exclude<T & Lookup, Function | readonly any[]>\n\nexport const is = {\n  arr: Array.isArray as IsType<readonly any[]>,\n  obj: <T>(a: T & any): a is PlainObject<T> =>\n    !!a && a.constructor.name === 'Object',\n  fun: ((a: unknown) => typeof a === 'function') as IsType<Function>,\n  str: (a: unknown): a is string => typeof a === 'string',\n  num: (a: unknown): a is number => typeof a === 'number',\n  und: (a: unknown): a is undefined => a === undefined,\n}\n\n/** Compare animatable values */\nexport function isEqual(a: any, b: any) {\n  if (is.arr(a)) {\n    if (!is.arr(b) || a.length !== b.length) return false\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) return false\n    }\n    return true\n  }\n  return a === b\n}\n\ntype EachFn<Value, Key, This> = (this: This, value: Value, key: Key) => void\ntype Eachable<Value = any, Key = any, This = any> = {\n  forEach(cb: EachFn<Value, Key, This>, ctx?: This): void\n}\n\n/** Minifiable `.forEach` call */\nexport const each = <Value, Key, This>(\n  obj: Eachable<Value, Key, This>,\n  fn: EachFn<Value, Key, This>\n) => obj.forEach(fn)\n\n/** Iterate the properties of an object */\nexport function eachProp<T extends object, This>(\n  obj: T,\n  fn: (\n    this: This,\n    value: T extends any[] ? T[number] : T[keyof T],\n    key: string\n  ) => void,\n  ctx?: This\n) {\n  if (is.arr(obj)) {\n    for (let i = 0; i < obj.length; i++) {\n      fn.call(ctx as any, obj[i] as any, `${i}`)\n    }\n    return\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      fn.call(ctx as any, obj[key] as any, key)\n    }\n  }\n}\n\nexport const toArray = <T>(a: T): Arrify<Exclude<T, void>> =>\n  is.und(a) ? [] : is.arr(a) ? (a as any) : [a]\n\n/** Copy the `queue`, then iterate it after the `queue` is cleared */\nexport function flush<P, T>(\n  queue: Map<P, T>,\n  iterator: (entry: [P, T]) => void\n): void\nexport function flush<T>(queue: Set<T>, iterator: (value: T) => void): void\nexport function flush(queue: any, iterator: any) {\n  if (queue.size) {\n    const items = Array.from(queue)\n    queue.clear()\n    each(items, iterator)\n  }\n}\n\n/** Call every function in the queue with the same arguments. */\nexport const flushCalls = <T extends AnyFn>(\n  queue: Set<T>,\n  ...args: Parameters<T>\n) => flush(queue, fn => fn(...args))\n\n// For server-side rendering: https://github.com/react-spring/zustand/pull/34\n// Deno support: https://github.com/pmndrs/zustand/issues/347\n\nexport const isSSR = () =>\n  typeof window === 'undefined' ||\n  !window.navigator ||\n  /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent)\n", "import { raf } from '@react-spring/rafz'\nimport * as G from './globals'\n\nexport interface OpaqueAnimation {\n  idle: boolean\n  priority: number\n  advance(dt: number): void\n}\n\n// Animations starting on the next frame\nconst startQueue = new Set<OpaqueAnimation>()\n\n// The animations being updated in the current frame, sorted by lowest\n// priority first. These two arrays are swapped at the end of each frame.\nlet currentFrame: OpaqueAnimation[] = []\nlet prevFrame: OpaqueAnimation[] = []\n\n// The priority of the currently advancing animation.\n// To protect against a race condition whenever a frame is being processed,\n// where the filtering of `animations` is corrupted with a shifting index,\n// causing animations to potentially advance 2x faster than intended.\nlet priority = 0\n\n/**\n * The frameloop executes its animations in order of lowest priority first.\n * Animations are retained until idle.\n */\nexport const frameLoop = {\n  get idle() {\n    return !startQueue.size && !currentFrame.length\n  },\n\n  /** Advance the given animation on every frame until idle. */\n  start(animation: OpaqueAnimation) {\n    // An animation can be added while a frame is being processed,\n    // unless its priority is lower than the animation last updated.\n    if (priority > animation.priority) {\n      startQueue.add(animation)\n      raf.onStart(flushStartQueue)\n    } else {\n      startSafely(animation)\n      raf(advance)\n    }\n  },\n\n  /** Advance all animations by the given time. */\n  advance,\n\n  /** Call this when an animation's priority changes. */\n  sort(animation: OpaqueAnimation) {\n    if (priority) {\n      raf.onFrame(() => frameLoop.sort(animation))\n    } else {\n      const prevIndex = currentFrame.indexOf(animation)\n      if (~prevIndex) {\n        currentFrame.splice(prevIndex, 1)\n        startUnsafely(animation)\n      }\n    }\n  },\n\n  /**\n   * Clear all animations. For testing purposes.\n   *\n   * ☠️ Never call this from within the frameloop.\n   */\n  clear() {\n    currentFrame = []\n    startQueue.clear()\n  },\n}\n\nfunction flushStartQueue() {\n  startQueue.forEach(startSafely)\n  startQueue.clear()\n  raf(advance)\n}\n\nfunction startSafely(animation: OpaqueAnimation) {\n  if (!currentFrame.includes(animation)) startUnsafely(animation)\n}\n\nfunction startUnsafely(animation: OpaqueAnimation) {\n  currentFrame.splice(\n    findIndex(currentFrame, other => other.priority > animation.priority),\n    0,\n    animation\n  )\n}\n\nfunction advance(dt: number) {\n  const nextFrame = prevFrame\n\n  for (let i = 0; i < currentFrame.length; i++) {\n    const animation = currentFrame[i]\n    priority = animation.priority\n\n    // Animations may go idle before advancing.\n    if (!animation.idle) {\n      G.willAdvance(animation)\n      animation.advance(dt)\n      if (!animation.idle) {\n        nextFrame.push(animation)\n      }\n    }\n  }\n  priority = 0\n\n  // Reuse the `currentFrame` array to avoid garbage collection.\n  prevFrame = currentFrame\n  prevFrame.length = 0\n\n  // Set `currentFrame` for next frame, so the `start` function\n  // adds new animations to the proper array.\n  currentFrame = nextFrame\n\n  return currentFrame.length > 0\n}\n\n/** Like `Array.prototype.findIndex` but returns `arr.length` instead of `-1` */\nfunction findIndex<T>(arr: T[], test: (value: T) => boolean) {\n  const index = arr.findIndex(test)\n  return index < 0 ? arr.length : index\n}\n", "export const clamp = (min: number, max: number, v: number) =>\n  Math.min(Math.max(v, min), max)\n", "export type ColorName = keyof typeof colors\n\n// http://www.w3.org/TR/css3-color/#svg-color\nexport const colors = {\n  transparent: 0x00000000,\n  aliceblue: 0xf0f8ffff,\n  antiquewhite: 0xfaebd7ff,\n  aqua: 0x00ffffff,\n  aquamarine: 0x7fffd4ff,\n  azure: 0xf0ffffff,\n  beige: 0xf5f5dcff,\n  bisque: 0xffe4c4ff,\n  black: 0x000000ff,\n  blanchedalmond: 0xffebcdff,\n  blue: 0x0000ffff,\n  blueviolet: 0x8a2be2ff,\n  brown: 0xa52a2aff,\n  burlywood: 0xdeb887ff,\n  burntsienna: 0xea7e5dff,\n  cadetblue: 0x5f9ea0ff,\n  chartreuse: 0x7fff00ff,\n  chocolate: 0xd2691eff,\n  coral: 0xff7f50ff,\n  cornflowerblue: 0x6495edff,\n  cornsilk: 0xfff8dcff,\n  crimson: 0xdc143cff,\n  cyan: 0x00ffffff,\n  darkblue: 0x00008bff,\n  darkcyan: 0x008b8bff,\n  darkgoldenrod: 0xb8860bff,\n  darkgray: 0xa9a9a9ff,\n  darkgreen: 0x006400ff,\n  darkgrey: 0xa9a9a9ff,\n  darkkhaki: 0xbdb76bff,\n  darkmagenta: 0x8b008bff,\n  darkolivegreen: 0x556b2fff,\n  darkorange: 0xff8c00ff,\n  darkorchid: 0x9932ccff,\n  darkred: 0x8b0000ff,\n  darksalmon: 0xe9967aff,\n  darkseagreen: 0x8fbc8fff,\n  darkslateblue: 0x483d8bff,\n  darkslategray: 0x2f4f4fff,\n  darkslategrey: 0x2f4f4fff,\n  darkturquoise: 0x00ced1ff,\n  darkviolet: 0x9400d3ff,\n  deeppink: 0xff1493ff,\n  deepskyblue: 0x00bfffff,\n  dimgray: 0x696969ff,\n  dimgrey: 0x696969ff,\n  dodgerblue: 0x1e90ffff,\n  firebrick: 0xb22222ff,\n  floralwhite: 0xfffaf0ff,\n  forestgreen: 0x228b22ff,\n  fuchsia: 0xff00ffff,\n  gainsboro: 0xdcdcdcff,\n  ghostwhite: 0xf8f8ffff,\n  gold: 0xffd700ff,\n  goldenrod: 0xdaa520ff,\n  gray: 0x808080ff,\n  green: 0x008000ff,\n  greenyellow: 0xadff2fff,\n  grey: 0x808080ff,\n  honeydew: 0xf0fff0ff,\n  hotpink: 0xff69b4ff,\n  indianred: 0xcd5c5cff,\n  indigo: 0x4b0082ff,\n  ivory: 0xfffff0ff,\n  khaki: 0xf0e68cff,\n  lavender: 0xe6e6faff,\n  lavenderblush: 0xfff0f5ff,\n  lawngreen: 0x7cfc00ff,\n  lemonchiffon: 0xfffacdff,\n  lightblue: 0xadd8e6ff,\n  lightcoral: 0xf08080ff,\n  lightcyan: 0xe0ffffff,\n  lightgoldenrodyellow: 0xfafad2ff,\n  lightgray: 0xd3d3d3ff,\n  lightgreen: 0x90ee90ff,\n  lightgrey: 0xd3d3d3ff,\n  lightpink: 0xffb6c1ff,\n  lightsalmon: 0xffa07aff,\n  lightseagreen: 0x20b2aaff,\n  lightskyblue: 0x87cefaff,\n  lightslategray: 0x778899ff,\n  lightslategrey: 0x778899ff,\n  lightsteelblue: 0xb0c4deff,\n  lightyellow: 0xffffe0ff,\n  lime: 0x00ff00ff,\n  limegreen: 0x32cd32ff,\n  linen: 0xfaf0e6ff,\n  magenta: 0xff00ffff,\n  maroon: 0x800000ff,\n  mediumaquamarine: 0x66cdaaff,\n  mediumblue: 0x0000cdff,\n  mediumorchid: 0xba55d3ff,\n  mediumpurple: 0x9370dbff,\n  mediumseagreen: 0x3cb371ff,\n  mediumslateblue: 0x7b68eeff,\n  mediumspringgreen: 0x00fa9aff,\n  mediumturquoise: 0x48d1ccff,\n  mediumvioletred: 0xc71585ff,\n  midnightblue: 0x191970ff,\n  mintcream: 0xf5fffaff,\n  mistyrose: 0xffe4e1ff,\n  moccasin: 0xffe4b5ff,\n  navajowhite: 0xffdeadff,\n  navy: 0x000080ff,\n  oldlace: 0xfdf5e6ff,\n  olive: 0x808000ff,\n  olivedrab: 0x6b8e23ff,\n  orange: 0xffa500ff,\n  orangered: 0xff4500ff,\n  orchid: 0xda70d6ff,\n  palegoldenrod: 0xeee8aaff,\n  palegreen: 0x98fb98ff,\n  paleturquoise: 0xafeeeeff,\n  palevioletred: 0xdb7093ff,\n  papayawhip: 0xffefd5ff,\n  peachpuff: 0xffdab9ff,\n  peru: 0xcd853fff,\n  pink: 0xffc0cbff,\n  plum: 0xdda0ddff,\n  powderblue: 0xb0e0e6ff,\n  purple: 0x800080ff,\n  rebeccapurple: 0x663399ff,\n  red: 0xff0000ff,\n  rosybrown: 0xbc8f8fff,\n  royalblue: 0x4169e1ff,\n  saddlebrown: 0x8b4513ff,\n  salmon: 0xfa8072ff,\n  sandybrown: 0xf4a460ff,\n  seagreen: 0x2e8b57ff,\n  seashell: 0xfff5eeff,\n  sienna: 0xa0522dff,\n  silver: 0xc0c0c0ff,\n  skyblue: 0x87ceebff,\n  slateblue: 0x6a5acdff,\n  slategray: 0x708090ff,\n  slategrey: 0x708090ff,\n  snow: 0xfffafaff,\n  springgreen: 0x00ff7fff,\n  steelblue: 0x4682b4ff,\n  tan: 0xd2b48cff,\n  teal: 0x008080ff,\n  thistle: 0xd8bfd8ff,\n  tomato: 0xff6347ff,\n  turquoise: 0x40e0d0ff,\n  violet: 0xee82eeff,\n  wheat: 0xf5deb3ff,\n  white: 0xffffffff,\n  whitesmoke: 0xf5f5f5ff,\n  yellow: 0xffff00ff,\n  yellowgreen: 0x9acd32ff,\n}\n", "// const INTEGER = '[-+]?\\\\d+';\nconst NUMBER = '[-+]?\\\\d*\\\\.?\\\\d+'\nconst PERCENTAGE = NUMBER + '%'\n\nfunction call(...parts: string[]) {\n  return '\\\\(\\\\s*(' + parts.join(')\\\\s*,\\\\s*(') + ')\\\\s*\\\\)'\n}\n\nexport const rgb = new RegExp('rgb' + call(NUMBER, NUMBER, NUMBER))\nexport const rgba = new RegExp('rgba' + call(NUMBER, NUMBER, NUMBER, NUMBER))\nexport const hsl = new RegExp('hsl' + call(NUMBER, PERCENTAGE, PERCENTAGE))\nexport const hsla = new RegExp(\n  'hsla' + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER)\n)\nexport const hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/\nexport const hex4 =\n  /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/\nexport const hex6 = /^#([0-9a-fA-F]{6})$/\nexport const hex8 = /^#([0-9a-fA-F]{8})$/\n", "/*\nhttps://github.com/react-community/normalize-css-color\n\nBSD 3-Clause License\n\nCopyright (c) 2016, React Community\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n* Redistributions of source code must retain the above copyright notice, this\n  list of conditions and the following disclaimer.\n\n* Redistributions in binary form must reproduce the above copyright notice,\n  this list of conditions and the following disclaimer in the documentation\n  and/or other materials provided with the distribution.\n\n* Neither the name of the copyright holder nor the names of its\n  contributors may be used to endorse or promote products derived from\n  this software without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\nDISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\nFOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, <PERSON>XEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL\nDAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\nSERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\nCAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\nOR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n\nimport * as matchers from './colorMatchers'\nimport * as G from './globals'\n\nexport function normalizeColor(color: number | string) {\n  let match\n\n  if (typeof color === 'number') {\n    return color >>> 0 === color && color >= 0 && color <= 0xffffffff\n      ? color\n      : null\n  }\n\n  // Ordered based on occurrences on Facebook codebase\n  if ((match = matchers.hex6.exec(color)))\n    return parseInt(match[1] + 'ff', 16) >>> 0\n\n  if (G.colors && G.colors[color] !== undefined) {\n    return G.colors[color]\n  }\n\n  if ((match = matchers.rgb.exec(color))) {\n    return (\n      ((parse255(match[1]) << 24) | // r\n        (parse255(match[2]) << 16) | // g\n        (parse255(match[3]) << 8) | // b\n        0x000000ff) >>> // a\n      0\n    )\n  }\n\n  if ((match = matchers.rgba.exec(color))) {\n    return (\n      ((parse255(match[1]) << 24) | // r\n        (parse255(match[2]) << 16) | // g\n        (parse255(match[3]) << 8) | // b\n        parse1(match[4])) >>> // a\n      0\n    )\n  }\n\n  if ((match = matchers.hex3.exec(color))) {\n    return (\n      parseInt(\n        match[1] +\n          match[1] + // r\n          match[2] +\n          match[2] + // g\n          match[3] +\n          match[3] + // b\n          'ff', // a\n        16\n      ) >>> 0\n    )\n  }\n\n  // https://drafts.csswg.org/css-color-4/#hex-notation\n  if ((match = matchers.hex8.exec(color))) return parseInt(match[1], 16) >>> 0\n\n  if ((match = matchers.hex4.exec(color))) {\n    return (\n      parseInt(\n        match[1] +\n          match[1] + // r\n          match[2] +\n          match[2] + // g\n          match[3] +\n          match[3] + // b\n          match[4] +\n          match[4], // a\n        16\n      ) >>> 0\n    )\n  }\n\n  if ((match = matchers.hsl.exec(color))) {\n    return (\n      (hslToRgb(\n        parse360(match[1]), // h\n        parsePercentage(match[2]), // s\n        parsePercentage(match[3]) // l\n      ) |\n        0x000000ff) >>> // a\n      0\n    )\n  }\n\n  if ((match = matchers.hsla.exec(color))) {\n    return (\n      (hslToRgb(\n        parse360(match[1]), // h\n        parsePercentage(match[2]), // s\n        parsePercentage(match[3]) // l\n      ) |\n        parse1(match[4])) >>> // a\n      0\n    )\n  }\n  return null\n}\n\nfunction hue2rgb(p: number, q: number, t: number) {\n  if (t < 0) t += 1\n  if (t > 1) t -= 1\n  if (t < 1 / 6) return p + (q - p) * 6 * t\n  if (t < 1 / 2) return q\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6\n  return p\n}\n\nfunction hslToRgb(h: number, s: number, l: number) {\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s\n  const p = 2 * l - q\n  const r = hue2rgb(p, q, h + 1 / 3)\n  const g = hue2rgb(p, q, h)\n  const b = hue2rgb(p, q, h - 1 / 3)\n  return (\n    (Math.round(r * 255) << 24) |\n    (Math.round(g * 255) << 16) |\n    (Math.round(b * 255) << 8)\n  )\n}\n\nfunction parse255(str: string) {\n  const int = parseInt(str, 10)\n  if (int < 0) return 0\n  if (int > 255) return 255\n  return int\n}\n\nfunction parse360(str: string) {\n  const int = parseFloat(str)\n  return (((int % 360) + 360) % 360) / 360\n}\n\nfunction parse1(str: string) {\n  const num = parseFloat(str)\n  if (num < 0) return 0\n  if (num > 1) return 255\n  return Math.round(num * 255)\n}\n\nfunction parsePercentage(str: string) {\n  // parseFloat conveniently ignores the final %\n  const int = parseFloat(str)\n  if (int < 0) return 0\n  if (int > 100) return 1\n  return int / 100\n}\n", "import { normalizeColor } from './normalizeColor'\n\nexport function colorToRgba(input: string) {\n  let int32Color = normalizeColor(input)\n  if (int32Color === null) return input\n  int32Color = int32Color || 0\n  const r = (int32Color & 0xff000000) >>> 24\n  const g = (int32Color & 0x00ff0000) >>> 16\n  const b = (int32Color & 0x0000ff00) >>> 8\n  const a = (int32Color & 0x000000ff) / 255\n  return `rgba(${r}, ${g}, ${b}, ${a})`\n}\n", "import * as G from './globals'\nimport { is } from './helpers'\nimport {\n  Animatable,\n  InterpolatorFn,\n  EasingFunction,\n  ExtrapolateType,\n  InterpolatorConfig,\n  InterpolatorFactory,\n} from '@react-spring/types'\n\nexport const createInterpolator: InterpolatorFactory = (\n  range: readonly number[] | InterpolatorFn<any, any> | InterpolatorConfig<any>,\n  output?: readonly Animatable[],\n  extrapolate?: ExtrapolateType\n) => {\n  if (is.fun(range)) {\n    return range\n  }\n\n  if (is.arr(range)) {\n    return createInterpolator({\n      range,\n      output: output!,\n      extrapolate,\n    })\n  }\n\n  if (is.str(range.output[0])) {\n    return G.createStringInterpolator(range as any) as any\n  }\n\n  const config = range as InterpolatorConfig<number>\n  const outputRange = config.output\n  const inputRange = config.range || [0, 1]\n\n  const extrapolateLeft =\n    config.extrapolateLeft || config.extrapolate || 'extend'\n  const extrapolateRight =\n    config.extrapolateRight || config.extrapolate || 'extend'\n  const easing = config.easing || (t => t)\n\n  return (input: number) => {\n    const range = findRange(input, inputRange)\n    return interpolate(\n      input,\n      inputRange[range],\n      inputRange[range + 1],\n      outputRange[range],\n      outputRange[range + 1],\n      easing,\n      extrapolateLeft,\n      extrapolateRight,\n      config.map\n    )\n  }\n}\n\nfunction interpolate(\n  input: number,\n  inputMin: number,\n  inputMax: number,\n  outputMin: number,\n  outputMax: number,\n  easing: EasingFunction,\n  extrapolateLeft: ExtrapolateType,\n  extrapolateRight: ExtrapolateType,\n  map?: (x: number) => number\n) {\n  let result = map ? map(input) : input\n  // Extrapolate\n  if (result < inputMin) {\n    if (extrapolateLeft === 'identity') return result\n    else if (extrapolateLeft === 'clamp') result = inputMin\n  }\n  if (result > inputMax) {\n    if (extrapolateRight === 'identity') return result\n    else if (extrapolateRight === 'clamp') result = inputMax\n  }\n  if (outputMin === outputMax) return outputMin\n  if (inputMin === inputMax) return input <= inputMin ? outputMin : outputMax\n  // Input Range\n  if (inputMin === -Infinity) result = -result\n  else if (inputMax === Infinity) result = result - inputMin\n  else result = (result - inputMin) / (inputMax - inputMin)\n  // Easing\n  result = easing(result)\n  // Output Range\n  if (outputMin === -Infinity) result = -result\n  else if (outputMax === Infinity) result = result + outputMin\n  else result = result * (outputMax - outputMin) + outputMin\n  return result\n}\n\nfunction findRange(input: number, inputRange: readonly number[]) {\n  // eslint-disable-next-line no-var\n  for (var i = 1; i < inputRange.length - 1; ++i)\n    if (inputRange[i] >= input) break\n  return i - 1\n}\n", "import { EasingFunction } from '@react-spring/types'\n\nimport { clamp } from './clamp'\n\nexport type Direction = 'start' | 'end'\n\ntype StepsEasing = (steps: number, direction?: Direction) => EasingFunction\n\nconst steps: StepsEasing =\n  (steps: number, direction: Direction = 'end') =>\n  (progress: number) => {\n    progress =\n      direction === 'end'\n        ? Math.min(progress, 0.999)\n        : Math.max(progress, 0.001)\n    const expanded = progress * steps\n    const rounded =\n      direction === 'end' ? Math.floor(expanded) : Math.ceil(expanded)\n\n    return clamp(0, 1, rounded / steps)\n  }\n\n/**\n * With thanks to ai easings.net\n * https://github.com/ai/easings.net/blob/master/src/easings/easingsFunctions.ts\n */\ninterface EasingDictionary {\n  linear: (t: number) => number\n  easeInQuad: (t: number) => number\n  easeOutQuad: (t: number) => number\n  easeInOutQuad: (t: number) => number\n  easeInCubic: (t: number) => number\n  easeOutCubic: (t: number) => number\n  easeInOutCubic: (t: number) => number\n  easeInQuart: (t: number) => number\n  easeOutQuart: (t: number) => number\n  easeInOutQuart: (t: number) => number\n  easeInQuint: (t: number) => number\n  easeOutQuint: (t: number) => number\n  easeInOutQuint: (t: number) => number\n  easeInSine: (t: number) => number\n  easeOutSine: (t: number) => number\n  easeInOutSine: (t: number) => number\n  easeInExpo: (t: number) => number\n  easeOutExpo: (t: number) => number\n  easeInOutExpo: (t: number) => number\n  easeInCirc: (t: number) => number\n  easeOutCirc: (t: number) => number\n  easeInOutCirc: (t: number) => number\n  easeInBack: (t: number) => number\n  easeOutBack: (t: number) => number\n  easeInOutBack: (t: number) => number\n  easeInElastic: (t: number) => number\n  easeOutElastic: (t: number) => number\n  easeInOutElastic: (t: number) => number\n  easeInBounce: (t: number) => number\n  easeOutBounce: (t: number) => number\n  easeInOutBounce: (t: number) => number\n  steps: StepsEasing\n}\n\nconst c1 = 1.70158\nconst c2 = c1 * 1.525\nconst c3 = c1 + 1\nconst c4 = (2 * Math.PI) / 3\nconst c5 = (2 * Math.PI) / 4.5\n\nconst bounceOut: EasingFunction = x => {\n  const n1 = 7.5625\n  const d1 = 2.75\n\n  if (x < 1 / d1) {\n    return n1 * x * x\n  } else if (x < 2 / d1) {\n    return n1 * (x -= 1.5 / d1) * x + 0.75\n  } else if (x < 2.5 / d1) {\n    return n1 * (x -= 2.25 / d1) * x + 0.9375\n  } else {\n    return n1 * (x -= 2.625 / d1) * x + 0.984375\n  }\n}\n\nexport const easings: EasingDictionary = {\n  linear: x => x,\n  easeInQuad: x => x * x,\n  easeOutQuad: x => 1 - (1 - x) * (1 - x),\n  easeInOutQuad: x => (x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2),\n  easeInCubic: x => x * x * x,\n  easeOutCubic: x => 1 - Math.pow(1 - x, 3),\n  easeInOutCubic: x =>\n    x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,\n  easeInQuart: x => x * x * x * x,\n  easeOutQuart: x => 1 - Math.pow(1 - x, 4),\n  easeInOutQuart: x =>\n    x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,\n  easeInQuint: x => x * x * x * x * x,\n  easeOutQuint: x => 1 - Math.pow(1 - x, 5),\n  easeInOutQuint: x =>\n    x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,\n  easeInSine: x => 1 - Math.cos((x * Math.PI) / 2),\n  easeOutSine: x => Math.sin((x * Math.PI) / 2),\n  easeInOutSine: x => -(Math.cos(Math.PI * x) - 1) / 2,\n  easeInExpo: x => (x === 0 ? 0 : Math.pow(2, 10 * x - 10)),\n  easeOutExpo: x => (x === 1 ? 1 : 1 - Math.pow(2, -10 * x)),\n  easeInOutExpo: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : x < 0.5\n          ? Math.pow(2, 20 * x - 10) / 2\n          : (2 - Math.pow(2, -20 * x + 10)) / 2,\n  easeInCirc: x => 1 - Math.sqrt(1 - Math.pow(x, 2)),\n  easeOutCirc: x => Math.sqrt(1 - Math.pow(x - 1, 2)),\n  easeInOutCirc: x =>\n    x < 0.5\n      ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2\n      : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,\n  easeInBack: x => c3 * x * x * x - c1 * x * x,\n  easeOutBack: x => 1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),\n  easeInOutBack: x =>\n    x < 0.5\n      ? (Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2)) / 2\n      : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,\n  easeInElastic: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),\n  easeOutElastic: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,\n  easeInOutElastic: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : x < 0.5\n          ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2\n          : (Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5)) / 2 +\n            1,\n  easeInBounce: x => 1 - bounceOut(1 - x),\n  easeOutBounce: bounceOut,\n  easeInOutBounce: x =>\n    x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,\n  steps,\n} as const\n", "/**\n * MIT License\n * Copyright (c) <PERSON>\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\nconst $get = Symbol.for('FluidValue.get')\nconst $observers = Symbol.for('FluidValue.observers')\n\nexport {\n  FluidValue,\n  hasFluidValue,\n  getFluidValue,\n  getFluidObservers,\n  callFluidObserver,\n  callFluidObservers,\n  // Mutations\n  setFluidGetter,\n  addFluidObserver,\n  removeFluidObserver,\n}\n\n/** Returns true if `arg` can be observed. */\nconst hasFluidValue = (arg: any): arg is FluidValue => Boolean(arg && arg[$get])\n\n/**\n * Get the current value.\n * If `arg` is not observable, `arg` is returned.\n */\nconst getFluidValue: GetFluidValue = (arg: any) =>\n  arg && arg[$get] ? arg[$get]() : arg\n\n/** Get the current observer set. Never mutate it directly! */\nconst getFluidObservers: GetFluidObservers = (target: any) =>\n  target[$observers] || null\n\n/** Send an event to an observer. */\nfunction callFluidObserver<E extends FluidEvent>(\n  observer: FluidObserver<E>,\n  event: E\n): void\n\nfunction callFluidObserver(observer: any, event: FluidEvent) {\n  if (observer.eventObserved) {\n    observer.eventObserved(event)\n  } else {\n    observer(event)\n  }\n}\n\n/** Send an event to all observers. */\nfunction callFluidObservers<E extends FluidEvent>(\n  target: FluidValue<any, E>,\n  event: E\n): void\n\nfunction callFluidObservers(target: object, event: FluidEvent): void\n\nfunction callFluidObservers(target: any, event: FluidEvent) {\n  const observers: Set<FluidObserver> = target[$observers]\n  if (observers) {\n    observers.forEach(observer => {\n      callFluidObserver(observer, event)\n    })\n  }\n}\n\ntype GetFluidValue = {\n  <T, U = never>(target: T | FluidValue<U>): Exclude<T, FluidValue> | U\n}\n\ntype GetFluidObservers = {\n  <E extends FluidEvent>(\n    target: FluidValue<any, E>\n  ): ReadonlySet<FluidObserver<E>> | null\n  (target: object): ReadonlySet<FluidObserver> | null\n}\n\n/** An event sent to `FluidObserver` objects. */\nexport interface FluidEvent<T = any> {\n  type: string\n  parent: FluidValue<T>\n}\n\n/**\n * Extend this class for automatic TypeScript support when passing this\n * value to `fluids`-compatible libraries.\n */\nabstract class FluidValue<T = any, E extends FluidEvent<T> = any> {\n  // @ts-expect-error (TS 4.4)\n  private [$get]: () => T\n  // @ts-expect-error (TS 4.4)\n  private [$observers]?: Set<FluidObserver<E>>\n\n  constructor(get?: () => T) {\n    if (!get && !(get = this.get)) {\n      throw Error('Unknown getter')\n    }\n    setFluidGetter(this, get)\n  }\n\n  /** Get the current value. */\n  protected get?(): T\n  /** Called after an observer is added. */\n  protected observerAdded?(count: number, observer: FluidObserver<E>): void\n  /** Called after an observer is removed. */\n  protected observerRemoved?(count: number, observer: FluidObserver<E>): void\n}\n\n/** An observer of `FluidValue` objects. */\nexport type FluidObserver<E extends FluidEvent = any> =\n  | { eventObserved(event: E): void }\n  | { (event: E): void }\n\n/** Add the `FluidValue` type to every property. */\nexport type FluidProps<T> = T extends object\n  ? { [P in keyof T]: T[P] | FluidValue<Exclude<T[P], void>> }\n  : unknown\n\n/** Remove the `FluidValue` type from every property. */\nexport type StaticProps<T extends object> = {\n  [P in keyof T]: T[P] extends FluidValue<infer U> ? U : T[P]\n}\n\n/** Define the getter called by `getFluidValue`. */\nconst setFluidGetter = (target: object, get: () => any) =>\n  setHidden(target, $get, get)\n\n/** Observe a `fluids`-compatible object. */\nfunction addFluidObserver<T, E extends FluidEvent>(\n  target: FluidValue<T, E>,\n  observer: FluidObserver<E>\n): typeof observer\n\nfunction addFluidObserver<E extends FluidEvent>(\n  target: object,\n  observer: FluidObserver<E>\n): typeof observer\n\nfunction addFluidObserver(target: any, observer: FluidObserver) {\n  if (target[$get]) {\n    let observers: Set<FluidObserver> = target[$observers]\n    if (!observers) {\n      setHidden(target, $observers, (observers = new Set()))\n    }\n    if (!observers.has(observer)) {\n      observers.add(observer)\n      if (target.observerAdded) {\n        target.observerAdded(observers.size, observer)\n      }\n    }\n  }\n  return observer\n}\n\n/** Stop observing a `fluids`-compatible object. */\nfunction removeFluidObserver<E extends FluidEvent>(\n  target: FluidValue<any, E>,\n  observer: FluidObserver<E>\n): void\n\nfunction removeFluidObserver<E extends FluidEvent>(\n  target: object,\n  observer: FluidObserver<E>\n): void\n\nfunction removeFluidObserver(target: any, observer: FluidObserver) {\n  const observers: Set<FluidObserver> = target[$observers]\n  if (observers && observers.has(observer)) {\n    const count = observers.size - 1\n    if (count) {\n      observers.delete(observer)\n    } else {\n      target[$observers] = null\n    }\n    if (target.observerRemoved) {\n      target.observerRemoved(count, observer)\n    }\n  }\n}\n\nconst setHidden = (target: any, key: any, value: any) =>\n  Object.defineProperty(target, key, {\n    value,\n    writable: true,\n    configurable: true,\n  })\n", "// Problem: https://github.com/animatedjs/animated/pull/102\n// Solution: https://stackoverflow.com/questions/638565/parsing-scientific-notation-sensibly/658662\nexport const numberRegex = /[+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g\n\n// Covers rgb, rgba, hsl, hsla\n// Taken from https://gist.github.com/olmokramer/82ccce673f86db7cda5e\nexport const colorRegex =\n  /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d\\.]+%?\\))/gi\n\n// Gets numbers with units when specified\nexport const unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, 'i')\n\n// get values of rgba string\nexport const rgbaRegex =\n  /rgba\\(([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+)\\)/gi\n\n/**\n * Parse special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n */\nexport const cssVariableRegex =\n  /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/\n", "import { isSSR } from './helpers'\nimport { cssVariableRegex } from './regexs'\n\n/**\n * takes a CSS variable and attempts\n * to turn it into a RGBA value\n *\n * ```\n * 'var(--white)' => 'rgba(255,255,255,1)'\n * ```\n *\n * @param input string\n * @returns string\n */\nexport const variableToRgba = (input: string): string => {\n  const [token, fallback] = parseCSSVariable(input)\n\n  if (!token || isSSR()) {\n    return input\n  }\n\n  const value = window\n    .getComputedStyle(document.documentElement)\n    .getPropertyValue(token)\n\n  if (value) {\n    /**\n     * We have a valid variable returned\n     * trim and return\n     */\n    return value.trim()\n  } else if (fallback && fallback.startsWith('--')) {\n    /**\n     * fallback is something like --my-variable\n     * so we try get property value\n     */\n    const value = window\n      .getComputedStyle(document.documentElement)\n      .getPropertyValue(fallback)\n\n    /**\n     * if it exists, return else nothing was found so just return the input\n     */\n    if (value) {\n      return value\n    } else {\n      return input\n    }\n  } else if (fallback && cssVariableRegex.test(fallback)) {\n    /**\n     * We have a fallback and it's another CSS variable\n     */\n    return variableToRgba(fallback)\n  } else if (fallback) {\n    /**\n     * We have a fallback and it's not a CSS variable\n     */\n    return fallback\n  }\n\n  /**\n   * Nothing worked so just return the input\n   * like our other FluidValue replace functions do\n   */\n  return input\n}\n\nconst parseCSSVariable = (current: string) => {\n  const match = cssVariableRegex.exec(current)\n  if (!match) return [,]\n\n  const [, token, fallback] = match\n  return [token, fallback]\n}\n", "import { InterpolatorConfig } from '@react-spring/types'\n\nimport { getFluidValue } from './fluids'\nimport { createInterpolator } from './createInterpolator'\nimport { colorToRgba } from './colorToRgba'\nimport * as G from './globals'\nimport {\n  cssVariableRegex,\n  colorRegex,\n  unitRegex,\n  numberRegex,\n  rgbaRegex,\n} from './regexs'\nimport { variableToRgba } from './variableToRgba'\n\n// Covers color names (transparent, blue, etc.)\nlet namedColorRegex: RegExp\n\n// rgba requires that the r,g,b are integers.... so we want to round them,\n// but we *dont* want to round the opacity (4th column).\nconst rgbaRound = (_: any, p1: number, p2: number, p3: number, p4: number) =>\n  `rgba(${Math.round(p1)}, ${Math.round(p2)}, ${Math.round(p3)}, ${p4})`\n\n/**\n * Supports string shapes by extracting numbers so new values can be computed,\n * and recombines those values into new strings of the same shape.  Supports\n * things like:\n *\n *     \"rgba(123, 42, 99, 0.36)\"           // colors\n *     \"-45deg\"                            // values with units\n *     \"0 2px 2px 0px rgba(0, 0, 0, 0.12)\" // CSS box-shadows\n *     \"rotate(0deg) translate(2px, 3px)\"  // CSS transforms\n */\nexport const createStringInterpolator = (\n  config: InterpolatorConfig<string>\n) => {\n  if (!namedColorRegex)\n    namedColorRegex = G.colors\n      ? // match color names, ignore partial matches\n        new RegExp(`(${Object.keys(G.colors).join('|')})(?!\\\\w)`, 'g')\n      : // never match\n        /^\\b$/\n\n  // Convert colors to rgba(...)\n  const output = config.output.map(value => {\n    return getFluidValue(value)\n      .replace(cssVariableRegex, variableToRgba)\n      .replace(colorRegex, colorToRgba)\n      .replace(namedColorRegex, colorToRgba)\n  })\n\n  // Convert [\"1px 2px\", \"0px 0px\"] into [[1, 2], [0, 0]]\n  const keyframes = output.map(value => value.match(numberRegex)!.map(Number))\n\n  // Convert [\"1px 2px\", \"0px 0px\"] into [[1, 0], [2, 0]]\n  const outputRanges = keyframes[0].map((_, i) =>\n    keyframes.map(values => {\n      if (!(i in values)) {\n        throw Error('The arity of each \"output\" value must be equal')\n      }\n      return values[i]\n    })\n  )\n\n  // Create an interpolator for each animated number\n  const interpolators = outputRanges.map(output =>\n    createInterpolator({ ...config, output })\n  )\n\n  // Use the first `output` as a template for each call\n  return (input: number) => {\n    // Convert numbers to units if available (allows for [\"0\", \"100%\"])\n    const missingUnit =\n      !unitRegex.test(output[0]) &&\n      output.find(value => unitRegex.test(value))?.replace(numberRegex, '')\n\n    let i = 0\n    return output[0]\n      .replace(\n        numberRegex,\n        () => `${interpolators[i++](input)}${missingUnit || ''}`\n      )\n      .replace(rgbaRegex, rgbaRound)\n  }\n}\n", "declare const console: any\n\nexport const prefix = 'react-spring: '\n\nexport const once = <TFunc extends (...args: any) => any>(fn: TFunc) => {\n  const func = fn\n  let called = false\n\n  if (typeof func != 'function') {\n    throw new TypeError(`${prefix}once requires a function parameter`)\n  }\n\n  return (...args: any) => {\n    if (!called) {\n      func(...args)\n      called = true\n    }\n  }\n}\n\nconst warnInterpolate = once(console.warn)\nexport function deprecateInterpolate() {\n  warnInterpolate(\n    `${prefix}The \"interpolate\" function is deprecated in v9 (use \"to\" instead)`\n  )\n}\n\nconst warnDirectCall = once(console.warn)\nexport function deprecateDirectCall() {\n  warnDirectCall(\n    `${prefix}Directly calling start instead of using the api object is deprecated in v9 (use \".start\" instead), this will be removed in later 0.X.0 versions`\n  )\n}\n", "import * as G from './globals'\nimport { is, isSSR } from './helpers'\nimport { cssVariableRegex } from './regexs'\n\n// Not all strings can be animated (eg: {display: \"none\"})\nexport function isAnimatedString(value: unknown): value is string {\n  return (\n    is.str(value) &&\n    (value[0] == '#' ||\n      /\\d/.test(value) ||\n      // Do not identify a CSS variable as an AnimatedString if its SSR\n      (!isSSR() && cssVariableRegex.test(value)) ||\n      value in (G.colors || {}))\n  )\n}\n", "import { raf } from '@react-spring/rafz'\nimport { onResize } from '../resize'\n\nimport { ScrollInfo, ScrollHandler } from './ScrollHandler'\n\nexport type OnScrollCallback = (info: ScrollInfo) => void\n\nexport type OnScrollOptions = {\n  /**\n   * The root container to measure against\n   */\n  container?: HTMLElement\n}\n\nconst scrollListeners = new WeakMap<Element, () => boolean>()\nconst resizeListeners = new WeakMap<Element, VoidFunction>()\nconst onScrollHandlers = new WeakMap<Element, Set<ScrollHandler>>()\n\nconst getTarget = (container: HTMLElement) =>\n  container === document.documentElement ? window : container\n\nexport const onScroll = (\n  callback: OnScrollCallback,\n  { container = document.documentElement }: OnScrollOptions = {}\n) => {\n  /**\n   * get the current handlers for the target\n   */\n  let containerHandlers = onScrollHandlers.get(container)\n\n  /**\n   * If there aren't any handlers then create a new set.\n   */\n  if (!containerHandlers) {\n    containerHandlers = new Set()\n    onScrollHandlers.set(container, containerHandlers)\n  }\n\n  /**\n   * Create a new ScrollHandler class and add it to the set.\n   */\n  const containerHandler = new ScrollHandler(callback, container)\n  containerHandlers.add(containerHandler)\n\n  /**\n   * If there are no scrollListeners then we need to make them\n   */\n  if (!scrollListeners.has(container)) {\n    /**\n     * Return true so RAFZ continues to run it\n     */\n    const listener = () => {\n      containerHandlers?.forEach(handler => handler.advance())\n      return true\n    }\n\n    scrollListeners.set(container, listener)\n\n    const target = getTarget(container)\n\n    /**\n     * Add resize handlers so we can correctly calculate the\n     * scroll position on changes\n     */\n    window.addEventListener('resize', listener, { passive: true })\n\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, onResize(listener, { container }))\n    }\n\n    /**\n     * Add the actual scroll listener\n     */\n    target.addEventListener('scroll', listener, { passive: true })\n  }\n\n  /**\n   * Start animation loop\n   */\n  const animateScroll = scrollListeners.get(container)!\n  raf(animateScroll)\n\n  return () => {\n    /**\n     * Clear it on cleanup\n     */\n    raf.cancel(animateScroll)\n\n    /**\n     * Check if we even have any handlers for this container.\n     */\n    const containerHandlers = onScrollHandlers.get(container)\n    if (!containerHandlers) return\n\n    containerHandlers.delete(containerHandler)\n\n    if (containerHandlers.size) return\n\n    /**\n     * If no more handlers, remove the scroll listener too.\n     */\n    const listener = scrollListeners.get(container)\n    scrollListeners.delete(container)\n\n    if (listener) {\n      getTarget(container).removeEventListener('scroll', listener)\n      window.removeEventListener('resize', listener)\n\n      resizeListeners.get(container)?.()\n    }\n  }\n}\n", "import type { OnResizeCallback } from '.'\n\nlet observer: ResizeObserver | undefined\n\nconst resizeHandlers = new WeakMap<Element, Set<OnResizeCallback>>()\n\nconst handleObservation = (entries: ResizeObserverEntry[]) =>\n  entries.forEach(({ target, contentRect }) => {\n    return resizeHandlers.get(target)?.forEach(handler => handler(contentRect))\n  })\n\nexport function resizeElement(handler: OnResizeCallback, target: HTMLElement) {\n  /**\n   * If there's a resize observer in the ENV then use that too.\n   */\n  if (!observer) {\n    if (typeof ResizeObserver !== 'undefined') {\n      observer = new ResizeObserver(handleObservation)\n    }\n  }\n\n  /**\n   * Fetch the handlers for the target\n   */\n  let elementHandlers = resizeHandlers.get(target)\n\n  /**\n   * If there are no handlers create a new set for the target\n   * and then add to the map\n   */\n  if (!elementHandlers) {\n    elementHandlers = new Set()\n    resizeHandlers.set(target, elementHandlers)\n  }\n\n  /**\n   * Add the handler to the target's set\n   * and observe the target if possible.\n   */\n  elementHandlers.add(handler)\n\n  if (observer) {\n    observer.observe(target)\n  }\n\n  /**\n   * Cleanup the event handlers and potential observers.\n   */\n  return () => {\n    const elementHandlers = resizeHandlers.get(target)\n\n    if (!elementHandlers) return\n\n    elementHandlers.delete(handler)\n\n    if (!elementHandlers.size && observer) {\n      observer.unobserve(target)\n    }\n  }\n}\n", "import type { OnResizeCallback } from '.'\n\nconst listeners = new Set<OnResizeCallback>()\n\nlet cleanupWindowResizeHandler: VoidFunction | undefined\n\nconst createResizeHandler = () => {\n  const handleResize = () => {\n    listeners.forEach(callback =>\n      callback({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      })\n    )\n  }\n\n  window.addEventListener('resize', handleResize)\n\n  return () => {\n    window.removeEventListener('resize', handleResize)\n  }\n}\n\nexport const resizeWindow = (callback: OnResizeCallback) => {\n  listeners.add(callback)\n\n  if (!cleanupWindowResizeHandler) {\n    cleanupWindowResizeHandler = createResizeHandler()\n  }\n\n  return () => {\n    listeners.delete(callback)\n\n    if (!listeners.size && cleanupWindowResizeHandler) {\n      cleanupWindowResizeHandler()\n      cleanupWindowResizeHandler = undefined\n    }\n  }\n}\n", "import { resizeElement } from './resizeElement'\nimport { resizeWindow } from './resizeWindow'\n\nexport interface OnResizeOptions {\n  container?: HTMLElement\n}\n\nexport type OnResizeCallback = (\n  rect: Pick<DOMRectReadOnly, 'width' | 'height'> &\n    Partial<Omit<DOMRectReadOnly, 'width' | 'height'>>\n) => void\n\nexport const onResize = (\n  callback: OnResizeCallback,\n  { container = document.documentElement }: OnResizeOptions = {}\n): (() => void) => {\n  if (container === document.documentElement) {\n    return resizeWindow(callback)\n  } else {\n    return resizeElement(callback, container)\n  }\n}\n", "export const progress = (min: number, max: number, value: number) =>\n  max - min === 0 ? 1 : (value - min) / (max - min)\n", "import { progress } from '../../progress'\n\nimport type { OnScrollCallback } from './index'\n\nconst SCROLL_KEYS = {\n  x: {\n    length: 'Width',\n    position: 'Left',\n  },\n  y: {\n    length: 'Height',\n    position: 'Top',\n  },\n} as const\n\n/**\n * Whilst user's may not need the scrollLength, it's easier to return\n * the whole state we're storing and let them pick what they want.\n */\nexport interface ScrollAxis {\n  current: number\n  progress: number\n  scrollLength: number\n}\n\nexport interface ScrollInfo {\n  time: number\n  x: ScrollAxis\n  y: ScrollAxis\n}\n\n/**\n * Why use a class? More extensible in the future.\n */\nexport class ScrollHandler {\n  protected callback: OnScrollCallback\n  protected container: HTMLElement\n  protected info: ScrollInfo\n\n  constructor(callback: OnScrollCallback, container: HTMLElement) {\n    this.callback = callback\n    this.container = container\n\n    this.info = {\n      time: 0,\n      x: this.createAxis(),\n      y: this.createAxis(),\n    }\n  }\n\n  private createAxis = (): ScrollAxis => ({\n    current: 0,\n    progress: 0,\n    scrollLength: 0,\n  })\n\n  private updateAxis = (axisName: keyof Pick<ScrollInfo, 'x' | 'y'>) => {\n    const axis = this.info[axisName]\n    const { length, position } = SCROLL_KEYS[axisName]\n\n    axis.current = this.container[`scroll${position}`]\n    axis.scrollLength =\n      this.container[`scroll${length}`] - this.container[`client${length}`]\n\n    axis.progress = progress(0, axis.scrollLength, axis.current)\n  }\n\n  private update = () => {\n    this.updateAxis('x')\n    this.updateAxis('y')\n  }\n\n  private sendEvent = () => {\n    this.callback(this.info)\n  }\n\n  advance = () => {\n    this.update()\n    this.sendEvent()\n  }\n}\n", "import { useRef } from 'react'\n\ntype Init<T> = () => T\n\n/**\n * Creates a constant value over the lifecycle of a component.\n */\nexport function useConstant<T>(init: Init<T>) {\n  const ref = useRef<T | null>(null)\n\n  if (ref.current === null) {\n    ref.current = init()\n  }\n\n  return ref.current\n}\n", "import { useState } from 'react'\nimport { useIsMounted } from './useIsMounted'\n\n/** Return a function that re-renders this component, if still mounted */\nexport function useForceUpdate() {\n  const update = useState<any>()[1]\n  const isMounted = useIsMounted()\n  return () => {\n    if (isMounted.current) {\n      update(Math.random())\n    }\n  }\n}\n", "import { useRef } from 'react'\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect'\n\nexport const useIsMounted = () => {\n  const isMounted = useRef(false)\n  useIsomorphicLayoutEffect(() => {\n    isMounted.current = true\n\n    return () => {\n      isMounted.current = false\n    }\n  }, [])\n\n  return isMounted\n}\n", "import { useEffect, useLayoutEffect } from 'react'\n\nimport { isSSR } from '../helpers'\n\n/**\n * Use this to read layout from the DOM and synchronously\n * re-render if the isSSR returns true. Updates scheduled\n * inside `useIsomorphicLayoutEffect` will be flushed\n * synchronously in the browser, before the browser has\n * a chance to paint.\n */\nexport const useIsomorphicLayoutEffect = isSSR() ? useEffect : useLayoutEffect\n", "import { useEffect, useRef, useState } from 'react'\n\ntype Cache<T> = {\n  inputs?: any[]\n  result?: T\n}\n\n// TODO: remove once merged (https://github.com/alexreardon/use-memo-one/pull/10)\nexport function useMemoOne<T>(getResult: () => T, inputs?: any[]): T {\n  const [initial] = useState(\n    (): Cache<T> => ({\n      inputs,\n      result: getResult(),\n    })\n  )\n\n  const committed = useRef<Cache<T>>()\n  const prevCache = committed.current\n\n  let cache = prevCache\n  if (cache) {\n    const useCache = Boolean(\n      inputs && cache.inputs && areInputsEqual(inputs, cache.inputs)\n    )\n    if (!useCache) {\n      cache = {\n        inputs,\n        result: getResult(),\n      }\n    }\n  } else {\n    cache = initial\n  }\n\n  useEffect(() => {\n    committed.current = cache\n    if (prevCache == initial) {\n      initial.inputs = initial.result = undefined\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [cache])\n\n  return cache.result!\n}\n\nfunction areInputsEqual(next: any[], prev: any[]) {\n  if (next.length !== prev.length) {\n    return false\n  }\n  for (let i = 0; i < next.length; i++) {\n    if (next[i] !== prev[i]) {\n      return false\n    }\n  }\n  return true\n}\n", "/* eslint-disable react-hooks/exhaustive-deps */\nimport { useEffect, EffectCallback } from 'react'\n\nexport const useOnce = (effect: EffectCallback) => useEffect(effect, emptyDeps)\n\nconst emptyDeps: any[] = []\n", "import { useEffect, useRef } from 'react'\n\n/** Use a value from the previous render */\nexport function usePrev<T>(value: T): T | undefined {\n  const prevRef = useRef<any>()\n  useEffect(() => {\n    prevRef.current = value\n  })\n  return prevRef.current\n}\n", "import { useState } from 'react'\n\nimport { assign } from '../globals'\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect'\n\n/**\n * Returns `boolean` or `null`, used to automatically\n * set skipAnimations to the value of the user's\n * `prefers-reduced-motion` query.\n *\n * The return value, post-effect, is the value of their prefered setting\n */\nexport const useReducedMotion = () => {\n  const [reducedMotion, setReducedMotion] = useState<boolean | null>(null)\n\n  useIsomorphicLayoutEffect(() => {\n    const mql = window.matchMedia('(prefers-reduced-motion)')\n\n    const handleMediaChange = (e: MediaQueryListEvent | MediaQueryList) => {\n      setReducedMotion(e.matches)\n\n      assign({\n        skipAnimation: e.matches,\n      })\n    }\n\n    handleMediaChange(mql)\n\n    if (mql.addEventListener) {\n      mql.addEventListener('change', handleMediaChange)\n    } else {\n      mql.addListener(handleMediaChange)\n    }\n\n    return () => {\n      if (mql.removeEventListener) {\n        mql.removeEventListener('change', handleMediaChange)\n      } else {\n        mql.removeListener(handleMediaChange)\n      }\n    }\n  }, [])\n\n  return reducedMotion\n}\n", "import * as Globals from './globals'\nexport { Globals }\n\nexport * from './FrameLoop'\nexport * from './clamp'\nexport * from './colors'\nexport * from './colorToRgba'\nexport * from './colorMatchers'\nexport * from './createInterpolator'\nexport * from './easings'\nexport * from './stringInterpolation'\nexport * from './deprecations'\nexport * from './helpers'\nexport * from './isAnimatedString'\n/**\n * Should these be moved to a DOM only\n * package to avoid native issues?\n */\nexport * from './dom-events/scroll'\nexport * from './dom-events/resize'\n\nexport * from './hooks/useConstant'\nexport * from './hooks/useForceUpdate'\nexport * from './hooks/useMemoOne'\nexport * from './hooks/useOnce'\nexport * from './hooks/usePrev'\nexport * from './hooks/useIsomorphicLayoutEffect'\nexport * from './hooks/useReducedMotion'\n\nexport * from './fluids'\n\nexport { raf } from '@react-spring/rafz'\nexport type { Timeout } from '@react-spring/rafz'\n"], "mappings": "6FAAA,IAAAA,EAAA,GAAAC,GAAAD,EAAA,YAAAE,EAAA,WAAAC,EAAA,6BAAAC,EAAA,kBAAAC,GAAA,OAAAC,EAAA,gBAAAC,IAAA,OAAS,OAAAC,MAAiB,qBCGnB,SAASC,GAAO,CAAC,CAEjB,IAAMC,GAAe,CAACC,EAAUC,EAAUC,IAC/C,OAAO,eAAeF,EAAKC,EAAK,CAAE,MAAAC,EAAO,SAAU,GAAM,aAAc,EAAK,CAAC,EAOlEC,EAAK,CAChB,IAAK,MAAM,QACX,IAASC,GACP,CAAC,CAACA,GAAKA,EAAE,YAAY,OAAS,SAChC,IAAOA,GAAe,OAAOA,GAAM,WACnC,IAAMA,GAA4B,OAAOA,GAAM,SAC/C,IAAMA,GAA4B,OAAOA,GAAM,SAC/C,IAAMA,GAA+BA,IAAM,MAC7C,EAGO,SAASC,GAAQD,EAAQE,EAAQ,CACtC,GAAIH,EAAG,IAAIC,CAAC,EAAG,CACb,GAAI,CAACD,EAAG,IAAIG,CAAC,GAAKF,EAAE,SAAWE,EAAE,OAAQ,MAAO,GAChD,QAASC,EAAI,EAAGA,EAAIH,EAAE,OAAQG,IAC5B,GAAIH,EAAEG,CAAC,IAAMD,EAAEC,CAAC,EAAG,MAAO,GAE5B,MAAO,GAET,OAAOH,IAAME,CACf,CAQO,IAAME,GAAO,CAClBR,EACAS,IACGT,EAAI,QAAQS,CAAE,EAGZ,SAASC,GACdV,EACAS,EAKAE,EACA,CACA,GAAIR,EAAG,IAAIH,CAAG,EAAG,CACf,QAASO,EAAI,EAAGA,EAAIP,EAAI,OAAQO,IAC9BE,EAAG,KAAKE,EAAYX,EAAIO,CAAC,EAAU,GAAGA,GAAG,EAE3C,OAEF,QAAWN,KAAOD,EACZA,EAAI,eAAeC,CAAG,GACxBQ,EAAG,KAAKE,EAAYX,EAAIC,CAAG,EAAUA,CAAG,CAG9C,CAEO,IAAMW,GAAcR,GACzBD,EAAG,IAAIC,CAAC,EAAI,CAAC,EAAID,EAAG,IAAIC,CAAC,EAAKA,EAAY,CAACA,CAAC,EAQvC,SAASS,GAAMC,EAAYC,EAAe,CAC/C,GAAID,EAAM,KAAM,CACd,IAAME,EAAQ,MAAM,KAAKF,CAAK,EAC9BA,EAAM,MAAM,EACZN,GAAKQ,EAAOD,CAAQ,EAExB,CAGO,IAAME,GAAa,CACxBH,KACGI,IACAL,GAAMC,EAAOL,GAAMA,EAAG,GAAGS,CAAI,CAAC,EAKtBC,EAAQ,IACnB,OAAO,OAAW,KAClB,CAAC,OAAO,WACR,8BAA8B,KAAK,OAAO,UAAU,SAAS,EDnFxD,IAAIC,EAQAC,EAKAC,EAAS,KAETC,GAAgB,GAEhBC,EAAoDC,EA2BlDC,EAAUC,GAA6B,CAC9CA,EAAQ,KAAIN,EAAKM,EAAQ,IACzBA,EAAQ,MAAKC,EAAI,IAAMD,EAAQ,KAC/BA,EAAQ,SAAW,SAAWL,EAASK,EAAQ,QAC/CA,EAAQ,eAAiB,OAAMJ,GAAgBI,EAAQ,eACvDA,EAAQ,2BACVP,EAA2BO,EAAQ,0BACjCA,EAAQ,uBAAuBC,EAAI,IAAID,EAAQ,qBAAqB,EACpEA,EAAQ,iBAAgBC,EAAI,eAAiBD,EAAQ,gBACrDA,EAAQ,cAAaH,EAAcG,EAAQ,aAC3CA,EAAQ,YAAWC,EAAI,UAAYD,EAAQ,UACjD,EEtEA,OAAS,OAAAE,MAAW,qBAUpB,IAAMC,EAAa,IAAI,IAInBC,EAAkC,CAAC,EACnCC,EAA+B,CAAC,EAMhCC,EAAW,EAMFC,GAAY,CACvB,IAAI,MAAO,CACT,MAAO,CAACJ,EAAW,MAAQ,CAACC,EAAa,MAC3C,EAGA,MAAMI,EAA4B,CAG5BF,EAAWE,EAAU,UACvBL,EAAW,IAAIK,CAAS,EACxBC,EAAI,QAAQC,EAAe,IAE3BC,GAAYH,CAAS,EACrBC,EAAIG,CAAO,EAEf,EAGA,QAAAA,EAGA,KAAKJ,EAA4B,CAC/B,GAAIF,EACFG,EAAI,QAAQ,IAAMF,GAAU,KAAKC,CAAS,CAAC,MACtC,CACL,IAAMK,EAAYT,EAAa,QAAQI,CAAS,EAC5C,CAACK,IACHT,EAAa,OAAOS,EAAW,CAAC,EAChCC,GAAcN,CAAS,GAG7B,EAOA,OAAQ,CACNJ,EAAe,CAAC,EAChBD,EAAW,MAAM,CACnB,CACF,EAEA,SAASO,IAAkB,CACzBP,EAAW,QAAQQ,EAAW,EAC9BR,EAAW,MAAM,EACjBM,EAAIG,CAAO,CACb,CAEA,SAASD,GAAYH,EAA4B,CAC1CJ,EAAa,SAASI,CAAS,GAAGM,GAAcN,CAAS,CAChE,CAEA,SAASM,GAAcN,EAA4B,CACjDJ,EAAa,OACXW,GAAUX,EAAcY,GAASA,EAAM,SAAWR,EAAU,QAAQ,EACpE,EACAA,CACF,CACF,CAEA,SAASI,EAAQK,EAAY,CAC3B,IAAMC,EAAYb,EAElB,QAASc,EAAI,EAAGA,EAAIf,EAAa,OAAQe,IAAK,CAC5C,IAAMX,EAAYJ,EAAae,CAAC,EAChCb,EAAWE,EAAU,SAGhBA,EAAU,OACXY,EAAYZ,CAAS,EACvBA,EAAU,QAAQS,CAAE,EACfT,EAAU,MACbU,EAAU,KAAKV,CAAS,GAI9B,OAAAF,EAAW,EAGXD,EAAYD,EACZC,EAAU,OAAS,EAInBD,EAAec,EAERd,EAAa,OAAS,CAC/B,CAGA,SAASW,GAAaM,EAAUC,EAA6B,CAC3D,IAAMC,EAAQF,EAAI,UAAUC,CAAI,EAChC,OAAOC,EAAQ,EAAIF,EAAI,OAASE,CAClC,CC3HO,IAAMC,GAAQ,CAACC,EAAaC,EAAaC,IAC9C,KAAK,IAAI,KAAK,IAAIA,EAAGF,CAAG,EAAGC,CAAG,ECEzB,IAAME,GAAS,CACpB,YAAa,EACb,UAAW,WACX,aAAc,WACd,KAAM,SACN,WAAY,WACZ,MAAO,WACP,MAAO,WACP,OAAQ,WACR,MAAO,IACP,eAAgB,WAChB,KAAM,MACN,WAAY,WACZ,MAAO,WACP,UAAW,WACX,YAAa,WACb,UAAW,WACX,WAAY,WACZ,UAAW,WACX,MAAO,WACP,eAAgB,WAChB,SAAU,WACV,QAAS,WACT,KAAM,SACN,SAAU,MACV,SAAU,QACV,cAAe,WACf,SAAU,WACV,UAAW,QACX,SAAU,WACV,UAAW,WACX,YAAa,WACb,eAAgB,WAChB,WAAY,WACZ,WAAY,WACZ,QAAS,WACT,WAAY,WACZ,aAAc,WACd,cAAe,WACf,cAAe,UACf,cAAe,UACf,cAAe,SACf,WAAY,WACZ,SAAU,WACV,YAAa,SACb,QAAS,WACT,QAAS,WACT,WAAY,UACZ,UAAW,WACX,YAAa,WACb,YAAa,UACb,QAAS,WACT,UAAW,WACX,WAAY,WACZ,KAAM,WACN,UAAW,WACX,KAAM,WACN,MAAO,QACP,YAAa,WACb,KAAM,WACN,SAAU,WACV,QAAS,WACT,UAAW,WACX,OAAQ,WACR,MAAO,WACP,MAAO,WACP,SAAU,WACV,cAAe,WACf,UAAW,WACX,aAAc,WACd,UAAW,WACX,WAAY,WACZ,UAAW,WACX,qBAAsB,WACtB,UAAW,WACX,WAAY,WACZ,UAAW,WACX,UAAW,WACX,YAAa,WACb,cAAe,UACf,aAAc,WACd,eAAgB,WAChB,eAAgB,WAChB,eAAgB,WAChB,YAAa,WACb,KAAM,SACN,UAAW,UACX,MAAO,WACP,QAAS,WACT,OAAQ,WACR,iBAAkB,WAClB,WAAY,MACZ,aAAc,WACd,aAAc,WACd,eAAgB,WAChB,gBAAiB,WACjB,kBAAmB,SACnB,gBAAiB,WACjB,gBAAiB,WACjB,aAAc,UACd,UAAW,WACX,UAAW,WACX,SAAU,WACV,YAAa,WACb,KAAM,MACN,QAAS,WACT,MAAO,WACP,UAAW,WACX,OAAQ,WACR,UAAW,WACX,OAAQ,WACR,cAAe,WACf,UAAW,WACX,cAAe,WACf,cAAe,WACf,WAAY,WACZ,UAAW,WACX,KAAM,WACN,KAAM,WACN,KAAM,WACN,WAAY,WACZ,OAAQ,WACR,cAAe,WACf,IAAK,WACL,UAAW,WACX,UAAW,WACX,YAAa,WACb,OAAQ,WACR,WAAY,WACZ,SAAU,UACV,SAAU,WACV,OAAQ,WACR,OAAQ,WACR,QAAS,WACT,UAAW,WACX,UAAW,WACX,UAAW,WACX,KAAM,WACN,YAAa,SACb,UAAW,WACX,IAAK,WACL,KAAM,QACN,QAAS,WACT,OAAQ,WACR,UAAW,WACX,OAAQ,WACR,MAAO,WACP,MAAO,WACP,WAAY,WACZ,OAAQ,WACR,YAAa,UACf,ECzJA,IAAMC,EAAS,oBACTC,EAAaD,EAAS,IAE5B,SAASE,KAAQC,EAAiB,CAChC,MAAO,WAAaA,EAAM,KAAK,aAAa,EAAI,UAClD,CAEO,IAAMC,GAAM,IAAI,OAAO,MAAQF,EAAKF,EAAQA,EAAQA,CAAM,CAAC,EACrDK,GAAO,IAAI,OAAO,OAASH,EAAKF,EAAQA,EAAQA,EAAQA,CAAM,CAAC,EAC/DM,GAAM,IAAI,OAAO,MAAQJ,EAAKF,EAAQC,EAAYA,CAAU,CAAC,EAC7DM,GAAO,IAAI,OACtB,OAASL,EAAKF,EAAQC,EAAYA,EAAYD,CAAM,CACtD,EACaQ,GAAO,sDACPC,GACX,sEACWC,GAAO,sBACPC,GAAO,sBCmBb,SAASC,GAAeC,EAAwB,CACrD,IAAIC,EAEJ,OAAI,OAAOD,GAAU,SACZA,IAAU,IAAMA,GAASA,GAAS,GAAKA,GAAS,WACnDA,EACA,MAIDC,EAAiBC,GAAK,KAAKF,CAAK,GAC5B,SAASC,EAAM,CAAC,EAAI,KAAM,EAAE,IAAM,EAErCE,GAAYA,EAAOH,CAAK,IAAM,OACzBG,EAAOH,CAAK,GAGlBC,EAAiBG,GAAI,KAAKJ,CAAK,IAE9BK,EAASJ,EAAM,CAAC,CAAC,GAAK,GACrBI,EAASJ,EAAM,CAAC,CAAC,GAAK,GACtBI,EAASJ,EAAM,CAAC,CAAC,GAAK,EACvB,OACF,GAICA,EAAiBK,GAAK,KAAKN,CAAK,IAE/BK,EAASJ,EAAM,CAAC,CAAC,GAAK,GACrBI,EAASJ,EAAM,CAAC,CAAC,GAAK,GACtBI,EAASJ,EAAM,CAAC,CAAC,GAAK,EACvBM,GAAON,EAAM,CAAC,CAAC,KACjB,GAICA,EAAiBO,GAAK,KAAKR,CAAK,GAEjC,SACEC,EAAM,CAAC,EACLA,EAAM,CAAC,EACPA,EAAM,CAAC,EACPA,EAAM,CAAC,EACPA,EAAM,CAAC,EACPA,EAAM,CAAC,EACP,KACF,EACF,IAAM,GAKLA,EAAiBQ,GAAK,KAAKT,CAAK,GAAW,SAASC,EAAM,CAAC,EAAG,EAAE,IAAM,GAEtEA,EAAiBS,GAAK,KAAKV,CAAK,GAEjC,SACEC,EAAM,CAAC,EACLA,EAAM,CAAC,EACPA,EAAM,CAAC,EACPA,EAAM,CAAC,EACPA,EAAM,CAAC,EACPA,EAAM,CAAC,EACPA,EAAM,CAAC,EACPA,EAAM,CAAC,EACT,EACF,IAAM,GAILA,EAAiBU,GAAI,KAAKX,CAAK,IAE/BY,GACCC,GAASZ,EAAM,CAAC,CAAC,EACjBa,EAAgBb,EAAM,CAAC,CAAC,EACxBa,EAAgBb,EAAM,CAAC,CAAC,CAC1B,EACE,OACF,GAICA,EAAiBc,GAAK,KAAKf,CAAK,IAEhCY,GACCC,GAASZ,EAAM,CAAC,CAAC,EACjBa,EAAgBb,EAAM,CAAC,CAAC,EACxBa,EAAgBb,EAAM,CAAC,CAAC,CAC1B,EACEM,GAAON,EAAM,CAAC,CAAC,KACjB,EAGG,IACT,CAEA,SAASe,EAAQC,EAAWC,EAAWC,EAAW,CAGhD,OAFIA,EAAI,IAAGA,GAAK,GACZA,EAAI,IAAGA,GAAK,GACZA,EAAI,EAAI,EAAUF,GAAKC,EAAID,GAAK,EAAIE,EACpCA,EAAI,EAAI,EAAUD,EAClBC,EAAI,EAAI,EAAUF,GAAKC,EAAID,IAAM,EAAI,EAAIE,GAAK,EAC3CF,CACT,CAEA,SAASL,GAASQ,EAAWC,EAAWC,EAAW,CACjD,IAAMJ,EAAII,EAAI,GAAMA,GAAK,EAAID,GAAKC,EAAID,EAAIC,EAAID,EACxCJ,EAAI,EAAIK,EAAIJ,EACZK,EAAIP,EAAQC,EAAGC,EAAGE,EAAI,EAAI,CAAC,EAC3BI,EAAIR,EAAQC,EAAGC,EAAGE,CAAC,EACnBK,EAAIT,EAAQC,EAAGC,EAAGE,EAAI,EAAI,CAAC,EACjC,OACG,KAAK,MAAMG,EAAI,GAAG,GAAK,GACvB,KAAK,MAAMC,EAAI,GAAG,GAAK,GACvB,KAAK,MAAMC,EAAI,GAAG,GAAK,CAE5B,CAEA,SAASpB,EAASqB,EAAa,CAC7B,IAAMC,EAAM,SAASD,EAAK,EAAE,EAC5B,OAAIC,EAAM,EAAU,EAChBA,EAAM,IAAY,IACfA,CACT,CAEA,SAASd,GAASa,EAAa,CAE7B,OADY,WAAWA,CAAG,EACV,IAAO,KAAO,IAAO,GACvC,CAEA,SAASnB,GAAOmB,EAAa,CAC3B,IAAME,EAAM,WAAWF,CAAG,EAC1B,OAAIE,EAAM,EAAU,EAChBA,EAAM,EAAU,IACb,KAAK,MAAMA,EAAM,GAAG,CAC7B,CAEA,SAASd,EAAgBY,EAAa,CAEpC,IAAMC,EAAM,WAAWD,CAAG,EAC1B,OAAIC,EAAM,EAAU,EAChBA,EAAM,IAAY,EACfA,EAAM,GACf,CCnLO,SAASE,EAAYC,EAAe,CACzC,IAAIC,EAAaC,GAAeF,CAAK,EACrC,GAAIC,IAAe,KAAM,OAAOD,EAChCC,EAAaA,GAAc,EAC3B,IAAM,GAAKA,EAAa,cAAgB,GAClCE,GAAKF,EAAa,YAAgB,GAClCG,GAAKH,EAAa,SAAgB,EAClCI,GAAKJ,EAAa,KAAc,IACtC,MAAO,QAAQ,MAAME,MAAMC,MAAMC,IACnC,CCAO,IAAMC,EAA0C,CACrDC,EACAC,EACAC,IACG,CACH,GAAIC,EAAG,IAAIH,CAAK,EACd,OAAOA,EAGT,GAAIG,EAAG,IAAIH,CAAK,EACd,OAAOD,EAAmB,CACxB,MAAAC,EACA,OAAQC,EACR,YAAAC,CACF,CAAC,EAGH,GAAIC,EAAG,IAAIH,EAAM,OAAO,CAAC,CAAC,EACxB,OAASI,EAAyBJ,CAAY,EAGhD,IAAMK,EAASL,EACTM,EAAcD,EAAO,OACrBE,EAAaF,EAAO,OAAS,CAAC,EAAG,CAAC,EAElCG,EACJH,EAAO,iBAAmBA,EAAO,aAAe,SAC5CI,EACJJ,EAAO,kBAAoBA,EAAO,aAAe,SAC7CK,EAASL,EAAO,SAAWM,GAAKA,GAEtC,OAAQC,GAAkB,CACxB,IAAMZ,EAAQa,GAAUD,EAAOL,CAAU,EACzC,OAAOO,GACLF,EACAL,EAAWP,CAAK,EAChBO,EAAWP,EAAQ,CAAC,EACpBM,EAAYN,CAAK,EACjBM,EAAYN,EAAQ,CAAC,EACrBU,EACAF,EACAC,EACAJ,EAAO,GACT,CACF,CACF,EAEA,SAASS,GACPF,EACAG,EACAC,EACAC,EACAC,EACAR,EACAF,EACAC,EACAU,EACA,CACA,IAAIC,EAASD,EAAMA,EAAIP,CAAK,EAAIA,EAEhC,GAAIQ,EAASL,EAAU,CACrB,GAAIP,IAAoB,WAAY,OAAOY,EAClCZ,IAAoB,UAASY,EAASL,GAEjD,GAAIK,EAASJ,EAAU,CACrB,GAAIP,IAAqB,WAAY,OAAOW,EACnCX,IAAqB,UAASW,EAASJ,GAElD,OAAIC,IAAcC,EAAkBD,EAChCF,IAAaC,EAAiBJ,GAASG,EAAWE,EAAYC,GAE9DH,IAAa,KAAWK,EAAS,CAACA,EAC7BJ,IAAa,IAAUI,EAASA,EAASL,EAC7CK,GAAUA,EAASL,IAAaC,EAAWD,GAEhDK,EAASV,EAAOU,CAAM,EAElBH,IAAc,KAAWG,EAAS,CAACA,EAC9BF,IAAc,IAAUE,EAASA,EAASH,EAC9CG,EAASA,GAAUF,EAAYD,GAAaA,EAC1CG,EACT,CAEA,SAASP,GAAUD,EAAeL,EAA+B,CAE/D,QAASc,EAAI,EAAGA,EAAId,EAAW,OAAS,GAClC,EAAAA,EAAWc,CAAC,GAAKT,GADoB,EAAES,EAC3C,CACF,OAAOA,EAAI,CACb,CC3FA,IAAMC,GACJ,CAACA,EAAeC,EAAuB,QACtCC,GAAqB,CACpBA,EACED,IAAc,MACV,KAAK,IAAIC,EAAU,IAAK,EACxB,KAAK,IAAIA,EAAU,IAAK,EAC9B,IAAMC,EAAWD,EAAWF,EACtBI,EACJH,IAAc,MAAQ,KAAK,MAAME,CAAQ,EAAI,KAAK,KAAKA,CAAQ,EAEjE,OAAOE,GAAM,EAAG,EAAGD,EAAUJ,CAAK,CACpC,EAyCIM,EAAK,QACLC,EAAKD,EAAK,MACVE,GAAKF,EAAK,EACVG,GAAM,EAAI,KAAK,GAAM,EACrBC,GAAM,EAAI,KAAK,GAAM,IAErBC,EAA4BC,GAI5BA,EAAI,EAAI,KACH,OAAKA,EAAIA,EACPA,EAAI,EAAI,KACV,QAAMA,GAAK,IAAM,MAAMA,EAAI,IACzBA,EAAI,IAAM,KACZ,QAAMA,GAAK,KAAO,MAAMA,EAAI,MAE5B,QAAMA,GAAK,MAAQ,MAAMA,EAAI,QAI3BC,GAA4B,CACvC,OAAQD,GAAKA,EACb,WAAYA,GAAKA,EAAIA,EACrB,YAAaA,GAAK,GAAK,EAAIA,IAAM,EAAIA,GACrC,cAAeA,GAAMA,EAAI,GAAM,EAAIA,EAAIA,EAAI,EAAI,KAAK,IAAI,GAAKA,EAAI,EAAG,CAAC,EAAI,EACzE,YAAaA,GAAKA,EAAIA,EAAIA,EAC1B,aAAcA,GAAK,EAAI,KAAK,IAAI,EAAIA,EAAG,CAAC,EACxC,eAAgBA,GACdA,EAAI,GAAM,EAAIA,EAAIA,EAAIA,EAAI,EAAI,KAAK,IAAI,GAAKA,EAAI,EAAG,CAAC,EAAI,EAC1D,YAAaA,GAAKA,EAAIA,EAAIA,EAAIA,EAC9B,aAAcA,GAAK,EAAI,KAAK,IAAI,EAAIA,EAAG,CAAC,EACxC,eAAgBA,GACdA,EAAI,GAAM,EAAIA,EAAIA,EAAIA,EAAIA,EAAI,EAAI,KAAK,IAAI,GAAKA,EAAI,EAAG,CAAC,EAAI,EAC9D,YAAaA,GAAKA,EAAIA,EAAIA,EAAIA,EAAIA,EAClC,aAAcA,GAAK,EAAI,KAAK,IAAI,EAAIA,EAAG,CAAC,EACxC,eAAgBA,GACdA,EAAI,GAAM,GAAKA,EAAIA,EAAIA,EAAIA,EAAIA,EAAI,EAAI,KAAK,IAAI,GAAKA,EAAI,EAAG,CAAC,EAAI,EACnE,WAAYA,GAAK,EAAI,KAAK,IAAKA,EAAI,KAAK,GAAM,CAAC,EAC/C,YAAaA,GAAK,KAAK,IAAKA,EAAI,KAAK,GAAM,CAAC,EAC5C,cAAeA,GAAK,EAAE,KAAK,IAAI,KAAK,GAAKA,CAAC,EAAI,GAAK,EACnD,WAAYA,GAAMA,IAAM,EAAI,EAAI,KAAK,IAAI,EAAG,GAAKA,EAAI,EAAE,EACvD,YAAaA,GAAMA,IAAM,EAAI,EAAI,EAAI,KAAK,IAAI,EAAG,IAAMA,CAAC,EACxD,cAAeA,GACbA,IAAM,EACF,EACAA,IAAM,EACJ,EACAA,EAAI,GACF,KAAK,IAAI,EAAG,GAAKA,EAAI,EAAE,EAAI,GAC1B,EAAI,KAAK,IAAI,EAAG,IAAMA,EAAI,EAAE,GAAK,EAC5C,WAAYA,GAAK,EAAI,KAAK,KAAK,EAAI,KAAK,IAAIA,EAAG,CAAC,CAAC,EACjD,YAAaA,GAAK,KAAK,KAAK,EAAI,KAAK,IAAIA,EAAI,EAAG,CAAC,CAAC,EAClD,cAAeA,GACbA,EAAI,IACC,EAAI,KAAK,KAAK,EAAI,KAAK,IAAI,EAAIA,EAAG,CAAC,CAAC,GAAK,GACzC,KAAK,KAAK,EAAI,KAAK,IAAI,GAAKA,EAAI,EAAG,CAAC,CAAC,EAAI,GAAK,EACrD,WAAYA,GAAKJ,GAAKI,EAAIA,EAAIA,EAAIN,EAAKM,EAAIA,EAC3C,YAAaA,GAAK,EAAIJ,GAAK,KAAK,IAAII,EAAI,EAAG,CAAC,EAAIN,EAAK,KAAK,IAAIM,EAAI,EAAG,CAAC,EACtE,cAAeA,GACbA,EAAI,GACC,KAAK,IAAI,EAAIA,EAAG,CAAC,IAAML,EAAK,GAAK,EAAIK,EAAIL,GAAO,GAChD,KAAK,IAAI,EAAIK,EAAI,EAAG,CAAC,IAAML,EAAK,IAAMK,EAAI,EAAI,GAAKL,GAAM,GAAK,EACrE,cAAeK,GACbA,IAAM,EACF,EACAA,IAAM,EACJ,EACA,CAAC,KAAK,IAAI,EAAG,GAAKA,EAAI,EAAE,EAAI,KAAK,KAAKA,EAAI,GAAK,OAASH,EAAE,EAClE,eAAgBG,GACdA,IAAM,EACF,EACAA,IAAM,EACJ,EACA,KAAK,IAAI,EAAG,IAAMA,CAAC,EAAI,KAAK,KAAKA,EAAI,GAAK,KAAQH,EAAE,EAAI,EAChE,iBAAkBG,GAChBA,IAAM,EACF,EACAA,IAAM,EACJ,EACAA,EAAI,GACF,EAAE,KAAK,IAAI,EAAG,GAAKA,EAAI,EAAE,EAAI,KAAK,KAAK,GAAKA,EAAI,QAAUF,EAAE,GAAK,EAChE,KAAK,IAAI,EAAG,IAAME,EAAI,EAAE,EAAI,KAAK,KAAK,GAAKA,EAAI,QAAUF,EAAE,EAAK,EACjE,EACV,aAAcE,GAAK,EAAID,EAAU,EAAIC,CAAC,EACtC,cAAeD,EACf,gBAAiBC,GACfA,EAAI,IAAO,EAAID,EAAU,EAAI,EAAIC,CAAC,GAAK,GAAK,EAAID,EAAU,EAAIC,EAAI,CAAC,GAAK,EAC1E,MAAAZ,EACF,EChIA,IAAMc,EAAO,OAAO,IAAI,gBAAgB,EAClCC,EAAa,OAAO,IAAI,sBAAsB,EAgBpD,IAAMC,GAAiBC,GAAgC,GAAQA,GAAOA,EAAIC,CAAI,GAMxEC,GAAgCF,GACpCA,GAAOA,EAAIC,CAAI,EAAID,EAAIC,CAAI,EAAE,EAAID,EAG7BG,GAAwCC,GAC5CA,EAAOC,CAAU,GAAK,KAQxB,SAASC,GAAkBC,EAAeC,EAAmB,CACvDD,EAAS,cACXA,EAAS,cAAcC,CAAK,EAE5BD,EAASC,CAAK,CAElB,CAUA,SAASC,GAAmBL,EAAaI,EAAmB,CAC1D,IAAME,EAAgCN,EAAOC,CAAU,EACnDK,GACFA,EAAU,QAAQH,GAAY,CAC5BD,GAAkBC,EAAUC,CAAK,CACnC,CAAC,CAEL,CAuBA,IAAeG,GAAf,KAAkE,CAMhE,YAAYC,EAAe,CACzB,GAAI,CAACA,GAAO,EAAEA,EAAM,KAAK,KACvB,MAAM,MAAM,gBAAgB,EAE9BC,GAAe,KAAMD,CAAG,CAC1B,CAQF,EAjBWX,EAEAI,EAiCX,IAAMQ,GAAiB,CAACT,EAAgBQ,IACtCE,GAAUV,EAAQH,EAAMW,CAAG,EAa7B,SAASG,GAAiBX,EAAaG,EAAyB,CAC9D,GAAIH,EAAOH,CAAI,EAAG,CAChB,IAAIS,EAAgCN,EAAOC,CAAU,EAChDK,GACHI,GAAUV,EAAQC,EAAaK,EAAY,IAAI,GAAM,EAElDA,EAAU,IAAIH,CAAQ,IACzBG,EAAU,IAAIH,CAAQ,EAClBH,EAAO,eACTA,EAAO,cAAcM,EAAU,KAAMH,CAAQ,GAInD,OAAOA,CACT,CAaA,SAASS,GAAoBZ,EAAaG,EAAyB,CACjE,IAAMG,EAAgCN,EAAOC,CAAU,EACvD,GAAIK,GAAaA,EAAU,IAAIH,CAAQ,EAAG,CACxC,IAAMU,EAAQP,EAAU,KAAO,EAC3BO,EACFP,EAAU,OAAOH,CAAQ,EAEzBH,EAAOC,CAAU,EAAI,KAEnBD,EAAO,iBACTA,EAAO,gBAAgBa,EAAOV,CAAQ,EAG5C,CAEA,IAAMO,GAAY,CAACV,EAAac,EAAUC,IACxC,OAAO,eAAef,EAAQc,EAAK,CACjC,MAAAC,EACA,SAAU,GACV,aAAc,EAChB,CAAC,ECxMI,IAAMC,EAAc,oDAIdC,GACX,2FAGWC,EAAY,IAAI,OAAO,IAAIF,EAAY,oBAAqB,GAAG,EAG/DG,GACX,+DAUWC,EACX,uDCXK,IAAMC,EAAkBC,GAA0B,CACvD,GAAM,CAACC,EAAOC,CAAQ,EAAIC,GAAiBH,CAAK,EAEhD,GAAI,CAACC,GAASG,EAAM,EAClB,OAAOJ,EAGT,IAAMK,EAAQ,OACX,iBAAiB,SAAS,eAAe,EACzC,iBAAiBJ,CAAK,EAEzB,GAAII,EAKF,OAAOA,EAAM,KAAK,EACb,GAAIH,GAAYA,EAAS,WAAW,IAAI,EAAG,CAKhD,IAAMG,EAAQ,OACX,iBAAiB,SAAS,eAAe,EACzC,iBAAiBH,CAAQ,EAK5B,OAAIG,GAGKL,MAEJ,IAAIE,GAAYI,EAAiB,KAAKJ,CAAQ,EAInD,OAAOH,EAAeG,CAAQ,EACzB,GAAIA,EAIT,OAAOA,EAOT,OAAOF,CACT,EAEMG,GAAoBI,GAAoB,CAC5C,IAAMC,EAAQF,EAAiB,KAAKC,CAAO,EAC3C,GAAI,CAACC,EAAO,MAAO,CAAC,CAAC,EAErB,GAAM,CAAC,CAAEP,EAAOC,CAAQ,EAAIM,EAC5B,MAAO,CAACP,EAAOC,CAAQ,CACzB,ECzDA,IAAIO,EAIEC,GAAY,CAACC,EAAQC,EAAYC,EAAYC,EAAYC,IAC7D,QAAQ,KAAK,MAAMH,CAAE,MAAM,KAAK,MAAMC,CAAE,MAAM,KAAK,MAAMC,CAAE,MAAMC,KAYtDC,GACXC,GACG,CACER,IACHA,EAAoBS,EAEhB,IAAI,OAAO,IAAI,OAAO,KAAOA,CAAM,EAAE,KAAK,GAAG,YAAa,GAAG,EAE7D,QAGN,IAAMC,EAASF,EAAO,OAAO,IAAIG,GACxBC,GAAcD,CAAK,EACvB,QAAQE,EAAkBC,CAAc,EACxC,QAAQC,GAAYC,CAAW,EAC/B,QAAQhB,EAAiBgB,CAAW,CACxC,EAGKC,EAAYP,EAAO,IAAIC,GAASA,EAAM,MAAMO,CAAW,EAAG,IAAI,MAAM,CAAC,EAarEC,EAVeF,EAAU,CAAC,EAAE,IAAI,CAACf,EAAG,IACxCe,EAAU,IAAIG,GAAU,CACtB,GAAI,EAAE,KAAKA,GACT,MAAM,MAAM,gDAAgD,EAE9D,OAAOA,EAAO,CAAC,CACjB,CAAC,CACH,EAGmC,IAAIV,GACrCW,EAAmB,CAAE,GAAGb,EAAQ,OAAAE,CAAO,CAAC,CAC1C,EAGA,OAAQY,GAAkB,CAExB,IAAMC,EACJ,CAACC,EAAU,KAAKd,EAAO,CAAC,CAAC,GACzBA,EAAO,KAAKC,GAASa,EAAU,KAAKb,CAAK,CAAC,GAAG,QAAQO,EAAa,EAAE,EAElEO,EAAI,EACR,OAAOf,EAAO,CAAC,EACZ,QACCQ,EACA,IAAM,GAAGC,EAAcM,GAAG,EAAEH,CAAK,IAAIC,GAAe,IACtD,EACC,QAAQG,GAAWzB,EAAS,CACjC,CACF,EClFO,IAAM0B,EAAS,iBAETC,GAA6CC,GAAc,CACtE,IAAMC,EAAOD,EACTE,EAAS,GAEb,GAAI,OAAOD,GAAQ,WACjB,MAAM,IAAI,UAAU,GAAGH,qCAA0C,EAGnE,MAAO,IAAIK,IAAc,CAClBD,IACHD,EAAK,GAAGE,CAAI,EACZD,EAAS,GAEb,CACF,EAEME,GAAkBL,GAAK,QAAQ,IAAI,EAClC,SAASM,IAAuB,CACrCD,GACE,GAAGN,oEACL,CACF,CAEA,IAAMQ,GAAiBP,GAAK,QAAQ,IAAI,EACjC,SAASQ,IAAsB,CACpCD,GACE,GAAGR,kJACL,CACF,CC3BO,SAASU,GAAiBC,EAAiC,CAChE,OACEC,EAAG,IAAID,CAAK,IACXA,EAAM,CAAC,GAAK,KACX,KAAK,KAAKA,CAAK,GAEd,CAACE,EAAM,GAAKC,EAAiB,KAAKH,CAAK,GACxCA,KAAYI,GAAU,CAAC,GAE7B,CCdA,OAAS,OAAAC,OAAW,qBCEpB,IAAIC,EAEEC,EAAiB,IAAI,QAErBC,GAAqBC,GACzBA,EAAQ,QAAQ,CAAC,CAAE,OAAAC,EAAQ,YAAAC,CAAY,IAC9BJ,EAAe,IAAIG,CAAM,GAAG,QAAQE,GAAWA,EAAQD,CAAW,CAAC,CAC3E,EAEI,SAASE,GAAcD,EAA2BF,EAAqB,CAIvEJ,GACC,OAAO,eAAmB,MAC5BA,EAAW,IAAI,eAAeE,EAAiB,GAOnD,IAAIM,EAAkBP,EAAe,IAAIG,CAAM,EAM/C,OAAKI,IACHA,EAAkB,IAAI,IACtBP,EAAe,IAAIG,EAAQI,CAAe,GAO5CA,EAAgB,IAAIF,CAAO,EAEvBN,GACFA,EAAS,QAAQI,CAAM,EAMlB,IAAM,CACX,IAAMI,EAAkBP,EAAe,IAAIG,CAAM,EAE5CI,IAELA,EAAgB,OAAOF,CAAO,EAE1B,CAACE,EAAgB,MAAQR,GAC3BA,EAAS,UAAUI,CAAM,EAE7B,CACF,CCzDA,IAAMK,EAAY,IAAI,IAElBC,EAEEC,GAAsB,IAAM,CAChC,IAAMC,EAAe,IAAM,CACzBH,EAAU,QAAQI,GAChBA,EAAS,CACP,MAAO,OAAO,WACd,OAAQ,OAAO,WACjB,CAAC,CACH,CACF,EAEA,cAAO,iBAAiB,SAAUD,CAAY,EAEvC,IAAM,CACX,OAAO,oBAAoB,SAAUA,CAAY,CACnD,CACF,EAEaE,GAAgBD,IAC3BJ,EAAU,IAAII,CAAQ,EAEjBH,IACHA,EAA6BC,GAAoB,GAG5C,IAAM,CACXF,EAAU,OAAOI,CAAQ,EAErB,CAACJ,EAAU,MAAQC,IACrBA,EAA2B,EAC3BA,EAA6B,OAEjC,GCzBK,IAAMK,GAAW,CACtBC,EACA,CAAE,UAAAC,EAAY,SAAS,eAAgB,EAAqB,CAAC,IAEzDA,IAAc,SAAS,gBAClBC,GAAaF,CAAQ,EAErBG,GAAcH,EAAUC,CAAS,ECnBrC,IAAMG,GAAW,CAACC,EAAaC,EAAaC,IACjDD,EAAMD,IAAQ,EAAI,GAAKE,EAAQF,IAAQC,EAAMD,GCG/C,IAAMG,GAAc,CAClB,EAAG,CACD,OAAQ,QACR,SAAU,MACZ,EACA,EAAG,CACD,OAAQ,SACR,SAAU,KACZ,CACF,EAqBaC,EAAN,KAAoB,CAKzB,YAAYC,EAA4BC,EAAwB,CAWhE,KAAQ,WAAa,KAAmB,CACtC,QAAS,EACT,SAAU,EACV,aAAc,CAChB,GAEA,KAAQ,WAAcC,GAAgD,CACpE,IAAMC,EAAO,KAAK,KAAKD,CAAQ,EACzB,CAAE,OAAAE,EAAQ,SAAAC,CAAS,EAAIP,GAAYI,CAAQ,EAEjDC,EAAK,QAAU,KAAK,UAAU,SAASE,GAAU,EACjDF,EAAK,aACH,KAAK,UAAU,SAASC,GAAQ,EAAI,KAAK,UAAU,SAASA,GAAQ,EAEtED,EAAK,SAAWG,GAAS,EAAGH,EAAK,aAAcA,EAAK,OAAO,CAC7D,EAEA,KAAQ,OAAS,IAAM,CACrB,KAAK,WAAW,GAAG,EACnB,KAAK,WAAW,GAAG,CACrB,EAEA,KAAQ,UAAY,IAAM,CACxB,KAAK,SAAS,KAAK,IAAI,CACzB,EAEA,aAAU,IAAM,CACd,KAAK,OAAO,EACZ,KAAK,UAAU,CACjB,EAvCE,KAAK,SAAWH,EAChB,KAAK,UAAYC,EAEjB,KAAK,KAAO,CACV,KAAM,EACN,EAAG,KAAK,WAAW,EACnB,EAAG,KAAK,WAAW,CACrB,CACF,CAgCF,ELlEA,IAAMM,EAAkB,IAAI,QACtBC,GAAkB,IAAI,QACtBC,EAAmB,IAAI,QAEvBC,GAAaC,GACjBA,IAAc,SAAS,gBAAkB,OAASA,EAEvCC,GAAW,CACtBC,EACA,CAAE,UAAAF,EAAY,SAAS,eAAgB,EAAqB,CAAC,IAC1D,CAIH,IAAIG,EAAoBL,EAAiB,IAAIE,CAAS,EAKjDG,IACHA,EAAoB,IAAI,IACxBL,EAAiB,IAAIE,EAAWG,CAAiB,GAMnD,IAAMC,EAAmB,IAAIC,EAAcH,EAAUF,CAAS,EAM9D,GALAG,EAAkB,IAAIC,CAAgB,EAKlC,CAACR,EAAgB,IAAII,CAAS,EAAG,CAInC,IAAMM,EAAW,KACfH,GAAmB,QAAQI,GAAWA,EAAQ,QAAQ,CAAC,EAChD,IAGTX,EAAgB,IAAII,EAAWM,CAAQ,EAEvC,IAAME,EAAST,GAAUC,CAAS,EAMlC,OAAO,iBAAiB,SAAUM,EAAU,CAAE,QAAS,EAAK,CAAC,EAEzDN,IAAc,SAAS,iBACzBH,GAAgB,IAAIG,EAAWS,GAASH,EAAU,CAAE,UAAAN,CAAU,CAAC,CAAC,EAMlEQ,EAAO,iBAAiB,SAAUF,EAAU,CAAE,QAAS,EAAK,CAAC,EAM/D,IAAMI,EAAgBd,EAAgB,IAAII,CAAS,EACnD,OAAAW,GAAID,CAAa,EAEV,IAAM,CAIXC,GAAI,OAAOD,CAAa,EAKxB,IAAMP,EAAoBL,EAAiB,IAAIE,CAAS,EAKxD,GAJI,CAACG,IAELA,EAAkB,OAAOC,CAAgB,EAErCD,EAAkB,MAAM,OAK5B,IAAMG,EAAWV,EAAgB,IAAII,CAAS,EAC9CJ,EAAgB,OAAOI,CAAS,EAE5BM,IACFP,GAAUC,CAAS,EAAE,oBAAoB,SAAUM,CAAQ,EAC3D,OAAO,oBAAoB,SAAUA,CAAQ,EAE7CT,GAAgB,IAAIG,CAAS,IAAI,EAErC,CACF,EM/GA,OAAS,UAAAY,OAAc,QAOhB,SAASC,GAAeC,EAAe,CAC5C,IAAMC,EAAMH,GAAiB,IAAI,EAEjC,OAAIG,EAAI,UAAY,OAClBA,EAAI,QAAUD,EAAK,GAGdC,EAAI,OACb,CCfA,OAAS,YAAAC,OAAgB,QCAzB,OAAS,UAAAC,OAAc,QCAvB,OAAS,aAAAC,GAAW,mBAAAC,OAAuB,QAWpC,IAAMC,EAA4BC,EAAM,EAAIC,GAAYC,GDRxD,IAAMC,GAAe,IAAM,CAChC,IAAMC,EAAYC,GAAO,EAAK,EAC9B,OAAAC,EAA0B,KACxBF,EAAU,QAAU,GAEb,IAAM,CACXA,EAAU,QAAU,EACtB,GACC,CAAC,CAAC,EAEEA,CACT,EDVO,SAASG,IAAiB,CAC/B,IAAMC,EAASC,GAAc,EAAE,CAAC,EAC1BC,EAAYC,GAAa,EAC/B,MAAO,IAAM,CACPD,EAAU,SACZF,EAAO,KAAK,OAAO,CAAC,CAExB,CACF,CGZA,OAAS,aAAAI,GAAW,UAAAC,GAAQ,YAAAC,OAAgB,QAQrC,SAASC,GAAcC,EAAoBC,EAAmB,CACnE,GAAM,CAACC,CAAO,EAAIJ,GAChB,KAAiB,CACf,OAAAG,EACA,OAAQD,EAAU,CACpB,EACF,EAEMG,EAAYN,GAAiB,EAC7BO,EAAYD,EAAU,QAExBE,EAAQD,EACZ,OAAIC,EAEAJ,GAAUI,EAAM,QAAUC,GAAeL,EAAQI,EAAM,MAAM,IAG7DA,EAAQ,CACN,OAAAJ,EACA,OAAQD,EAAU,CACpB,GAGFK,EAAQH,EAGVN,GAAU,IAAM,CACdO,EAAU,QAAUE,EAChBD,GAAaF,IACfA,EAAQ,OAASA,EAAQ,OAAS,OAGtC,EAAG,CAACG,CAAK,CAAC,EAEHA,EAAM,MACf,CAEA,SAASC,GAAeC,EAAaC,EAAa,CAChD,GAAID,EAAK,SAAWC,EAAK,OACvB,MAAO,GAET,QAASC,EAAI,EAAGA,EAAIF,EAAK,OAAQE,IAC/B,GAAIF,EAAKE,CAAC,IAAMD,EAAKC,CAAC,EACpB,MAAO,GAGX,MAAO,EACT,CCtDA,OAAS,aAAAC,OAAiC,QAEnC,IAAMC,GAAWC,GAA2BF,GAAUE,EAAQC,EAAS,EAExEA,GAAmB,CAAC,ECL1B,OAAS,aAAAC,GAAW,UAAAC,OAAc,QAG3B,SAASC,GAAWC,EAAyB,CAClD,IAAMC,EAAUH,GAAY,EAC5B,OAAAD,GAAU,IAAM,CACdI,EAAQ,QAAUD,CACpB,CAAC,EACMC,EAAQ,OACjB,CCTA,OAAS,YAAAC,OAAgB,QAYlB,IAAMC,GAAmB,IAAM,CACpC,GAAM,CAACC,EAAeC,CAAgB,EAAIC,GAAyB,IAAI,EAEvE,OAAAC,EAA0B,IAAM,CAC9B,IAAMC,EAAM,OAAO,WAAW,0BAA0B,EAElDC,EAAqBC,GAA4C,CACrEL,EAAiBK,EAAE,OAAO,EAE1BC,EAAO,CACL,cAAeD,EAAE,OACnB,CAAC,CACH,EAEA,OAAAD,EAAkBD,CAAG,EAEjBA,EAAI,iBACNA,EAAI,iBAAiB,SAAUC,CAAiB,EAEhDD,EAAI,YAAYC,CAAiB,EAG5B,IAAM,CACPD,EAAI,oBACNA,EAAI,oBAAoB,SAAUC,CAAiB,EAEnDD,EAAI,eAAeC,CAAiB,CAExC,CACF,EAAG,CAAC,CAAC,EAEEL,CACT,ECbA,OAAS,OAAAQ,OAAW", "names": ["globals_exports", "__export", "assign", "colors", "createStringInterpolator", "skipAnimation", "to", "willAdvance", "raf", "noop", "defineHidden", "obj", "key", "value", "is", "a", "isEqual", "b", "i", "each", "fn", "eachProp", "ctx", "toArray", "flush", "queue", "iterator", "items", "flushCalls", "args", "isSSR", "createStringInterpolator", "to", "colors", "skipAnimation", "willAdvance", "noop", "assign", "globals", "raf", "raf", "startQueue", "currentFrame", "prevFrame", "priority", "frameLoop", "animation", "raf", "flushStartQueue", "startSafely", "advance", "prevIndex", "startUnsafely", "findIndex", "other", "dt", "next<PERSON><PERSON><PERSON>", "i", "willAdvance", "arr", "test", "index", "clamp", "min", "max", "v", "colors", "NUMBER", "PERCENTAGE", "call", "parts", "rgb", "rgba", "hsl", "hsla", "hex3", "hex4", "hex6", "hex8", "normalizeColor", "color", "match", "hex6", "colors", "rgb", "parse255", "rgba", "parse1", "hex3", "hex8", "hex4", "hsl", "hslToRgb", "parse360", "parsePercentage", "hsla", "hue2rgb", "p", "q", "t", "h", "s", "l", "r", "g", "b", "str", "int", "num", "colorToRgba", "input", "int32Color", "normalizeColor", "g", "b", "a", "createInterpolator", "range", "output", "extrapolate", "is", "createStringInterpolator", "config", "outputRange", "inputRange", "extrapolateLeft", "extrapolateRight", "easing", "t", "input", "find<PERSON><PERSON><PERSON>", "interpolate", "inputMin", "inputMax", "outputMin", "outputMax", "map", "result", "i", "steps", "direction", "progress", "expanded", "rounded", "clamp", "c1", "c2", "c3", "c4", "c5", "bounceOut", "x", "easings", "$get", "$observers", "hasFluidValue", "arg", "$get", "getFluidValue", "getFluidObservers", "target", "$observers", "callFluidObserver", "observer", "event", "callFluidObservers", "observers", "FluidValue", "get", "setFluidGetter", "setHidden", "addFluidObserver", "removeFluidObserver", "count", "key", "value", "numberRegex", "colorRegex", "unitRegex", "rgbaRegex", "cssVariableRegex", "variableToRgba", "input", "token", "fallback", "parseCSSVariable", "isSSR", "value", "cssVariableRegex", "current", "match", "namedColorRegex", "rgbaRound", "_", "p1", "p2", "p3", "p4", "createStringInterpolator", "config", "colors", "output", "value", "getFluidValue", "cssVariableRegex", "variableToRgba", "colorRegex", "colorToRgba", "keyframes", "numberRegex", "interpolators", "values", "createInterpolator", "input", "missing<PERSON><PERSON><PERSON>", "unitRegex", "i", "rgbaRegex", "prefix", "once", "fn", "func", "called", "args", "warnInterpolate", "deprecateInterpolate", "warnDirectCall", "deprecateDirectCall", "isAnimatedString", "value", "is", "isSSR", "cssVariableRegex", "colors", "raf", "observer", "resizeHandlers", "handleObservation", "entries", "target", "contentRect", "handler", "resizeElement", "elementHandlers", "listeners", "cleanupWindowResizeHandler", "createResizeHandler", "handleResize", "callback", "resizeWindow", "onResize", "callback", "container", "resizeWindow", "resizeElement", "progress", "min", "max", "value", "SCROLL_KEYS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callback", "container", "axisName", "axis", "length", "position", "progress", "scrollListeners", "resizeListeners", "onScrollHandlers", "get<PERSON><PERSON><PERSON>", "container", "onScroll", "callback", "containerHandlers", "containerHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listener", "handler", "target", "onResize", "animateScroll", "raf", "useRef", "useConstant", "init", "ref", "useState", "useRef", "useEffect", "useLayoutEffect", "useIsomorphicLayoutEffect", "isSSR", "useEffect", "useLayoutEffect", "useIsMounted", "isMounted", "useRef", "useIsomorphicLayoutEffect", "useForceUpdate", "update", "useState", "isMounted", "useIsMounted", "useEffect", "useRef", "useState", "useMemoOne", "getResult", "inputs", "initial", "committed", "prevCache", "cache", "areInputsEqual", "next", "prev", "i", "useEffect", "useOnce", "effect", "emptyDeps", "useEffect", "useRef", "usePrev", "value", "prevRef", "useState", "useReducedMotion", "reducedMotion", "setReducedMotion", "useState", "useIsomorphicLayoutEffect", "mql", "handleMediaChange", "e", "assign", "raf"]}