{"version": 3, "file": "sem.js", "sourceRoot": "", "sources": ["../src/sem.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EACN,KAAK,EACL,UAAU,EACV,KAAK,EACL,OAAO,EACP,OAAO,EACP,QAAQ,EACR,iBAAiB,EACjB,UAAU,EACV,SAAS,EACT,KAAK,EACL,OAAO,EACP,aAAa,GACb,MAAM,OAAO,CAAC;AAEf,OAAO,EAAE,aAAa,EAAE,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;AAEtE,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAEtD,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAGvC,MAAM,aAAa,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAE5C,MAAM,OAAO,0BAA2B,SAAQ,WAAW;IAuB1D,YAAoB,QAAkB;QACrC,KAAK,EAAE,CAAC;QADW,aAAQ,GAAR,QAAQ,CAAU;QAtBtB,kBAAa,GAAqB,IAAI,GAAG,EAAE,CAAC;QAC5C,kBAAa,GAAoB,IAAI,GAAG,EAAE,CAAC;QAC3C,YAAO,GAAG,OAAO,CAAC;QAK1B,cAAS,GAA+B,IAAI,GAAG,EAAE,CAAC;QAClD,WAAM,GAAG,IAAI,KAAK,EAAE,CAAC;QACrB,UAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QACpB,WAAM,GAAG,IAAI,KAAK,EAAE,CAAC;QACrB,iBAAY,GAAG,IAAI,OAAO,EAAE,CAAC;QAC7B,mBAAc,GAAG,IAAI,UAAU,EAAE,CAAC;QAClC,kBAAa,GAAG,IAAI,OAAO,EAAE,CAAC;QAC9B,cAAS,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,eAAU,GAAG,IAAI,OAAO,EAAE,CAAC;QAC3B,cAAS,GAAG,IAAI,SAAS,EAAE,CAAC;QAC5B,kBAAa,GAAG,IAAI,KAAK,EAAE,CAAC;QAC5B,kBAAa,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC/B,gBAAW,GAAG,IAAI,OAAO,EAAE,CAAC;QAC5B,iBAAY,GAAG,IAAI,OAAO,EAAE,CAAC;QAIpC,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,GAAG,IAAI,iBAAiB,CAClC,EAAE,EACF,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,WAAW,EACtC,GAAG,EACH,EAAE,CACF,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEpC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAE3B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;QAE3B,IAAI,CAAC,QAAQ,GAAG,IAAI,aAAa,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACrD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;QAClD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC;QAC5C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC;QAC7C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,GAAG,uBAAuB,CAAC;QAEnE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,IAAI,iBAAiB;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;IACjC,CAAC;IAED,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAI,aAAa,CAAC,OAAgB;QACjC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,CAAC;IAED,IAAI,oBAAoB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC3B,CAAC;IAED,IAAI,oBAAoB,CAAC,OAAgB;QACxC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,CAAC;IAED,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAI,aAAa,CAAC,OAAgB;QACjC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,CAAC;IAED,MAAM;QACL,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACtD,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;QACzD,IAAI,uBAAuB,GAAG,KAAK,CAAC;QACpC,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,WAAW,CAAC;YAC9B,uBAAuB,GAAG,IAAI,CAAC;QAChC,CAAC;QACD,MAAM,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAC3D,IAAI,mBAAmB,EAAE,CAAC;YACzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YACxC,MAAM,YAAY,GACjB,MAAM,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK;gBAC1C,MAAM,CAAC,MAAM,KAAK,mBAAmB,CAAC,MAAM,CAAC;YAC9C,IAAI,YAAY,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,MAAM;oBACjB,mBAAmB,CAAC,KAAK,GAAG,mBAAmB,CAAC,MAAM,CAAC;gBACxD,IAAI,CAAC,QAAQ,CAAC,OAAO,CACpB,mBAAmB,CAAC,KAAK,EACzB,mBAAmB,CAAC,MAAM,CAC1B,CAAC;gBACF,uBAAuB,GAAG,IAAI,CAAC;YAChC,CAAC;QACF,CAAC;QACD,IAAI,uBAAuB,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,SAAS;QACR,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC/C,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IAED,eAAe,CAAC,IAAS;QACxB,IAAI,CAAC,SAAS,EAAE,CAAC;QAChB,IAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,EAAE;YACjE,MAAM,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAClE,IAAI,aAAa,EAAE,CAAC;gBACnB,QAAQ,aAAa,CAAC,UAAU,EAAE,CAAC;oBAClC,KAAK,iBAAiB,CAAC,GAAG;wBACzB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;wBAC9B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,YAA0B,CAAC,CAAC;wBACjE,MAAM;oBACP,KAAK,iBAAiB,CAAC,KAAK;wBAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;wBAC/B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,YAA2B,CAAC,CAAC;wBAClE,MAAM;oBACP,KAAK,iBAAiB,CAAC,IAAI;wBAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;wBAC/B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,YAA0B,CAAC,CAAC;wBACjE,MAAM;gBACR,CAAC;gBACD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAC3D,IAAI,OAAO,EAAE,CAAC;oBACb,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,CAAC;gBACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAC3D,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,sBAAsB,CAAC,KAAa;QACnC,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,UAAU,EAAE,CAAC;YACrD,iDAAiD;YACjD,MAAM,GAAG,GAAG,mCAAmC,OAAO,aAAa,KAAK,OAAO,CAAC;YAChF,KAAK,CAAC,GAAG,CAAC;iBACR,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAClB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBAClB,MAAM,IAAI,KAAK,CACd,gCAAgC,QAAQ,CAAC,UAAU,EAAE,CACrD,CAAC;gBACH,CAAC;gBACD,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxB,CAAC,CAAC;iBACD,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;gBACjB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAC/B,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBAChB,OAAO,CAAC,KAAK,CAAC,6BAA6B,KAAK,WAAW,EAAE,KAAK,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACP,mCAAmC;YACnC,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,SAAS,EAAE,CAAC;gBAChB,OAAO,CAAC,KAAK,CAAC,yBAAyB,KAAK,iBAAiB,CAAC,CAAC;gBAC/D,OAAO;YACR,CAAC;YACD,SAAS,EAAE;iBACT,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBAChB,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;gBAC/B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAC/B,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBAChB,OAAO,CAAC,KAAK,CAAC,6BAA6B,KAAK,UAAU,EAAE,KAAK,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;QACL,CAAC;IACF,CAAC;IAED,qBAAqB,CAAC,IAAU;QAC/B,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,SAAS,CACxB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,SAAS,CACd,CAAC;QAEF,IAAI,CAAC,aAAa;aAChB,IAAI,CAAC,aAAa,CAAC;aACnB,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC;aACpC,SAAS,EAAE,CAAC;QACd,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1D,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxE,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;;YAClD,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,MAAA,YAAY,CAAC,IAAI,0CAAE,MAAM,EAAE,CAAC;gBAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChD,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;gBACnC,IAAI,CAAC,WAAW;qBACd,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;qBACnE,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,aAAa,CAAC,MAAM,CACxB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CACrD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACP,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/C,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IAChB,CAAC;CACD"}