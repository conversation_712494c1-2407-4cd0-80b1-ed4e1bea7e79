import { Vector2 } from 'three';
import { SpatialEntityComponent, SpatialEntityComponentType } from './component.js';
import { Rect2D } from '../../generated/protos/openxr_core.js';
import { SpatialEntity } from '../entity.js';
export declare class Bounded2DComponent extends SpatialEntityComponent {
    private _offset;
    private _extent;
    type: SpatialEntityComponentType;
    constructor(spatialEntity: SpatialEntity, initData: Rect2D);
    buildGeometry(): void;
    get offset(): Vector2;
    get extent(): Vector2;
    get initData(): {
        offset: Vector2;
        extent: Vector2;
    };
    get pbData(): Rect2D;
}
//# sourceMappingURL=bounded2d.d.ts.map