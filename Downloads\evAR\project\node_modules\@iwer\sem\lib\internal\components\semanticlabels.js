/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { SemanticLabelJSON, } from '../description.js';
import { SpatialEntityComponent, SpatialEntityComponentType, } from './component.js';
function convertToReadableString(str) {
    return str.toLowerCase().replace(/_/g, ' ').trim();
}
export class SemanticLabelsComponent extends SpatialEntityComponent {
    constructor(spatialEntity, initData) {
        super(spatialEntity);
        this.type = SpatialEntityComponentType.SemanticLabels;
        this._semanticLabel = initData[0];
        this._spatialEntity.name = convertToReadableString(initData[0]);
    }
    get semanticLabel() {
        return this._semanticLabel;
    }
    set semanticLabel(value) {
        if (Object.values(SemanticLabelJSON).includes(value)) {
            this._semanticLabel = value;
        }
        else {
            this._semanticLabel = SemanticLabelJSON.OTHER;
        }
        this._spatialEntity.name = convertToReadableString(this._semanticLabel);
    }
    get initData() {
        return [this._semanticLabel];
    }
    toJSON() {
        return {
            semanticLabels: this.initData,
        };
    }
}
//# sourceMappingURL=semanticlabels.js.map