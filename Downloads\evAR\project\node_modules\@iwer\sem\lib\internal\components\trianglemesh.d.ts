/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Mesh } from 'three';
import { SpatialEntityComponent, SpatialEntityComponentType } from './component.js';
import { TriangleMeshComponentJSON, TriangleMeshJSON, Vector3JSON } from '../description.js';
export declare class TriangleMeshComponent extends SpatialEntityComponent {
    private _vertices;
    private _indices;
    private _polygonCount;
    private _vertexCount;
    private _dimensions;
    type: SpatialEntityComponentType;
    constructor(spatialEntity: Mesh, initData: TriangleMeshJSON);
    private buildGeometry;
    get vertexCount(): number;
    get polygonCount(): number;
    get dimensions(): Vector3JSON;
    get initData(): {
        vertices: Vector3JSON[];
        indices: number[];
    };
    toJSON(): TriangleMeshComponentJSON;
}
//# sourceMappingURL=trianglemesh.d.ts.map