# `typescript-eslint`

> Tooling which enables you to use TypeScript with ESLint

[![NPM Version](https://img.shields.io/npm/v/typescript-eslint.svg?style=flat-square)](https://www.npmjs.com/package/typescript-eslint)
[![NPM Downloads](https://img.shields.io/npm/dm/typescript-eslint.svg?style=flat-square)](https://www.npmjs.com/package/typescript-eslint)

👉 See **https://typescript-eslint.io/packages/typescript-eslint** for documentation on this package.

> See https://typescript-eslint.io for general documentation on typescript-eslint, the tooling that allows you to run ESLint and Prettier on TypeScript code.

<!-- Local path for docs: docs/packages/typescript-eslint.mdx -->
