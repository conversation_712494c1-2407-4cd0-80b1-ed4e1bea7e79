{"version": 3, "file": "scene.js", "sourceRoot": "", "sources": ["../src/scene.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EACN,WAAW,EACX,SAAS,EACT,KAAK,EACL,IAAI,EACJ,iBAAiB,EACjB,iBAAiB,EACjB,UAAU,EACV,YAAY,EACZ,KAAK,EACL,OAAO,EACP,aAAa,GACb,MAAM,OAAO,CAAC;AAEf,OAAO,EAAE,iBAAiB,EAAE,MAAM,kDAAkD,CAAC;AAGrF,MAAM,OAAO,GAAG,MAAM,CAAC,0BAA0B,CAAC,CAAC;AACnD,MAAM,mBAAmB,GAAG,KAAK,CAAC;AAElC,MAAM,OAAO,UAAU;IAyBtB,YAAY,QAAkB;QAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;QACjD,iBAAiB;QACjB,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QAE1B,kBAAkB;QAClB,MAAM,MAAM,GAAG,IAAI,iBAAiB,CACnC,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,EAC/B,eAAe,CAAC,WAAW,GAAG,eAAe,CAAC,YAAY,EAC1D,GAAG,EACH,IAAI,CACJ,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC;QAC9B,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC;QAC9B,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACrB,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACzB,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACrD,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACzD,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACtB,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;QAEtC,MAAM,uBAAuB,GAAG,IAAI,IAAI,CAAC,IAAI,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACzE,MAAM,wBAAwB,GAAG,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACjE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CACzC,QAAQ,CAAC,WAAW,CAAC,IAAK,CAAC,QAAQ,CAAC,IAAI,CACxC,CAAC;QACF,uBAAuB,CAAC,UAAU,CAAC,SAAS,CAC3C,QAAQ,CAAC,WAAW,CAAC,IAAK,CAAC,UAAU,CAAC,IAAI,CAC1C,CAAC;QACF,wBAAwB,CAAC,QAAQ,CAAC,SAAS,CAC1C,QAAQ,CAAC,WAAW,CAAC,KAAM,CAAC,QAAQ,CAAC,IAAI,CACzC,CAAC;QACF,wBAAwB,CAAC,UAAU,CAAC,SAAS,CAC5C,QAAQ,CAAC,WAAW,CAAC,KAAM,CAAC,UAAU,CAAC,IAAI,CAC3C,CAAC;QACF,SAAS,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;QAC1C,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;QAC3C,uBAAuB,CAAC,OAAO,GAAG,KAAK,CAAC;QACxC,wBAAwB,CAAC,OAAO,GAAG,KAAK,CAAC;QAEzC,MAAM,oBAAoB,GAAG,IAAI,IAAI,CACpC,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,EAChC,IAAI,iBAAiB,CAAC;YACrB,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE,SAAS;SACf,CAAC,CACF,CAAC;QACF,oBAAoB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3C,KAAK,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAEhC,oBAAoB;QACpB,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACpD,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE,eAAe,CAAC,YAAY,CAAC,CAAC;QAC5E,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QACpC,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAEjD,MAAM,qBAAqB,GAAG,IAAI,iBAAiB,CAClD,MAAM,EACN,QAAQ,CAAC,UAAU,CACnB,CAAC;QACF,qBAAqB,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;QACtD,KAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAEjC,MAAM,sBAAsB,GAAG,IAAI,iBAAiB,CACnD,MAAM,EACN,QAAQ,CAAC,UAAU,CACnB,CAAC;QACF,sBAAsB,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;QACxD,KAAK,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAElC,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,GAAG,EAAE;YAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAExC,wCAAwC;QACxC,MAAM,sBAAsB,GAAG,CAAC,QAA2B,EAAE,EAAE;YAC9D,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,GAAG,EAAE;gBAC3C,QAAQ,CAAC,QAAQ,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;YAClD,CAAC,CAAC,CAAC;YACH,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE;gBACzC,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACnE,QAAQ,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;gBACpC,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;oBACrB,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAChC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;oBAC/B,CAAC;yBAAM,CAAC;wBACP,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAC5B,CAAC;gBACF,CAAC;YACF,CAAC,CAAC,CAAC;YACH,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACxC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;QAC9C,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;QAE/C,MAAM,gBAAgB,GAAG,CAAC,KAAiB,EAAE,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe;gBAAE,OAAO;YAC3C,MAAM,SAAS;YACd,aAAa;YACb,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC;YACrE,MAAM,SAAS;YACd,aAAa;YACb,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC;YACrE,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,SAAS,GAAG,KAAK,CAAC;YAC1C,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,SAAS,GAAG,KAAK,CAAC;YAE1C,QAAQ,CAAC,UAAU,CAAC,IAAI,CACvB,SAAS,CAAC,kBAAkB,CAAC,IAAI,UAAU,EAAE,CAAQ,CACrD,CAAC;YACF,IAAI,CAAC,WAAW,EAAE,CAAC;QACpB,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,GAAG;YACf,eAAe;YACf,QAAQ;YACR,KAAK;YACL,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,oBAAoB,EAAE;gBACrB,IAAI,EAAE,uBAAuB;gBAC7B,KAAK,EAAE,wBAAwB;aAC/B;YACD,iBAAiB,EAAE;gBAClB,IAAI,EAAE,qBAAqB;gBAC3B,KAAK,EAAE,sBAAsB;aAC7B;YACD,sBAAsB,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE;YAClD,wBAAwB,EAAE,SAAS,CAAC,UAAU,CAAC,KAAK,EAAE;YACtD,0BAA0B,EAAE;gBAC3B,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,KAAK,EAAE;gBAC9C,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,KAAK,EAAE;aAChD;YACD,4BAA4B,EAAE;gBAC7B,IAAI,EAAE,uBAAuB,CAAC,UAAU,CAAC,KAAK,EAAE;gBAChD,KAAK,EAAE,wBAAwB,CAAC,UAAU,CAAC,KAAK,EAAE;aAClD;YACD,cAAc;YACd,eAAe,EAAE,KAAK;YACtB,IAAI,EAAE,IAAI,OAAO,EAAE;YACnB,IAAI,EAAE,IAAI,UAAU,EAAE;YACtB,gBAAgB;YAChB,QAAQ,EAAE;gBACT,SAAS,EAAE,KAAK;gBAChB,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,KAAK;aAChB;YACD,aAAa,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE;YACzC,YAAY,EAAE,IAAI;SAClB,CAAC;QAEF,QAAQ,CAAC,gBAAgB,CACxB,mBAAmB,EACnB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,EACxC,KAAK,CACL,CAAC;QACF,QAAQ,CAAC,gBAAgB,CACxB,sBAAsB,EACtB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,EACxC,KAAK,CACL,CAAC;QACF,QAAQ,CAAC,gBAAgB,CACxB,yBAAyB,EACzB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,EACxC,KAAK,CACL,CAAC;QAEF,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;QAC5E,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IACzE,CAAC;IAED,WAAW;QACV,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,kBAAkB;YACnD,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,kBAAkB;gBACpD,aAAa;gBACb,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,qBAAqB;gBACvD,aAAa;gBACb,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,wBAAwB,CAAC;QAC5D,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;IACxD,CAAC;IAED,wBAAwB;QACvB,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe;YAC5B,QAAQ,CAAC,kBAAkB,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU;gBACjE,aAAa;gBACb,QAAQ,CAAC,qBAAqB,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU;gBACpE,aAAa;gBACb,QAAQ,CAAC,wBAAwB,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;QAEzE,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,CAAC;YACnC,QAAQ,CAAC,gBAAgB,CACxB,WAAW,EACX,IAAI,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAC9B,KAAK,CACL,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,CAAC,OAAO,CACrD,CAAC,iBAAiB,EAAE,EAAE;gBACrB,iBAAiB,CAAC,OAAO,GAAG,KAAK,CAAC;gBAClC,iBAAiB,CAAC,OAAO,GAAG,KAAK,CAAC;YACnC,CAAC,CACD,CAAC;QACH,CAAC;aAAM,CAAC;YACP,QAAQ,CAAC,mBAAmB,CAC3B,WAAW,EACX,IAAI,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAC9B,KAAK,CACL,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,CAAC,OAAO,CACrD,CAAC,iBAAiB,EAAE,EAAE;gBACrB,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC;gBACjC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC;YAClC,CAAC,CACD,CAAC;QACH,CAAC;IACF,CAAC;IAED,cAAc,CAAC,KAAoB;QAClC,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QAEhE,IAAI,KAAK,CAAC,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IACC,QAAQ,CAAC,SAAS;YAClB,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,EACjE,CAAC;YACF,IAAI,CAAC,YAAY,EAAE,CAAC;gBACnB,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;YACpE,CAAC;QACF,CAAC;QAED,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC;YAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;QACpB,CAAC;QAED,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC;YAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;QACpB,CAAC;IACF,CAAC;IAED,YAAY,CAAC,KAAoB;QAChC,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QAEjD,IAAI,KAAK,CAAC,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAC9B,CAAC;QAED,IACC,CAAC,QAAQ,CAAC,SAAS;YACnB,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,EAClE,CAAC;YACF,IAAI,YAAY,EAAE,CAAC;gBAClB,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC;YACnC,CAAC;QACF,CAAC;IACF,CAAC;IAED,aAAa;QACZ,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,GAAG,CACP,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACjD,CAAC,EACD,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACjD,CAAC;QACF,IAAI,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI;iBACF,SAAS,EAAE;iBACX,cAAc,CAAC,mBAAmB,CAAC;iBACnC,eAAe,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACxC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE7B,IAAI,CAAC,WAAW,EAAE,CAAC;QACpB,CAAC;IACF,CAAC;IAED,QAAQ;QACP,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;QACzE,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;IAC/C,CAAC;IAED,qBAAqB;QACpB,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,oBAAoB,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QACrE,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,CAAC;QAC9D,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAChC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAC3C,CAAC,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAE,EAAE;YACrC,mBAAmB,CAAC,QAAQ,CAAC,IAAI;YAChC,aAAa;YACb,IAAI,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAC,UAAU,CAAC,CACpD,CAAC;YACF,mBAAmB,CAAC,UAAU,CAAC,IAAI;YAClC,aAAa;YACb,IAAI,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC,UAAU,CAAC,CACtD,CAAC;QACH,CAAC,CACD,CAAC;QACF,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;IACpB,CAAC;IAED,oBAAoB;QACnB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,oBAAoB,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QACpE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CACrB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAQ,CACrD,CAAC;QACF,QAAQ,CAAC,UAAU,CAAC,IAAI,CACvB,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAQ,CACvD,CAAC;QACF,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAC3C,CAAC,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAE,EAAE;YACrC,aAAa;YACb,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAE,CAAC,QAAQ,CAAC,IAAI,CAC9C,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAQ,CAC/D,CAAC;YACF,aAAa;YACb,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAE,CAAC,UAAU,CAAC,IAAI,CAChD,mBAAmB,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAQ,CACjE,CAAC;QACH,CAAC,CACD,CAAC;IACH,CAAC;IAED,WAAW;QACV,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;IAC1C,CAAC;IAED,MAAM;QACL,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC;QAC1D,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;QAC9C,IAAI,CAAC,WAAW,EAAE,CAAC;IACpB,CAAC;IAED,OAAO;QACN,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QAC1C,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACjC,QAAQ,CAAC,mBAAmB,CAC3B,mBAAmB,EACnB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,EACxC,KAAK,CACL,CAAC;QACF,QAAQ,CAAC,mBAAmB,CAC3B,sBAAsB,EACtB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,EACxC,KAAK,CACL,CAAC;QACF,QAAQ,CAAC,mBAAmB,CAC3B,yBAAyB,EACzB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,EACxC,KAAK,CACL,CAAC;QACF,QAAQ,CAAC,mBAAmB,CAC3B,WAAW,EACX,IAAI,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAC9B,KAAK,CACL,CAAC;QACF,QAAQ,CAAC,mBAAmB,CAC3B,SAAS,EACT,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9B,KAAK,CACL,CAAC;QACF,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IAC5E,CAAC;CACD"}