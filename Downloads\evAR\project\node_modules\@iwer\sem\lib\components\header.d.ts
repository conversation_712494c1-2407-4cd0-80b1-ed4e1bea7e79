/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { InputLayer } from '../scene.js';
import React from 'react';
import { XRDevice } from 'iwer';
interface HeaderUIProps {
    xrDevice: XRDevice;
    inputLayer: InputLayer;
    keyMapOpen: boolean;
    setKeyMapOpen: React.Dispatch<React.SetStateAction<boolean>>;
    fovSettingOpen: boolean;
    setFovSettingOpen: React.Dispatch<React.SetStateAction<boolean>>;
}
export declare const HeaderUI: React.FC<HeaderUIProps>;
export {};
//# sourceMappingURL=header.d.ts.map