{"version": 3, "file": "reconstruct.js", "sourceRoot": "", "sources": ["../src/reconstruct.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAOH,OAAO,EAAE,aAAa,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AAGxE,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAGrC,MAAM,UAAU,YAAY,CAC3B,MAAyB,EACzB,aAAsB;IAEtB,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,aAAa,IAAI,SAAS,CAE3D,CAAC;AACd,CAAC;AAED,MAAM,CAAC,MAAM,sBAAsB,GAAG,CACrC,GAA+B,EAC/B,SAA4B,EAC5B,IAAmB,EAClB,EAAE;IACH,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,EAAE;QAClD,MAAM,aAAa,GAAG,aAAa,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;QAChE,QAAQ,aAAa,CAAC,UAAU,EAAE,CAAC;YAClC,KAAK,iBAAiB,CAAC,GAAG;gBACzB,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBACtC,MAAM;YACP,KAAK,iBAAiB,CAAC,KAAK;gBAC3B,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBACvC,MAAM;YACP,KAAK,iBAAiB,CAAC,IAAI;gBAC1B,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBACvC,MAAM;QACR,CAAC;QACD,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,OAAO,EAAE,CAAC;YACb,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC;QACD,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC"}