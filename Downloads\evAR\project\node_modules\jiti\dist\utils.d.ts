import type { PackageJson } from "pkg-types";
export declare function getCacheDir(): string;
export declare function isDir(filename: string): boolean;
export declare function isWritable(filename: string): boolean;
export declare function md5(content: string, len?: number): string;
export declare function detectLegacySyntax(code: string): RegExpMatchArray | null;
export declare function isObject(val: any): boolean;
export declare function readNearestPackageJSON(path: string): PackageJson | undefined;
