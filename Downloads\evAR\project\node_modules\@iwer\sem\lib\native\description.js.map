{"version": 3, "file": "description.js", "sourceRoot": "", "sources": ["../../src/native/description.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAmCH,MAAM,CAAN,IAAY,iBAsBX;AAtBD,WAAY,iBAAiB;IAC5B,oCAAe,CAAA;IACf,wCAAmB,CAAA;IACnB,4CAAuB,CAAA;IACvB,oCAAe,CAAA;IACf,oCAAe,CAAA;IACf,8CAAyB,CAAA;IACzB,kDAA6B,CAAA;IAC7B,oCAAe,CAAA;IACf,wCAAmB,CAAA;IACnB,gCAAW,CAAA;IACX,sCAAiB,CAAA;IACjB,kCAAa,CAAA;IACb,oCAAe,CAAA;IACf,0CAAqB,CAAA;IACrB,8CAAyB,CAAA;IACzB,gEAA2C,CAAA;IAC3C,oCAAe,CAAA;IACf,wCAAmB,CAAA;IACnB,wDAAmC,CAAA;IACnC,wDAAmC,CAAA;IACnC,wCAAmB,CAAA;AACpB,CAAC,EAtBW,iBAAiB,KAAjB,iBAAiB,QAsB5B;AA6CD,SAAS,SAAS,CAAC,GAAQ;IAC1B,OAAO,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ,CAAC;AAC/D,CAAC;AACD,SAAS,SAAS,CAAC,GAAQ;IAC1B,OAAO,CACN,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ;QACzB,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ;QACzB,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ,CACzB,CAAC;AACH,CAAC;AACD,SAAS,YAAY,CAAC,GAAQ;IAC7B,OAAO,CACN,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ;QACzB,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ;QACzB,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ;QACzB,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ,CACzB,CAAC;AACH,CAAC;AACD,SAAS,WAAW,CAAC,GAAQ;IAC5B,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACvD,CAAC;AACD,SAAS,WAAW,CAAC,GAAQ;IAC5B,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACvD,CAAC;AACD,SAAS,cAAc,CAAC,GAAQ;IAC/B,OAAO,CACN,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;QAC3B,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC;QAC7B,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;QAC1B,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAC5D,CAAC;AACH,CAAC;AACD,SAAS,MAAM,CAAC,GAAQ;IACvB,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACjE,CAAC;AACD,SAAS,oBAAoB,CAAC,GAAQ;IACrC,OAAO,GAAG,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACjE,CAAC;AACD,SAAS,oBAAoB,CAAC,GAAQ;IACrC,OAAO,GAAG,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACtE,CAAC;AACD,SAAS,oBAAoB,CAAC,GAAQ;IACrC,OAAO,GAAG,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACtE,CAAC;AACD,SAAS,yBAAyB,CAAC,GAAQ;IAC1C,OAAO,CACN,GAAG,CAAC,cAAc,CAAC,gBAAgB,CAAC;QACpC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;QACjC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CACnE,CAAC;AACH,CAAC;AACD,SAAS,uBAAuB,CAAC,GAAQ;IACxC,OAAO,GAAG,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC/E,CAAC;AACD,SAAS,wBAAwB,CAAC,GAAQ;IACzC,OAAO,CACN,oBAAoB,CAAC,GAAG,CAAC;QACzB,oBAAoB,CAAC,GAAG,CAAC;QACzB,oBAAoB,CAAC,GAAG,CAAC;QACzB,yBAAyB,CAAC,GAAG,CAAC;QAC9B,uBAAuB,CAAC,GAAG,CAAC,CAC5B,CAAC;AACH,CAAC;AACD,SAAS,eAAe,CAAC,GAAQ;IAChC,OAAO,CACN,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ;QAC5B,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;QAC7B,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAC9C,CAAC;AACH,CAAC;AACD,MAAM,UAAU,gBAAgB,CAAC,GAAQ;IACxC,OAAO,CACN,GAAG,CAAC,IAAI,KAAK,OAAO;QACpB,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ;QAC/B,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;QAClC,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,eAAe,CAAC,CAC1C,CAAC;AACH,CAAC"}