import { Mesh } from 'three';
import { SpatialEntityComponent, SpatialEntityComponentType } from './component.js';
import { TriangleMeshMETA } from '../../generated/protos/openxr_scene.js';
import { Vector3 } from '../../generated/protos/openxr_core.js';
export declare class TriangleMeshComponent extends SpatialEntityComponent {
    private _vertices;
    private _indices;
    private _polygonCount;
    private _vertexCount;
    private _dimensions;
    type: SpatialEntityComponentType;
    constructor(spatialEntity: Mesh, initData: TriangleMeshMETA);
    private buildGeometry;
    get vertexCount(): number;
    get polygonCount(): number;
    get dimensions(): Vector3;
    get initData(): {
        vertices: Vector3[];
        indices: number[];
    };
    get pbData(): TriangleMeshMETA;
}
//# sourceMappingURL=trianglemesh.d.ts.map