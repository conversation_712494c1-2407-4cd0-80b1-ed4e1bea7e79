/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { NativeMesh, NativePlane, XRDevice } from 'iwer';
import { mat4 } from 'gl-matrix';
export declare class SyntheticEnvironmentModule extends EventTarget {
    private xrDevice;
    readonly trackedPlanes: Set<NativePlane>;
    readonly trackedMeshes: Set<NativeMesh>;
    readonly version = "0.2.5";
    private renderer;
    private scene;
    private camera;
    private objectMap;
    private planes;
    private boxes;
    private meshes;
    private tempPosition;
    private tempQuaternion;
    private tempDirection;
    private tempScale;
    private tempMatrix;
    private raycaster;
    private hitTestTarget;
    private hitTestMarker;
    private worldNormal;
    private normalMatrix;
    constructor(xrDevice: XRDevice);
    get environmentCanvas(): HTMLCanvasElement;
    get planesVisible(): boolean;
    set planesVisible(visible: boolean);
    get boundingBoxesVisible(): boolean;
    set boundingBoxesVisible(visible: boolean);
    get meshesVisible(): boolean;
    set meshesVisible(visible: boolean);
    render(): void;
    deleteAll(): void;
    loadEnvironment(json: any): void;
    loadDefaultEnvironment(envId: string): void;
    computeHitTestResults(mat4: mat4): import("three").Matrix4Tuple[];
}
//# sourceMappingURL=sem.d.ts.map