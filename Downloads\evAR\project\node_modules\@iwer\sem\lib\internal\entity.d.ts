/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Mesh } from 'three';
import { NativeMesh, NativePlane } from 'iwer';
import { SpatialEntityJSON } from './description.js';
import { SpatialEntityComponent, SpatialEntityComponentType } from './components/component.js';
export declare enum SpatialEntityType {
    Plane = "plane",
    Box = "box",
    Mesh = "mesh"
}
export declare class SpatialEntity extends Mesh {
    name: string;
    isSpatialEntity: boolean;
    private _spatialUUID;
    private _componentMap;
    private _nativeEntity;
    constructor(uuid?: string);
    get spatialUUID(): string;
    updateMatrixWorld(force?: boolean): void;
    addComponent(componentType: SpatialEntityComponentType, initData: any): void;
    getComponent(componentType: SpatialEntityComponentType): SpatialEntityComponent | undefined;
    duplicate(): SpatialEntity;
    get nativeEntity(): NativePlane | NativeMesh | undefined;
    static fromJSON(json: SpatialEntityJSON): SpatialEntity;
    get entityType(): SpatialEntityType;
    static toJSON(entity: SpatialEntity): SpatialEntityJSON;
}
//# sourceMappingURL=entity.d.ts.map