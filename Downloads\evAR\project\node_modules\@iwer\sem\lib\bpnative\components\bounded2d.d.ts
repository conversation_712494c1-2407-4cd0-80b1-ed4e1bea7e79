/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Bounded2DComponentJSON, Bounded2DJSON } from '../description.js';
import { Mesh, Vector2 } from 'three';
import { SpatialEntityComponent, SpatialEntityComponentType } from './component.js';
export declare class Bounded2DComponent extends SpatialEntityComponent {
    private _offset;
    private _extent;
    type: SpatialEntityComponentType;
    constructor(spatialEntity: Mesh, initData: Bounded2DJSON);
    buildGeometry(): void;
    get offset(): Vector2;
    get extent(): Vector2;
    get initData(): {
        offset: Vector2;
        extent: Vector2;
    };
    toJSON(): Bounded2DComponentJSON;
}
//# sourceMappingURL=bounded2d.d.ts.map