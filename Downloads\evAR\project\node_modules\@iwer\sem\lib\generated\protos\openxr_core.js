// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.0
//   protoc               v5.29.1
// source: protos/openxr_core.proto
/* eslint-disable */
import { BinaryReader, BinaryWriter } from '@bufbuild/protobuf/wire';
export const protobufPackage = 'openxr';
function createBaseVector2() {
    return { x: 0, y: 0 };
}
export const Vector2 = {
    encode(message, writer = new BinaryWriter()) {
        if (message.x !== 0) {
            writer.uint32(13).float(message.x);
        }
        if (message.y !== 0) {
            writer.uint32(21).float(message.y);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseVector2();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 13) {
                        break;
                    }
                    message.x = reader.float();
                    continue;
                }
                case 2: {
                    if (tag !== 21) {
                        break;
                    }
                    message.y = reader.float();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            x: isSet(object.x) ? globalThis.Number(object.x) : 0,
            y: isSet(object.y) ? globalThis.Number(object.y) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.x !== 0) {
            obj.x = message.x;
        }
        if (message.y !== 0) {
            obj.y = message.y;
        }
        return obj;
    },
    create(base) {
        return Vector2.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseVector2();
        message.x = (_a = object.x) !== null && _a !== void 0 ? _a : 0;
        message.y = (_b = object.y) !== null && _b !== void 0 ? _b : 0;
        return message;
    },
};
function createBaseExtent2() {
    return { width: 0, height: 0 };
}
export const Extent2 = {
    encode(message, writer = new BinaryWriter()) {
        if (message.width !== 0) {
            writer.uint32(13).float(message.width);
        }
        if (message.height !== 0) {
            writer.uint32(21).float(message.height);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseExtent2();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 13) {
                        break;
                    }
                    message.width = reader.float();
                    continue;
                }
                case 2: {
                    if (tag !== 21) {
                        break;
                    }
                    message.height = reader.float();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            width: isSet(object.width) ? globalThis.Number(object.width) : 0,
            height: isSet(object.height) ? globalThis.Number(object.height) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.width !== 0) {
            obj.width = message.width;
        }
        if (message.height !== 0) {
            obj.height = message.height;
        }
        return obj;
    },
    create(base) {
        return Extent2.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseExtent2();
        message.width = (_a = object.width) !== null && _a !== void 0 ? _a : 0;
        message.height = (_b = object.height) !== null && _b !== void 0 ? _b : 0;
        return message;
    },
};
function createBaseVector3() {
    return { x: 0, y: 0, z: 0 };
}
export const Vector3 = {
    encode(message, writer = new BinaryWriter()) {
        if (message.x !== 0) {
            writer.uint32(13).float(message.x);
        }
        if (message.y !== 0) {
            writer.uint32(21).float(message.y);
        }
        if (message.z !== 0) {
            writer.uint32(29).float(message.z);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseVector3();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 13) {
                        break;
                    }
                    message.x = reader.float();
                    continue;
                }
                case 2: {
                    if (tag !== 21) {
                        break;
                    }
                    message.y = reader.float();
                    continue;
                }
                case 3: {
                    if (tag !== 29) {
                        break;
                    }
                    message.z = reader.float();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            x: isSet(object.x) ? globalThis.Number(object.x) : 0,
            y: isSet(object.y) ? globalThis.Number(object.y) : 0,
            z: isSet(object.z) ? globalThis.Number(object.z) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.x !== 0) {
            obj.x = message.x;
        }
        if (message.y !== 0) {
            obj.y = message.y;
        }
        if (message.z !== 0) {
            obj.z = message.z;
        }
        return obj;
    },
    create(base) {
        return Vector3.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseVector3();
        message.x = (_a = object.x) !== null && _a !== void 0 ? _a : 0;
        message.y = (_b = object.y) !== null && _b !== void 0 ? _b : 0;
        message.z = (_c = object.z) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseExtent3() {
    return { width: 0, height: 0, depth: 0 };
}
export const Extent3 = {
    encode(message, writer = new BinaryWriter()) {
        if (message.width !== 0) {
            writer.uint32(13).float(message.width);
        }
        if (message.height !== 0) {
            writer.uint32(21).float(message.height);
        }
        if (message.depth !== 0) {
            writer.uint32(29).float(message.depth);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseExtent3();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 13) {
                        break;
                    }
                    message.width = reader.float();
                    continue;
                }
                case 2: {
                    if (tag !== 21) {
                        break;
                    }
                    message.height = reader.float();
                    continue;
                }
                case 3: {
                    if (tag !== 29) {
                        break;
                    }
                    message.depth = reader.float();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            width: isSet(object.width) ? globalThis.Number(object.width) : 0,
            height: isSet(object.height) ? globalThis.Number(object.height) : 0,
            depth: isSet(object.depth) ? globalThis.Number(object.depth) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.width !== 0) {
            obj.width = message.width;
        }
        if (message.height !== 0) {
            obj.height = message.height;
        }
        if (message.depth !== 0) {
            obj.depth = message.depth;
        }
        return obj;
    },
    create(base) {
        return Extent3.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseExtent3();
        message.width = (_a = object.width) !== null && _a !== void 0 ? _a : 0;
        message.height = (_b = object.height) !== null && _b !== void 0 ? _b : 0;
        message.depth = (_c = object.depth) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseQuaternion() {
    return { x: 0, y: 0, z: 0, w: 0 };
}
export const Quaternion = {
    encode(message, writer = new BinaryWriter()) {
        if (message.x !== 0) {
            writer.uint32(13).float(message.x);
        }
        if (message.y !== 0) {
            writer.uint32(21).float(message.y);
        }
        if (message.z !== 0) {
            writer.uint32(29).float(message.z);
        }
        if (message.w !== 0) {
            writer.uint32(37).float(message.w);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQuaternion();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 13) {
                        break;
                    }
                    message.x = reader.float();
                    continue;
                }
                case 2: {
                    if (tag !== 21) {
                        break;
                    }
                    message.y = reader.float();
                    continue;
                }
                case 3: {
                    if (tag !== 29) {
                        break;
                    }
                    message.z = reader.float();
                    continue;
                }
                case 4: {
                    if (tag !== 37) {
                        break;
                    }
                    message.w = reader.float();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            x: isSet(object.x) ? globalThis.Number(object.x) : 0,
            y: isSet(object.y) ? globalThis.Number(object.y) : 0,
            z: isSet(object.z) ? globalThis.Number(object.z) : 0,
            w: isSet(object.w) ? globalThis.Number(object.w) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.x !== 0) {
            obj.x = message.x;
        }
        if (message.y !== 0) {
            obj.y = message.y;
        }
        if (message.z !== 0) {
            obj.z = message.z;
        }
        if (message.w !== 0) {
            obj.w = message.w;
        }
        return obj;
    },
    create(base) {
        return Quaternion.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseQuaternion();
        message.x = (_a = object.x) !== null && _a !== void 0 ? _a : 0;
        message.y = (_b = object.y) !== null && _b !== void 0 ? _b : 0;
        message.z = (_c = object.z) !== null && _c !== void 0 ? _c : 0;
        message.w = (_d = object.w) !== null && _d !== void 0 ? _d : 0;
        return message;
    },
};
function createBaseRect2D() {
    return { offset: undefined, extent: undefined };
}
export const Rect2D = {
    encode(message, writer = new BinaryWriter()) {
        if (message.offset !== undefined) {
            Vector2.encode(message.offset, writer.uint32(10).fork()).join();
        }
        if (message.extent !== undefined) {
            Extent2.encode(message.extent, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRect2D();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.offset = Vector2.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.extent = Extent2.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            offset: isSet(object.offset)
                ? Vector2.fromJSON(object.offset)
                : undefined,
            extent: isSet(object.extent)
                ? Extent2.fromJSON(object.extent)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.offset !== undefined) {
            obj.offset = Vector2.toJSON(message.offset);
        }
        if (message.extent !== undefined) {
            obj.extent = Extent2.toJSON(message.extent);
        }
        return obj;
    },
    create(base) {
        return Rect2D.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        const message = createBaseRect2D();
        message.offset =
            object.offset !== undefined && object.offset !== null
                ? Vector2.fromPartial(object.offset)
                : undefined;
        message.extent =
            object.extent !== undefined && object.extent !== null
                ? Extent2.fromPartial(object.extent)
                : undefined;
        return message;
    },
};
function createBaseRect3D() {
    return { offset: undefined, extent: undefined };
}
export const Rect3D = {
    encode(message, writer = new BinaryWriter()) {
        if (message.offset !== undefined) {
            Vector3.encode(message.offset, writer.uint32(10).fork()).join();
        }
        if (message.extent !== undefined) {
            Extent3.encode(message.extent, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRect3D();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.offset = Vector3.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.extent = Extent3.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            offset: isSet(object.offset)
                ? Vector3.fromJSON(object.offset)
                : undefined,
            extent: isSet(object.extent)
                ? Extent3.fromJSON(object.extent)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.offset !== undefined) {
            obj.offset = Vector3.toJSON(message.offset);
        }
        if (message.extent !== undefined) {
            obj.extent = Extent3.toJSON(message.extent);
        }
        return obj;
    },
    create(base) {
        return Rect3D.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        const message = createBaseRect3D();
        message.offset =
            object.offset !== undefined && object.offset !== null
                ? Vector3.fromPartial(object.offset)
                : undefined;
        message.extent =
            object.extent !== undefined && object.extent !== null
                ? Extent3.fromPartial(object.extent)
                : undefined;
        return message;
    },
};
function createBaseFov() {
    return { angleLeft: 0, angleRight: 0, angleUp: 0, angleDown: 0 };
}
export const Fov = {
    encode(message, writer = new BinaryWriter()) {
        if (message.angleLeft !== 0) {
            writer.uint32(13).float(message.angleLeft);
        }
        if (message.angleRight !== 0) {
            writer.uint32(21).float(message.angleRight);
        }
        if (message.angleUp !== 0) {
            writer.uint32(29).float(message.angleUp);
        }
        if (message.angleDown !== 0) {
            writer.uint32(37).float(message.angleDown);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseFov();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 13) {
                        break;
                    }
                    message.angleLeft = reader.float();
                    continue;
                }
                case 2: {
                    if (tag !== 21) {
                        break;
                    }
                    message.angleRight = reader.float();
                    continue;
                }
                case 3: {
                    if (tag !== 29) {
                        break;
                    }
                    message.angleUp = reader.float();
                    continue;
                }
                case 4: {
                    if (tag !== 37) {
                        break;
                    }
                    message.angleDown = reader.float();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            angleLeft: isSet(object.angleLeft)
                ? globalThis.Number(object.angleLeft)
                : 0,
            angleRight: isSet(object.angleRight)
                ? globalThis.Number(object.angleRight)
                : 0,
            angleUp: isSet(object.angleUp) ? globalThis.Number(object.angleUp) : 0,
            angleDown: isSet(object.angleDown)
                ? globalThis.Number(object.angleDown)
                : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.angleLeft !== 0) {
            obj.angleLeft = message.angleLeft;
        }
        if (message.angleRight !== 0) {
            obj.angleRight = message.angleRight;
        }
        if (message.angleUp !== 0) {
            obj.angleUp = message.angleUp;
        }
        if (message.angleDown !== 0) {
            obj.angleDown = message.angleDown;
        }
        return obj;
    },
    create(base) {
        return Fov.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseFov();
        message.angleLeft = (_a = object.angleLeft) !== null && _a !== void 0 ? _a : 0;
        message.angleRight = (_b = object.angleRight) !== null && _b !== void 0 ? _b : 0;
        message.angleUp = (_c = object.angleUp) !== null && _c !== void 0 ? _c : 0;
        message.angleDown = (_d = object.angleDown) !== null && _d !== void 0 ? _d : 0;
        return message;
    },
};
function createBasePose() {
    return { orientation: undefined, position: undefined };
}
export const Pose = {
    encode(message, writer = new BinaryWriter()) {
        if (message.orientation !== undefined) {
            Quaternion.encode(message.orientation, writer.uint32(10).fork()).join();
        }
        if (message.position !== undefined) {
            Vector3.encode(message.position, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePose();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.orientation = Quaternion.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.position = Vector3.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            orientation: isSet(object.orientation)
                ? Quaternion.fromJSON(object.orientation)
                : undefined,
            position: isSet(object.position)
                ? Vector3.fromJSON(object.position)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.orientation !== undefined) {
            obj.orientation = Quaternion.toJSON(message.orientation);
        }
        if (message.position !== undefined) {
            obj.position = Vector3.toJSON(message.position);
        }
        return obj;
    },
    create(base) {
        return Pose.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        const message = createBasePose();
        message.orientation =
            object.orientation !== undefined && object.orientation !== null
                ? Quaternion.fromPartial(object.orientation)
                : undefined;
        message.position =
            object.position !== undefined && object.position !== null
                ? Vector3.fromPartial(object.position)
                : undefined;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
//# sourceMappingURL=openxr_core.js.map