import React from 'react';
interface SettingsMenuProps {
    keyMap: {
        left: {
            up: string;
            down: string;
            left: string;
            right: string;
            'x-button': string;
            'y-button': string;
            trigger: string;
            squeeze: string;
        };
        right: {
            up: string;
            down: string;
            left: string;
            right: string;
            'a-button': string;
            'b-button': string;
            trigger: string;
            squeeze: string;
        };
    };
    setKeyMap: React.Dispatch<React.SetStateAction<{
        left: {
            up: string;
            down: string;
            left: string;
            right: string;
            'x-button': string;
            'y-button': string;
            trigger: string;
            squeeze: string;
        };
        right: {
            up: string;
            down: string;
            left: string;
            right: string;
            'a-button': string;
            'b-button': string;
            trigger: string;
            squeeze: string;
        };
    }>>;
}
export declare const SettingsMenu: React.FC<SettingsMenuProps>;
export {};
//# sourceMappingURL=settings.d.ts.map