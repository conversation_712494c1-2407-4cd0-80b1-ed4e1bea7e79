{"version": 3, "file": "settings.js", "sourceRoot": "", "sources": ["../../src/components/settings.tsx"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAClD,OAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAEnD,OAAO,EAAE,eAAe,EAAE,MAAM,gCAAgC,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AACzC,OAAO,EAAE,KAAK,EAAE,MAAM,mCAAmC,CAAC;AAC1D,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAmDvC,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAA;;;;;;;;;CASnC,CAAC;AAEF,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAA;;;;CAIxB,CAAC;AAEF,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAA;;;;;CAKrB,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAgC,CAAC,EACzD,MAAM,EACN,SAAS,GACT,EAAE,EAAE;IACJ,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,QAAQ,CAG1C,IAAI,CAAC,CAAC;IAEhB,MAAM,YAAY,GAAG,CAAC,UAA4B,EAAE,MAAc,EAAE,EAAE;QACrE,iBAAiB,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC,CAAC;IAEF,MAAM,QAAQ,GAAG,CAAC,UAA4B,EAAE,MAAc,EAAE,EAAE;QACjE,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC1B,GAAG,UAAU;YACb,CAAC,UAAU,CAAC,EAAE;gBACb,GAAG,UAAU,CAAC,UAAU,CAAC;gBACzB,CAAC,MAAM,CAAC,EAAE,UAAU;aACpB;SACD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,SAAS,CAAC,GAAG,EAAE;QACd,MAAM,aAAa,GAAG,CAAC,KAAoB,EAAE,EAAE;YAC9C,IAAI,cAAc,EAAE,CAAC;gBACpB,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;oBAC1B,GAAG,UAAU;oBACb,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;wBAC5B,GAAG,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC;wBACxC,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,IAAI;qBACnC;iBACD,CAAC,CAAC,CAAC;gBACJ,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACF,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,CAAC,KAAiB,EAAE,EAAE;YAC7C,IAAI,cAAc,EAAE,CAAC;gBACpB,MAAM,WAAW,GAChB,KAAK,CAAC,MAAM,KAAK,CAAC;oBACjB,CAAC,CAAC,WAAW;oBACb,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;wBACpB,CAAC,CAAC,YAAY;wBACd,CAAC,CAAC,IAAI,CAAC;gBACT,IAAI,WAAW,EAAE,CAAC;oBACjB,SAAS,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;wBAC1B,GAAG,UAAU;wBACb,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;4BAC5B,GAAG,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC;4BACxC,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,WAAW;yBACpC;qBACD,CAAC,CAAC,CAAC;oBACJ,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACF,CAAC;QACF,CAAC,CAAC;QAEF,MAAM,yBAAyB,GAAG,CAAC,KAAiB,EAAE,EAAE;YACvD,KAAK,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAClD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QACtD,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,yBAAyB,CAAC,CAAC;QAElE,OAAO,GAAG,EAAE;YACX,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YACrD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YACzD,MAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,yBAAyB,CAAC,CAAC;QACtE,CAAC,CAAC;IACH,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;IAEhC,OAAO,CACN,MAAC,iBAAiB,eACjB,KAAC,MAAM,cACL,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CACzC,MAAC,GAAG,eACH,KAAC,WAAW,IACX,UAAU,EAAE,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,EACnD,UAAU,EAAC,MAAM,GAChB,EACF,MAAC,WAAW,gBAAW,KAAK,aAC3B,KAAC,MAAM,gBACI,KAAK,EACf,KAAK,EAAE;wCACN,KAAK,EAAE,OAAO;wCACd,eAAe,EACd,cAAc;4CACd,cAAc,CAAC,UAAU,KAAK,MAAM;4CACpC,cAAc,CAAC,MAAM,KAAK,MAAM;4CAC/B,CAAC,CAAC,0BAA0B;4CAC5B,CAAC,CAAC,0BAA0B;qCAC9B,EACD,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,EAC3C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,EAAE,YAEtC,MAAM,CAAC,IAA+B,CAAC,MAAM,CAAC,GACxC,EACT,KAAC,MAAM,IACN,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,cACd,KAAK,EACf,OAAO,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,EACvC,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,EAAE,YAExC,KAAC,eAAe,IAAC,IAAI,EAAE,KAAK,GAAI,GACxB,IACI,KA9BL,MAAM,CA+BV,CACN,CAAC,GACM,EACT,KAAC,MAAM,cACL,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAC1C,MAAC,GAAG,eACH,KAAC,WAAW,IACX,UAAU,EAAE,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,EACnD,UAAU,EAAC,OAAO,GACjB,EACF,MAAC,WAAW,gBAAW,KAAK,aAC3B,KAAC,MAAM,gBACI,KAAK,EACf,KAAK,EAAE;wCACN,KAAK,EAAE,OAAO;wCACd,eAAe,EACd,cAAc;4CACd,cAAc,CAAC,UAAU,KAAK,OAAO;4CACrC,cAAc,CAAC,MAAM,KAAK,MAAM;4CAC/B,CAAC,CAAC,0BAA0B;4CAC5B,CAAC,CAAC,0BAA0B;qCAC9B,EACD,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,EAC5C,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,EAAE,YAEtC,MAAM,CAAC,KAAgC,CAAC,MAAM,CAAC,GACzC,EACT,KAAC,MAAM,gBACI,KAAK,EACf,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EACxB,OAAO,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,EACxC,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,EAAE,YAExC,KAAC,eAAe,IAAC,IAAI,EAAE,KAAK,GAAI,GACxB,IACI,KA9BL,MAAM,CA+BV,CACN,CAAC,GACM,IACU,CACpB,CAAC;AACH,CAAC,CAAC"}