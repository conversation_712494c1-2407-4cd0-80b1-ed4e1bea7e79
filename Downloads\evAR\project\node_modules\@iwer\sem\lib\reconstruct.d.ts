/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { SceneFileJSON, SpatialEntityComponentJSON, SpatialEntityJSON } from './internal/description.js';
import { Mesh } from 'three';
import type { SyntheticEnvironmentModule } from './index.js';
export declare function getComponent<T extends SpatialEntityComponentJSON>(entity: SpatialEntityJSON, componentName: keyof T): T | undefined;
export declare const reconstructEnvironment: (sem: SyntheticEnvironmentModule, objectMap: Map<string, Mesh>, json: SceneFileJSON) => void;
//# sourceMappingURL=reconstruct.d.ts.map