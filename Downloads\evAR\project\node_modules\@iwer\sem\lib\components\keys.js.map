{"version": 3, "file": "keys.js", "sourceRoot": "", "sources": ["../../src/components/keys.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AACnD,OAAO,EACN,SAAS,EACT,qBAAqB,EACrB,eAAe,EACf,WAAW,EACX,WAAW,EACX,YAAY,EACZ,SAAS,EACT,YAAY,GACZ,MAAM,mCAAmC,CAAC;AAE3C,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAErC,MAAM,CAAC,MAAM,gBAAgB,GAEzB;IACH,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,GAAG,EAAE,KAAC,MAAM,IAAC,IAAI,EAAE,qBAAqB,GAAI;IAC5C,SAAS,EAAE,KAAC,MAAM,IAAC,IAAI,EAAE,YAAY,GAAI;IACzC,KAAK,EAAE,CACN,KAAC,MAAM,IACN,KAAK,EAAE;YACN,SAAS,EAAE,eAAe;SAC1B,EACD,IAAI,EAAE,eAAe,GACpB,CACF;IACD,SAAS,EAAE,KAAC,MAAM,IAAC,IAAI,EAAE,SAAS,GAAI;IACtC,UAAU,EAAE,KAAC,MAAM,IAAC,IAAI,EAAE,SAAS,GAAI;IACvC,KAAK,EAAE,GAAG;IACV,OAAO,EAAE,KAAC,MAAM,IAAC,IAAI,EAAE,SAAS,GAAI;IACpC,SAAS,EAAE,KAAC,MAAM,IAAC,IAAI,EAAE,WAAW,GAAI;IACxC,SAAS,EAAE,KAAC,MAAM,IAAC,IAAI,EAAE,WAAW,GAAI;IACxC,UAAU,EAAE,KAAC,MAAM,IAAC,IAAI,EAAE,YAAY,GAAI;IAC1C,SAAS,EAAE,GAAG;IACd,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,GAAG;IACX,KAAK,EAAE,GAAG;IACV,SAAS,EAAE,GAAG;IACd,WAAW,EAAE,GAAG;IAChB,SAAS,EAAE,IAAI;IACf,YAAY,EAAE,GAAG;IACjB,KAAK,EAAE,GAAG;IACV,SAAS,EAAE,KAAC,SAAS,KAAG;IACxB,UAAU,EAAE,KAAC,UAAU,KAAG;CAC1B,CAAC"}