/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Bounded3DComponentJSON, Bounded3DJSON } from '../description.js';
import { Mesh, Vector3 } from 'three';
import { SpatialEntityComponent, SpatialEntityComponentType } from './component.js';
export declare class Bounded3DComponent extends SpatialEntityComponent {
    private _offset;
    private _extent;
    type: SpatialEntityComponentType;
    constructor(spatialEntity: Mesh, initData: Bounded3DJSON);
    buildGeometry(): void;
    get offset(): Vector3;
    get extent(): Vector3;
    get initData(): {
        offset: Vector3;
        extent: Vector3;
    };
    toJSON(): Bounded3DComponentJSON;
}
//# sourceMappingURL=bounded3d.d.ts.map