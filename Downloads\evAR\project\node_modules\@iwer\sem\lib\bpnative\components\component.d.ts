/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { Mesh } from 'three';
import { SpatialEntityComponentJSON } from '../description.js';
export declare enum SpatialEntityComponentType {
    Locatable = "locatable",
    Bounded3D = "bounded3D",
    Bounded2D = "bounded2D",
    TriangleMesh = "triangleMesh",
    SemanticLabels = "semanticLabels"
}
export declare abstract class SpatialEntityComponent extends EventTarget {
    protected _spatialEntity: Mesh;
    constructor(_spatialEntity: Mesh);
    abstract toJSON(): SpatialEntityComponentJSON;
    abstract get initData(): any;
    abstract type: SpatialEntityComponentType;
}
//# sourceMappingURL=component.d.ts.map