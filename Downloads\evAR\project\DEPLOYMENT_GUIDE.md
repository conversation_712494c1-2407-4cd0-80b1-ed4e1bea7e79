# EV-AR Deployment Guide

## Overview
This guide covers the complete deployment process for the EV-AR application, including environment setup, build optimization, and production deployment to Netlify.

## Prerequisites

### System Requirements
- Node.js 16.x or higher
- npm 8.x or higher
- Modern browser with WebXR support
- HTTPS certificate (required for AR features)

### Development Tools
- Git for version control
- Code editor with TypeScript support
- Chrome DevTools for debugging
- Network throttling tools for testing

## Environment Configuration

### Environment Variables
Create `.env` file for local development:

```bash
# API Configuration
VITE_API_BASE_URL=https://api.ev-ar.com
VITE_CHARGING_STATIONS_API=https://api.openchargemap.org

# Map Services  
VITE_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_key_here

# Analytics
VITE_GOOGLE_ANALYTICS_ID=GA-MEASUREMENT-ID
VITE_MIXPANEL_TOKEN=your_mixpanel_token

# Feature Flags
VITE_ENABLE_AR_MODE=true
VITE_ENABLE_ANALYTICS=true
VITE_DEBUG_MODE=false
```

### Production Environment Variables
For production deployment on Netlify:

```bash
# Build Settings
NODE_VERSION=18
NPM_VERSION=8
BUILD_COMMAND=npm run build
PUBLISH_DIRECTORY=dist

# Runtime Variables
VITE_APP_ENV=production
VITE_API_BASE_URL=https://prod-api.ev-ar.com
VITE_ENABLE_DEBUG=false
```

## Build Process

### Local Build
```bash
# Install dependencies
npm install

# Run type checking
npm run type-check

# Run linting
npm run lint

# Build for production
npm run build

# Preview production build
npm run preview
```

### Build Optimization

#### Bundle Analysis
```bash
# Analyze bundle size
npm run build -- --analyze

# Generate bundle report
npx vite-bundle-analyzer dist
```

#### Performance Optimization
- **Code Splitting**: Automatic route-based splitting implemented
- **Tree Shaking**: Unused code elimination
- **Asset Optimization**: Image compression and WebP conversion
- **Gzip Compression**: Enabled by default in production

## Netlify Deployment

### Automated Deployment Setup

1. **Repository Connection**
   - Connect GitHub repository to Netlify
   - Set branch: `main` for production
   - Configure auto-deploy on commits

2. **Build Settings**
   ```yaml
   # netlify.toml
   [build]
     command = "npm run build"
     publish = "dist"
     
   [build.environment]
     NODE_VERSION = "18"
     NPM_VERSION = "8"
   ```

3. **Deploy Configuration**
   ```yaml
   # netlify.toml continued
   [[redirects]]
     from = "/*"
     to = "/index.html"
     status = 200
     
   [[headers]]
     for = "/assets/*"
     [headers.values]
       Cache-Control = "public, max-age=31536000"
   ```

### Manual Deployment
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy to production
netlify deploy --prod --dir=dist
```

## SSL Certificate Configuration

### Automatic SSL (Recommended)
Netlify provides automatic SSL certificates:
- Enabled by default for custom domains
- Automatic renewal
- Let's Encrypt integration

### Custom SSL Certificate
For advanced configurations:
```bash
# Upload custom certificate
netlify ssl:create --cert-file=certificate.pem --key-file=private-key.pem
```

## Performance Optimization

### Critical Resource Loading
```html
<!-- Preload critical assets -->
<link rel="preload" href="/assets/critical.css" as="style">
<link rel="preload" href="/assets/hero-model.glb" as="fetch" crossorigin>

<!-- Preconnect to external services -->
<link rel="preconnect" href="https://api.ev-ar.com">
<link rel="preconnect" href="https://cdn.evmodels.com">
```

### Service Worker Configuration
```javascript
// sw.js - Auto-generated by Vite PWA plugin
import { precacheAndRoute } from 'workbox-precaching';
import { registerRoute } from 'workbox-routing';
import { CacheFirst } from 'workbox-strategies';

// Precache static assets
precacheAndRoute(self.__WB_MANIFEST);

// Cache 3D models
registerRoute(
  ({ request }) => request.destination === 'document',
  new CacheFirst({
    cacheName: '3d-models',
    plugins: [{
      cacheKeyWillBeUsed: async ({ request }) => `${request.url}?v=1`,
    }]
  })
);
```

## Monitoring & Analytics

### Performance Monitoring
```typescript
// Performance tracking setup
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

// Track Core Web Vitals
getCLS(console.log);
getFID(console.log);  
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);

// AR-specific metrics
const trackARPerformance = () => {
  performance.mark('ar-session-start');
  // ... AR initialization
  performance.mark('ar-session-ready');
  performance.measure('ar-initialization', 'ar-session-start', 'ar-session-ready');
};
```

### Error Tracking
```typescript
// Error boundary for production
import * as Sentry from '@sentry/react';

Sentry.init({
  dsn: process.env.VITE_SENTRY_DSN,
  environment: process.env.VITE_APP_ENV,
  integrations: [
    new Sentry.BrowserTracing(),
  ],
  tracesSampleRate: 0.1,
});
```

## Security Configuration

### Content Security Policy
```yaml
# netlify.toml - Security headers
[[headers]]
  for = "/*"
  [headers.values]
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://api.ev-ar.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api.ev-ar.com wss:; media-src 'self'; worker-src 'self' blob:;"
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
```

### API Security
```typescript
// API request interceptor
const api = axios.create({
  baseURL: process.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
});
```

## Testing in Production

### Smoke Tests
```bash
# Basic functionality tests
curl -f https://ev-ar.netlify.app/ || exit 1
curl -f https://ev-ar.netlify.app/showroom || exit 1
curl -f https://ev-ar.netlify.app/api/health || exit 1
```

### AR Feature Testing
```javascript
// AR compatibility check
const checkARSupport = async () => {
  if ('xr' in navigator) {
    const isSupported = await navigator.xr.isSessionSupported('immersive-ar');
    console.log('AR Support:', isSupported);
    return isSupported;
  }
  return false;
};
```

### Performance Testing
```bash
# Lighthouse CI
npm install -g @lhci/cli
lhci autorun --upload.target=temporary-public-storage
```

## Rollback Strategy

### Automatic Rollback
```yaml
# netlify.toml - Deploy contexts
[context.production]
  command = "npm run build:production"
  
[context.deploy-preview]
  command = "npm run build:preview"

[context.branch-deploy]
  command = "npm run build:staging"
```

### Manual Rollback
```bash
# Rollback to previous deployment
netlify rollback

# Rollback to specific deployment
netlify rollback --deploy-id=DEPLOY_ID
```

## Maintenance & Updates

### Automated Updates
```yaml
# .github/workflows/update-dependencies.yml
name: Update Dependencies
on:
  schedule:
    - cron: '0 0 * * 1'  # Weekly on Mondays
    
jobs:
  update:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm update
      - run: npm audit fix
      - run: npm test
```

### Health Checks
```typescript
// Health monitoring endpoint
export const healthCheck = {
  status: 'healthy',
  version: process.env.VITE_APP_VERSION,
  timestamp: new Date().toISOString(),
  checks: {
    api: await checkAPIHealth(),
    ar: await checkARSupport(),
    performance: await getPerformanceMetrics()
  }
};
```

## Troubleshooting

### Common Issues

#### AR Not Loading
```javascript
// Debug AR initialization
if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
  console.error('Camera access not supported');
}

// Check HTTPS requirement
if (location.protocol !== 'https:') {
  console.warn('AR requires HTTPS connection');
}
```

#### Build Failures
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check Node.js version compatibility
node --version  # Should be 16+
```

#### Performance Issues
```javascript
// Monitor memory usage
const trackMemory = () => {
  if ('memory' in performance) {
    console.log('Memory:', performance.memory);
  }
};

// Monitor frame rate
const trackFPS = () => {
  let frames = 0;
  let lastTime = performance.now();
  
  const countFrames = () => {
    frames++;
    const currentTime = performance.now();
    if (currentTime >= lastTime + 1000) {
      console.log('FPS:', frames);
      frames = 0;
      lastTime = currentTime;
    }
    requestAnimationFrame(countFrames);
  };
  requestAnimationFrame(countFrames);
};
```

## Support & Documentation

### Deployment Checklist
- [ ] Environment variables configured
- [ ] SSL certificate active
- [ ] Build optimization verified
- [ ] Performance metrics baseline established
- [ ] Error tracking configured
- [ ] Security headers implemented
- [ ] AR functionality tested across devices
- [ ] Rollback strategy documented

### Resources
- [Netlify Documentation](https://docs.netlify.com/)
- [WebXR Deployment Guide](https://immersive-web.github.io/webxr/)
- [Performance Best Practices](https://web.dev/performance/)
- [PWA Deployment Checklist](https://web.dev/pwa-checklist/)

---

This deployment guide ensures a production-ready EV-AR application with optimal performance, security, and reliability.