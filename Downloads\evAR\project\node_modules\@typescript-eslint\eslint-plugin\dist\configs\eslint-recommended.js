"use strict";
/**
 * This is a compatibility ruleset that:
 * - disables rules from eslint:recommended which are already handled by TypeScript.
 * - enables rules that make sense due to TS's typechecking / transpilation.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const eslint_recommended_raw_1 = __importDefault(require("./eslint-recommended-raw"));
module.exports = {
    overrides: [(0, eslint_recommended_raw_1.default)('glob')],
};
//# sourceMappingURL=eslint-recommended.js.map