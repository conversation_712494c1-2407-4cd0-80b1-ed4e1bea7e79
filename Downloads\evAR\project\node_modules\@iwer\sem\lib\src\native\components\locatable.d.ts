import { SpatialEntityComponent, SpatialEntityComponentType } from './component.js';
import { Mesh } from 'three';
import { Pose } from '../../generated/protos/openxr_core.js';
export declare class LocatableComponent extends SpatialEntityComponent {
    type: SpatialEntityComponentType;
    constructor(spatialEntity: Mesh, initData: Pose);
    get position(): import("three").Vector3;
    get rotation(): import("three").Euler;
    get orientation(): import("three").Quaternion;
    get initData(): {
        position: {
            x: number;
            y: number;
            z: number;
        };
        orientation: {
            x: number;
            y: number;
            z: number;
            w: number;
        };
    };
    get pbData(): Pose;
}
//# sourceMappingURL=locatable.d.ts.map