import { Binary<PERSON>eader, BinaryWriter } from '@bufbuild/protobuf/wire';
import { Pose, Rect2D, Rect3D } from './openxr_core.js';
export declare const protobufPackage = "openxr.scene";
export declare enum DynamicObjectTypeMETA {
    Keyboard = 0,
    UNRECOGNIZED = -1
}
export declare function dynamicObjectTypeMETAFromJSON(object: any): DynamicObjectTypeMETA;
export declare function dynamicObjectTypeMETAToJSON(object: DynamicObjectTypeMETA): string;
export declare enum SemanticLabelMETA {
    UNKNOWN = 0,
    FLOOR = 1,
    CEILING = 2,
    WALL_FACE = 3,
    TABLE = 4,
    COUCH = 5,
    DOOR_FRAME = 6,
    WINDOW_FRAME = 7,
    OTHER = 8,
    STORAGE = 9,
    BED = 10,
    SCREEN = 11,
    LAMP = 12,
    PLANT = 13,
    WALL_ART = 14,
    GLOBAL_MESH = 15,
    INVISIBLE_WALL_FACE = 16,
    CHAIR = 17,
    INNER_WALL_FACE = 18,
    OTHER_ROOM_FACE = 19,
    OPENING = 20,
    UNRECOGNIZED = -1
}
export declare function semanticLabelMETAFromJSON(object: any): SemanticLabelMETA;
export declare function semanticLabelMETAToJSON(object: SemanticLabelMETA): string;
export interface RoomLayoutMETA {
    floorUuid: string;
    ceilingUuid: string;
    wallUuids: string[];
}
export interface SpaceContainerMETA {
    uuids: string[];
}
export interface Boundary2DMETA {
    /**
     * vertices is an array of float 2d vectors [x1, y1, x2, y2, etc...]. The boundary is not closed
     * meaning that the last and first points are not the same and the line between them forms part
     * of the bounary.
     */
    vertices: Uint8Array;
}
export interface TriangleMeshMETA {
    /** vertices is an array of float 3d vectors [x1, y1, z1, x2, y2, z2, etc...] */
    vertices: Uint8Array;
    /** indices is an array of uint32 3 indices per triangle (i, j, k) [i1, j1, k1, i2, j2, j3, etc...] */
    indices: Uint8Array;
}
export interface DynamicObjectMETA {
    classType: DynamicObjectTypeMETA;
}
export interface RoomMeshMETA {
    mesh: TriangleMeshMETA | undefined;
    faces: RoomMeshMETA_RoomFace[];
}
export interface RoomMeshMETA_RoomFace {
    uuid: string;
    parentUuid: string;
    semanticLabel: SemanticLabelMETA;
}
/** The fields with _META are component definitions from the XR_FB_spatial_entity extensions */
export interface SpatialEntity {
    uuid: string;
    semanticLabel_META?: SemanticLabelMETA | undefined;
    locatable_META: Pose | undefined;
    boundary2D_META: Boundary2DMETA | undefined;
    bounded2D_META: Rect2D | undefined;
    bounded3D_META: Rect3D | undefined;
    roomLayout_META: RoomLayoutMETA | undefined;
    spaceContainer_META: SpaceContainerMETA | undefined;
    triangleMesh_META: TriangleMeshMETA | undefined;
    /** TODO: add EXT component definitions here */
    dynamicObject_META: DynamicObjectMETA | undefined;
}
export interface Scene {
    type: string;
    version: number;
    spatialEntities: SpatialEntity[];
}
export declare const RoomLayoutMETA: MessageFns<RoomLayoutMETA>;
export declare const SpaceContainerMETA: MessageFns<SpaceContainerMETA>;
export declare const Boundary2DMETA: MessageFns<Boundary2DMETA>;
export declare const TriangleMeshMETA: MessageFns<TriangleMeshMETA>;
export declare const DynamicObjectMETA: MessageFns<DynamicObjectMETA>;
export declare const RoomMeshMETA: MessageFns<RoomMeshMETA>;
export declare const RoomMeshMETA_RoomFace: MessageFns<RoomMeshMETA_RoomFace>;
export declare const SpatialEntity: MessageFns<SpatialEntity>;
export declare const Scene: MessageFns<Scene>;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P : P & {
    [K in keyof P]: Exact<P[K], I[K]>;
} & {
    [K in Exclude<keyof I, KeysOfUnion<P>>]: never;
};
export interface MessageFns<T> {
    encode(message: T, writer?: BinaryWriter): BinaryWriter;
    decode(input: BinaryReader | Uint8Array, length?: number): T;
    fromJSON(object: any): T;
    toJSON(message: T): unknown;
    create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
    fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
export {};
//# sourceMappingURL=openxr_scene.d.ts.map