{"version": 3, "sources": ["../../src/index.ts", "../../src/primitives.ts"], "sourcesContent": ["import { applyProps, addEffect } from '@react-three/fiber'\n\nimport { Globals } from '@react-spring/core'\nimport { createStringInterpolator, colors, raf } from '@react-spring/shared'\nimport { createHost } from '@react-spring/animated'\n\nimport { primitives } from './primitives'\nimport { WithAnimated } from './animated'\n\nGlobals.assign({\n  createStringInterpolator,\n  colors,\n  frameLoop: 'demand',\n})\n\n// Let r3f drive the frameloop.\naddEffect(() => {\n  raf.advance()\n})\n\nconst host = createHost(primitives, {\n  // @ts-expect-error r3f related\n  applyAnimatedValues: applyProps,\n})\n\nexport const animated = host.animated as WithAnimated\nexport { animated as a }\n\nexport * from './animated'\nexport * from '@react-spring/core'\n", "import * as THREE from 'three'\nimport '@react-three/fiber'\n\nexport type Primitives = keyof JSX.IntrinsicElements\n\nexport const primitives = ['primitive'].concat(\n  Object.keys(THREE)\n    .filter(key => /^[A-Z]/.test(key))\n    .map(key => key[0].toLowerCase() + key.slice(1))\n) as Primitives[]\n"], "mappings": "wmBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,OAAAE,EAAA,aAAAA,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAsC,8BAEtCC,EAAwB,8BACxBC,EAAsD,gCACtDC,EAA2B,kCCJ3B,IAAAC,EAAuB,oBACvBC,EAAO,8BAIMC,EAAa,CAAC,WAAW,EAAE,OACtC,OAAO,KAAKF,CAAK,EACd,OAAOG,GAAO,SAAS,KAAKA,CAAG,CAAC,EAChC,IAAIA,GAAOA,EAAI,CAAC,EAAE,YAAY,EAAIA,EAAI,MAAM,CAAC,CAAC,CACnD,EDoBAC,EAAAC,EAAc,8BA7Bd,gBASA,UAAQ,OAAO,CACb,oDACA,gBACA,UAAW,QACb,CAAC,KAGD,aAAU,IAAM,CACd,MAAI,QAAQ,CACd,CAAC,EAED,IAAMC,KAAO,cAAWC,EAAY,CAElC,oBAAqB,YACvB,CAAC,EAEYC,EAAWF,EAAK", "names": ["src_exports", "__export", "animated", "__toCommonJS", "import_fiber", "import_core", "import_shared", "import_animated", "THREE", "import_fiber", "primitives", "key", "__reExport", "src_exports", "host", "primitives", "animated"]}