"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react"),n=require("@react-three/fiber"),o=require("camera-controls");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var c=a(e),u=s(r),i=a(o);const l=r.forwardRef(((e,o)=>{r.useMemo((()=>{const e={Box3:t.Box3,MathUtils:{clamp:t.MathUtils.clamp},Matrix4:t.Matrix4,Quaternion:t.Quaternion,Raycaster:t.Raycaster,Sphere:t.Sphere,Spherical:t.Spherical,Vector2:t.Vector2,Vector3:t.Vector3,Vector4:t.Vector4};i.default.install({THREE:e}),n.extend({CameraControlsImpl:i.default})}),[]);const{camera:a,domElement:s,makeDefault:l,onStart:d,onEnd:f,onChange:m,regress:v,...E}=e,p=n.useThree((e=>e.camera)),h=n.useThree((e=>e.gl)),b=n.useThree((e=>e.invalidate)),L=n.useThree((e=>e.events)),T=n.useThree((e=>e.setEvents)),g=n.useThree((e=>e.set)),j=n.useThree((e=>e.get)),x=n.useThree((e=>e.performance)),M=a||p,y=s||L.connected||h.domElement,O=r.useMemo((()=>new i.default(M)),[M]);return n.useFrame(((e,t)=>{O.enabled&&O.update(t)}),-1),r.useEffect((()=>(O.connect(y),()=>{O.disconnect()})),[y,O]),r.useEffect((()=>{const e=e=>{b(),v&&x.regress(),m&&m(e)},t=e=>{d&&d(e)},r=e=>{f&&f(e)};return O.addEventListener("update",e),O.addEventListener("controlstart",t),O.addEventListener("controlend",r),O.addEventListener("control",e),O.addEventListener("transitionstart",e),O.addEventListener("wake",e),()=>{O.removeEventListener("update",e),O.removeEventListener("controlstart",t),O.removeEventListener("controlend",r),O.removeEventListener("control",e),O.removeEventListener("transitionstart",e),O.removeEventListener("wake",e)}}),[O,d,f,b,T,v,m]),r.useEffect((()=>{if(l){const e=j().controls;return g({controls:O}),()=>g({controls:e})}}),[l,O]),u.createElement("primitive",c.default({ref:o,object:O},E))}));exports.CameraControls=l;
