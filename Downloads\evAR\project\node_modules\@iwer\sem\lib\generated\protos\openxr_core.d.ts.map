{"version": 3, "file": "openxr_core.d.ts", "sourceRoot": "", "sources": ["../../../src/generated/protos/openxr_core.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAErE,eAAO,MAAM,eAAe,WAAW,CAAC;AAExC,MAAM,WAAW,OAAO;IACvB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACV;AAED,MAAM,WAAW,OAAO;IACvB,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,OAAO;IACvB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACV;AAED,MAAM,WAAW,OAAO;IACvB,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,UAAU;IAC1B,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACV;AAED,MAAM,WAAW,MAAM;IACtB,MAAM,EAAE,OAAO,GAAG,SAAS,CAAC;IAC5B,MAAM,EAAE,OAAO,GAAG,SAAS,CAAC;CAC5B;AAED,MAAM,WAAW,MAAM;IACtB,MAAM,EAAE,OAAO,GAAG,SAAS,CAAC;IAC5B,MAAM,EAAE,OAAO,GAAG,SAAS,CAAC;CAC5B;AAED;;;GAGG;AACH,MAAM,WAAW,GAAG;IACnB,eAAe;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,eAAe;IACf,UAAU,EAAE,MAAM,CAAC;IACnB,eAAe;IACf,OAAO,EAAE,MAAM,CAAC;IAChB,eAAe;IACf,SAAS,EAAE,MAAM,CAAC;CAClB;AAED;;;GAGG;AACH,MAAM,WAAW,IAAI;IACpB,WAAW,EAAE,UAAU,GAAG,SAAS,CAAC;IACpC,QAAQ,EAAE,OAAO,GAAG,SAAS,CAAC;CAC9B;AAMD,eAAO,MAAM,OAAO,EAAE,UAAU,CAAC,OAAO,CA0EvC,CAAC;AAMF,eAAO,MAAM,OAAO,EAAE,UAAU,CAAC,OAAO,CA0EvC,CAAC;AAMF,eAAO,MAAM,OAAO,EAAE,UAAU,CAAC,OAAO,CA0FvC,CAAC;AAMF,eAAO,MAAM,OAAO,EAAE,UAAU,CAAC,OAAO,CA0FvC,CAAC;AAMF,eAAO,MAAM,UAAU,EAAE,UAAU,CAAC,UAAU,CA4G7C,CAAC;AAMF,eAAO,MAAM,MAAM,EAAE,UAAU,CAAC,MAAM,CAoFrC,CAAC;AAMF,eAAO,MAAM,MAAM,EAAE,UAAU,CAAC,MAAM,CAoFrC,CAAC;AAMF,eAAO,MAAM,GAAG,EAAE,UAAU,CAAC,GAAG,CAgH/B,CAAC;AAMF,eAAO,MAAM,IAAI,EAAE,UAAU,CAAC,IAAI,CAoFjC,CAAC;AAEF,KAAK,OAAO,GACT,IAAI,GACJ,QAAQ,GACR,UAAU,GACV,MAAM,GACN,MAAM,GACN,OAAO,GACP,SAAS,CAAC;AAEb,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,OAAO,GAC3C,CAAC,GACD,CAAC,SAAS,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GACnC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAChC,CAAC,SAAS,aAAa,CAAC,MAAM,CAAC,CAAC,GAChC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAC7B,CAAC,SAAS,EAAE,GACZ;KAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,GACtC,OAAO,CAAC,CAAC,CAAC,CAAC;AAEd,KAAK,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC;AACpD,MAAM,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,OAAO,GAClD,CAAC,GACD,CAAC,GAAG;KAAG,CAAC,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,GAAG;KAC3C,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK;CAC7C,CAAC;AAML,MAAM,WAAW,UAAU,CAAC,CAAC;IAC5B,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,YAAY,GAAG,YAAY,CAAC;IACxD,MAAM,CAAC,KAAK,EAAE,YAAY,GAAG,UAAU,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;IAC7D,QAAQ,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;IACzB,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC;IAC5B,MAAM,CAAC,CAAC,SAAS,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACxD,WAAW,CAAC,CAAC,SAAS,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;CAC9D"}