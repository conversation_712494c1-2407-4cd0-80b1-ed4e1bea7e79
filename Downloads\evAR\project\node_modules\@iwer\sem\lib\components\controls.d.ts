/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { KeyMapType } from './mapper.js';
import React from 'react';
import { XRDevice } from 'iwer';
interface ControlsProps {
    xrDevice: XRDevice;
    keyMap: KeyMapType;
    pointerLocked: boolean;
}
export declare const ControlsUI: React.FC<ControlsProps>;
export {};
//# sourceMappingURL=controls.d.ts.map