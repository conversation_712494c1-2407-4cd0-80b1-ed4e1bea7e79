"use strict";function e(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}Object.defineProperty(exports,"__esModule",{value:!0});var t=e(require("react"));const r=e=>0===e?0:Math.pow(2,10*e-10);exports.Backdrop=function({children:e,floor:o=.25,segments:n=20,receiveShadow:c,...a}){const u=t.useRef(null);return t.useLayoutEffect((()=>{let e=0;const t=n/n/2,c=u.current.attributes.position;for(let a=0;a<n+1;a++)for(let u=0;u<n+1;u++)c.setXYZ(e++,a/n-t+(0===a?-o:0),u/n-t,r(a/n));c.needsUpdate=!0,u.current.computeVertexNormals()}),[n,o]),t.createElement("group",a,t.createElement("mesh",{receiveShadow:c,rotation:[-Math.PI/2,0,Math.PI/2]},t.createElement("planeGeometry",{ref:u,args:[1,1,n,n]}),e))};
