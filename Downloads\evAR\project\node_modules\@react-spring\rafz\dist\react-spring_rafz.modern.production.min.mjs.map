{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["import type {\n  FrameFn,\n  FrameUpdateFn,\n  NativeRaf,\n  <PERSON>fz,\n  Timeout,\n  Throttled,\n} from './types'\n\nexport type { FrameFn, FrameUpdateFn, Timeout, Throttled, Rafz }\n\nlet updateQueue = makeQueue<FrameUpdateFn>()\n\n/**\n * Schedule an update for next frame.\n * Your function can return `true` to repeat next frame.\n */\nexport const raf: Rafz = fn => schedule(fn, updateQueue)\n\nlet writeQueue = makeQueue<FrameFn>()\nraf.write = fn => schedule(fn, writeQueue)\n\nlet onStartQueue = makeQueue<FrameFn>()\nraf.onStart = fn => schedule(fn, onStartQueue)\n\nlet onFrameQueue = makeQueue<FrameFn>()\nraf.onFrame = fn => schedule(fn, onFrameQueue)\n\nlet onFinishQueue = makeQueue<FrameFn>()\nraf.onFinish = fn => schedule(fn, onFinishQueue)\n\nlet timeouts: Timeout[] = []\nraf.setTimeout = (handler, ms) => {\n  const time = raf.now() + ms\n  const cancel = () => {\n    const i = timeouts.findIndex(t => t.cancel == cancel)\n    if (~i) timeouts.splice(i, 1)\n    pendingCount -= ~i ? 1 : 0\n  }\n\n  const timeout: Timeout = { time, handler, cancel }\n  timeouts.splice(findTimeout(time), 0, timeout)\n  pendingCount += 1\n\n  start()\n  return timeout\n}\n\n/** Find the index where the given time is not greater. */\nconst findTimeout = (time: number) =>\n  ~(~timeouts.findIndex(t => t.time > time) || ~timeouts.length)\n\nraf.cancel = fn => {\n  onStartQueue.delete(fn)\n  onFrameQueue.delete(fn)\n  onFinishQueue.delete(fn)\n  updateQueue.delete(fn)\n  writeQueue.delete(fn)\n}\n\nraf.sync = fn => {\n  sync = true\n  raf.batchedUpdates(fn)\n  sync = false\n}\n\nraf.throttle = fn => {\n  let lastArgs: any\n  function queuedFn() {\n    try {\n      fn(...lastArgs)\n    } finally {\n      lastArgs = null\n    }\n  }\n  function throttled(...args: any) {\n    lastArgs = args\n    raf.onStart(queuedFn)\n  }\n  throttled.handler = fn\n  throttled.cancel = () => {\n    onStartQueue.delete(queuedFn)\n    lastArgs = null\n  }\n  return throttled as any\n}\n\nlet nativeRaf =\n  typeof window != 'undefined'\n    ? (window.requestAnimationFrame as NativeRaf)\n    : // eslint-disable-next-line @typescript-eslint/no-empty-function\n      () => {}\n\nraf.use = impl => (nativeRaf = impl)\nraf.now = typeof performance != 'undefined' ? () => performance.now() : Date.now\nraf.batchedUpdates = fn => fn()\nraf.catch = console.error\n\nraf.frameLoop = 'always'\n\nraf.advance = () => {\n  if (raf.frameLoop !== 'demand') {\n    console.warn(\n      'Cannot call the manual advancement of rafz whilst frameLoop is not set as demand'\n    )\n  } else {\n    update()\n  }\n}\n\n/** The most recent timestamp. */\nlet ts = -1\n\n/** The number of pending tasks  */\nlet pendingCount = 0\n\n/** When true, scheduling is disabled. */\nlet sync = false\n\nfunction schedule<T extends Function>(fn: T, queue: Queue<T>) {\n  if (sync) {\n    queue.delete(fn)\n    fn(0)\n  } else {\n    queue.add(fn)\n    start()\n  }\n}\n\nfunction start() {\n  if (ts < 0) {\n    ts = 0\n    if (raf.frameLoop !== 'demand') {\n      nativeRaf(loop)\n    }\n  }\n}\n\nfunction stop() {\n  ts = -1\n}\n\nfunction loop() {\n  if (~ts) {\n    nativeRaf(loop)\n    raf.batchedUpdates(update)\n  }\n}\n\nfunction update() {\n  const prevTs = ts\n  ts = raf.now()\n\n  // Flush timeouts whose time is up.\n  const count = findTimeout(ts)\n  if (count) {\n    eachSafely(timeouts.splice(0, count), t => t.handler())\n    pendingCount -= count\n  }\n\n  if (!pendingCount) {\n    stop()\n\n    return\n  }\n\n  onStartQueue.flush()\n  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667)\n  onFrameQueue.flush()\n  writeQueue.flush()\n  onFinishQueue.flush()\n}\n\ninterface Queue<T extends Function = any> {\n  add: (fn: T) => void\n  delete: (fn: T) => boolean\n  flush: (arg?: any) => void\n}\n\nfunction makeQueue<T extends Function>(): Queue<T> {\n  let next = new Set<T>()\n  let current = next\n  return {\n    add(fn) {\n      pendingCount += current == next && !next.has(fn) ? 1 : 0\n      next.add(fn)\n    },\n    delete(fn) {\n      pendingCount -= current == next && next.has(fn) ? 1 : 0\n      return next.delete(fn)\n    },\n    flush(arg) {\n      if (current.size) {\n        next = new Set()\n        pendingCount -= current.size\n        eachSafely(current, fn => fn(arg) && next.add(fn))\n        pendingCount += next.size\n        current = next\n      }\n    },\n  }\n}\n\ninterface Eachable<T> {\n  forEach(cb: (value: T) => void): void\n}\n\nfunction eachSafely<T>(values: Eachable<T>, each: (value: T) => void) {\n  values.forEach(value => {\n    try {\n      each(value)\n    } catch (e) {\n      raf.catch(e as Error)\n    }\n  })\n}\n\n/** Tree-shakable state for testing purposes */\nexport const __raf = {\n  /** The number of pending tasks */\n  count(): number {\n    return pendingCount\n  },\n  /** Whether there's a raf update loop running */\n  isRunning(): boolean {\n    return ts >= 0\n  },\n  /** Clear internal state. Never call from update loop! */\n  clear() {\n    ts = -1\n    timeouts = []\n    onStartQueue = makeQueue()\n    updateQueue = makeQueue()\n    onFrameQueue = makeQueue()\n    writeQueue = makeQueue()\n    onFinishQueue = makeQueue()\n    pendingCount = 0\n  },\n}\n"], "mappings": "AAWA,IAAIA,EAAcC,EAAyB,EAM9BC,EAAYC,GAAMC,EAASD,EAAIH,CAAW,EAEnDK,EAAaJ,EAAmB,EACpCC,EAAI,MAAQC,GAAMC,EAASD,EAAIE,CAAU,EAEzC,IAAIC,EAAeL,EAAmB,EACtCC,EAAI,QAAUC,GAAMC,EAASD,EAAIG,CAAY,EAE7C,IAAIC,EAAeN,EAAmB,EACtCC,EAAI,QAAUC,GAAMC,EAASD,EAAII,CAAY,EAE7C,IAAIC,EAAgBP,EAAmB,EACvCC,EAAI,SAAWC,GAAMC,EAASD,EAAIK,CAAa,EAE/C,IAAIC,EAAsB,CAAC,EAC3BP,EAAI,WAAa,CAACQ,EAASC,IAAO,CAChC,IAAMC,EAAOV,EAAI,IAAI,EAAIS,EACnBE,EAAS,IAAM,CACnB,IAAMC,EAAIL,EAAS,UAAUM,GAAKA,EAAE,QAAUF,CAAM,EAChD,CAACC,GAAGL,EAAS,OAAOK,EAAG,CAAC,EAC5BE,GAAgB,CAACF,EAAI,EAAI,CAC3B,EAEMG,EAAmB,CAAE,KAAAL,EAAM,QAAAF,EAAS,OAAAG,CAAO,EACjD,OAAAJ,EAAS,OAAOS,EAAYN,CAAI,EAAG,EAAGK,CAAO,EAC7CD,GAAgB,EAEhBG,EAAM,EACCF,CACT,EAGA,IAAMC,EAAeN,GACnB,EAAE,CAACH,EAAS,UAAU,GAAK,EAAE,KAAOG,CAAI,GAAK,CAACH,EAAS,QAEzDP,EAAI,OAASC,GAAM,CACjBG,EAAa,OAAOH,CAAE,EACtBI,EAAa,OAAOJ,CAAE,EACtBK,EAAc,OAAOL,CAAE,EACvBH,EAAY,OAAOG,CAAE,EACrBE,EAAW,OAAOF,CAAE,CACtB,EAEAD,EAAI,KAAOC,GAAM,CACfiB,EAAO,GACPlB,EAAI,eAAeC,CAAE,EACrBiB,EAAO,EACT,EAEAlB,EAAI,SAAWC,GAAM,CACnB,IAAIkB,EACJ,SAASC,GAAW,CAClB,GAAI,CACFnB,EAAG,GAAGkB,CAAQ,CAChB,QAAE,CACAA,EAAW,IACb,CACF,CACA,SAASE,KAAaC,EAAW,CAC/BH,EAAWG,EACXtB,EAAI,QAAQoB,CAAQ,CACtB,CACA,OAAAC,EAAU,QAAUpB,EACpBoB,EAAU,OAAS,IAAM,CACvBjB,EAAa,OAAOgB,CAAQ,EAC5BD,EAAW,IACb,EACOE,CACT,EAEA,IAAIE,EACF,OAAO,OAAU,IACZ,OAAO,sBAER,IAAM,CAAC,EAEbvB,EAAI,IAAMwB,GAASD,EAAYC,EAC/BxB,EAAI,IAAM,OAAO,YAAe,IAAc,IAAM,YAAY,IAAI,EAAI,KAAK,IAC7EA,EAAI,eAAiBC,GAAMA,EAAG,EAC9BD,EAAI,MAAQ,QAAQ,MAEpBA,EAAI,UAAY,SAEhBA,EAAI,QAAU,IAAM,CACdA,EAAI,YAAc,SACpB,QAAQ,KACN,kFACF,EAEAyB,EAAO,CAEX,EAGA,IAAIC,EAAK,GAGLZ,EAAe,EAGfI,EAAO,GAEX,SAAShB,EAA6BD,EAAO0B,EAAiB,CACxDT,GACFS,EAAM,OAAO1B,CAAE,EACfA,EAAG,CAAC,IAEJ0B,EAAM,IAAI1B,CAAE,EACZgB,EAAM,EAEV,CAEA,SAASA,GAAQ,CACXS,EAAK,IACPA,EAAK,EACD1B,EAAI,YAAc,UACpBuB,EAAUK,CAAI,EAGpB,CAEA,SAASC,GAAO,CACdH,EAAK,EACP,CAEA,SAASE,GAAO,CACV,CAACF,IACHH,EAAUK,CAAI,EACd5B,EAAI,eAAeyB,CAAM,EAE7B,CAEA,SAASA,GAAS,CAChB,IAAMK,EAASJ,EACfA,EAAK1B,EAAI,IAAI,EAGb,IAAM+B,EAAQf,EAAYU,CAAE,EAM5B,GALIK,IACFC,EAAWzB,EAAS,OAAO,EAAGwB,CAAK,EAAGlB,GAAKA,EAAE,QAAQ,CAAC,EACtDC,GAAgBiB,GAGd,CAACjB,EAAc,CACjBe,EAAK,EAEL,OAGFzB,EAAa,MAAM,EACnBN,EAAY,MAAMgC,EAAS,KAAK,IAAI,GAAIJ,EAAKI,CAAM,EAAI,MAAM,EAC7DzB,EAAa,MAAM,EACnBF,EAAW,MAAM,EACjBG,EAAc,MAAM,CACtB,CAQA,SAASP,GAA0C,CACjD,IAAIkC,EAAO,IAAI,IACXC,EAAUD,EACd,MAAO,CACL,IAAIhC,EAAI,CACNa,GAAgBoB,GAAWD,GAAQ,CAACA,EAAK,IAAIhC,CAAE,EAAI,EAAI,EACvDgC,EAAK,IAAIhC,CAAE,CACb,EACA,OAAOA,EAAI,CACT,OAAAa,GAAgBoB,GAAWD,GAAQA,EAAK,IAAIhC,CAAE,EAAI,EAAI,EAC/CgC,EAAK,OAAOhC,CAAE,CACvB,EACA,MAAMkC,EAAK,CACLD,EAAQ,OACVD,EAAO,IAAI,IACXnB,GAAgBoB,EAAQ,KACxBF,EAAWE,EAASjC,GAAMA,EAAGkC,CAAG,GAAKF,EAAK,IAAIhC,CAAE,CAAC,EACjDa,GAAgBmB,EAAK,KACrBC,EAAUD,EAEd,CACF,CACF,CAMA,SAASD,EAAcI,EAAqBC,EAA0B,CACpED,EAAO,QAAQE,GAAS,CACtB,GAAI,CACFD,EAAKC,CAAK,CACZ,OAASC,EAAP,CACAvC,EAAI,MAAMuC,CAAU,CACtB,CACF,CAAC,CACH,CAGO,IAAMC,EAAQ,CAEnB,OAAgB,CACd,OAAO1B,CACT,EAEA,WAAqB,CACnB,OAAOY,GAAM,CACf,EAEA,OAAQ,CACNA,EAAK,GACLnB,EAAW,CAAC,EACZH,EAAeL,EAAU,EACzBD,EAAcC,EAAU,EACxBM,EAAeN,EAAU,EACzBI,EAAaJ,EAAU,EACvBO,EAAgBP,EAAU,EAC1Be,EAAe,CACjB,CACF", "names": ["updateQueue", "makeQueue", "raf", "fn", "schedule", "writeQueue", "onStartQueue", "onFrameQueue", "onFinishQueue", "timeouts", "handler", "ms", "time", "cancel", "i", "t", "pendingCount", "timeout", "findTimeout", "start", "sync", "lastArgs", "queuedFn", "throttled", "args", "nativeRaf", "impl", "update", "ts", "queue", "loop", "stop", "prevTs", "count", "eachSafely", "next", "current", "arg", "values", "each", "value", "e", "__raf"]}