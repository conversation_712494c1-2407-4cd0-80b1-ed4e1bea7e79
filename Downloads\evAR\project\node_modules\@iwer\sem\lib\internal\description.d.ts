/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export type Vector2JSON = {
    x: number;
    y: number;
};
export type Vector3JSON = {
    x: number;
    y: number;
    z: number;
};
export type QuaternionJSON = {
    x: number;
    y: number;
    z: number;
    w: number;
};
export type Bounded2DJSON = {
    offset: Vector2JSON;
    extent: Vector2JSON;
};
export type Bounded3DJSON = {
    offset: Vector3JSON;
    extent: Vector3JSON;
};
export type TriangleMeshJSON = {
    vertices: Vector3JSON[];
    indices: number[];
};
export declare enum SemanticLabelJSON {
    FLOOR = "FLOOR",
    CEILING = "CEILING",
    WALL_FACE = "WALL_FACE",
    TABLE = "TABLE",
    COUCH = "COUCH",
    DOOR_FRAME = "DOOR_FRAME",
    WINDOW_FRAME = "WINDOW_FRAME",
    OTHER = "OTHER",
    STORAGE = "STORAGE",
    BED = "BED",
    SCREEN = "SCREEN",
    LAMP = "LAMP",
    PLANT = "PLANT",
    WALL_ART = "WALL_ART",
    SCENE_MESH = "SCENE_MESH",
    INVISIBLE_WALL_FACE = "INVISIBLE_WALL_FACE",
    CHAIR = "CHAIR",
    UNKNOWN = "UNKNOWN",
    INNER_WALL_FACE = "INNER_WALL_FACE",
    OTHER_ROOM_FACE = "OTHER_ROOM_FACE",
    OPENING = "OPENING"
}
export type PoseJSON = {
    position: Vector3JSON;
    orientation: QuaternionJSON;
};
export type LocatableComponentJSON = {
    locatable: PoseJSON;
};
export type Bounded2DComponentJSON = {
    bounded2D: Bounded2DJSON;
};
export type Bounded3DComponentJSON = {
    bounded3D: Bounded3DJSON;
};
export type SemanticLabelsComponentJSON = {
    semanticLabels: SemanticLabelJSON[];
};
export type TriangleMeshComponentJSON = {
    triangleMesh: TriangleMeshJSON;
};
export type SpatialEntityComponentJSON = LocatableComponentJSON | Bounded2DComponentJSON | Bounded3DComponentJSON | SemanticLabelsComponentJSON | TriangleMeshComponentJSON;
export type SpatialEntityJSON = {
    uuid: string;
    components: SpatialEntityComponentJSON[];
};
export type SceneFileJSON = {
    type: 'scene';
    version: number;
    spatialEntities: SpatialEntityJSON[];
};
export declare function isValidSceneFile(obj: any): any;
//# sourceMappingURL=description.d.ts.map