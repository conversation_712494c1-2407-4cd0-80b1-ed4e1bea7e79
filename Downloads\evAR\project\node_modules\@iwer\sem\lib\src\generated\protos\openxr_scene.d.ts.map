{"version": 3, "file": "openxr_scene.d.ts", "sourceRoot": "", "sources": ["../../../../src/generated/protos/openxr_scene.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AACrE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAExD,eAAO,MAAM,eAAe,iBAAiB,CAAC;AAE9C,oBAAY,qBAAqB;IAChC,QAAQ,IAAI;IACZ,YAAY,KAAK;CACjB;AAED,wBAAgB,6BAA6B,CAC5C,MAAM,EAAE,GAAG,GACT,qBAAqB,CAUvB;AAED,wBAAgB,2BAA2B,CAC1C,MAAM,EAAE,qBAAqB,GAC3B,MAAM,CAQR;AAED,oBAAY,iBAAiB;IAC5B,OAAO,IAAI;IACX,KAAK,IAAI;IACT,OAAO,IAAI;IACX,SAAS,IAAI;IACb,KAAK,IAAI;IACT,KAAK,IAAI;IACT,UAAU,IAAI;IACd,YAAY,IAAI;IAChB,KAAK,IAAI;IACT,OAAO,IAAI;IACX,GAAG,KAAK;IACR,MAAM,KAAK;IACX,IAAI,KAAK;IACT,KAAK,KAAK;IACV,QAAQ,KAAK;IACb,WAAW,KAAK;IAChB,mBAAmB,KAAK;IACxB,KAAK,KAAK;IACV,eAAe,KAAK;IACpB,eAAe,KAAK;IACpB,OAAO,KAAK;IACZ,YAAY,KAAK;CACjB;AAED,wBAAgB,yBAAyB,CAAC,MAAM,EAAE,GAAG,GAAG,iBAAiB,CAsExE;AAED,wBAAgB,uBAAuB,CAAC,MAAM,EAAE,iBAAiB,GAAG,MAAM,CAgDzE;AAED,MAAM,WAAW,cAAc;IAC9B,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,MAAM,EAAE,CAAC;CACpB;AAED,MAAM,WAAW,kBAAkB;IAClC,KAAK,EAAE,MAAM,EAAE,CAAC;CAChB;AAED,MAAM,WAAW,cAAc;IAC9B;;;;OAIG;IACH,QAAQ,EAAE,UAAU,CAAC;CACrB;AAED,MAAM,WAAW,gBAAgB;IAChC,gFAAgF;IAChF,QAAQ,EAAE,UAAU,CAAC;IACrB,sGAAsG;IACtG,OAAO,EAAE,UAAU,CAAC;CACpB;AAED,MAAM,WAAW,iBAAiB;IACjC,SAAS,EAAE,qBAAqB,CAAC;CACjC;AAED,MAAM,WAAW,YAAY;IAC5B,IAAI,EAAE,gBAAgB,GAAG,SAAS,CAAC;IACnC,KAAK,EAAE,qBAAqB,EAAE,CAAC;CAC/B;AAED,MAAM,WAAW,qBAAqB;IACrC,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,iBAAiB,CAAC;CACjC;AAED,+FAA+F;AAC/F,MAAM,WAAW,aAAa;IAC7B,IAAI,EAAE,MAAM,CAAC;IACb,kBAAkB,CAAC,EAAE,iBAAiB,GAAG,SAAS,CAAC;IACnD,cAAc,EAAE,IAAI,GAAG,SAAS,CAAC;IACjC,eAAe,EAAE,cAAc,GAAG,SAAS,CAAC;IAC5C,cAAc,EAAE,MAAM,GAAG,SAAS,CAAC;IACnC,cAAc,EAAE,MAAM,GAAG,SAAS,CAAC;IACnC,eAAe,EAAE,cAAc,GAAG,SAAS,CAAC;IAC5C,mBAAmB,EAAE,kBAAkB,GAAG,SAAS,CAAC;IACpD,iBAAiB,EAAE,gBAAgB,GAAG,SAAS,CAAC;IAChD,+CAA+C;IAC/C,kBAAkB,EAAE,iBAAiB,GAAG,SAAS,CAAC;CAClD;AAED,MAAM,WAAW,KAAK;IACrB,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;IAChB,eAAe,EAAE,aAAa,EAAE,CAAC;CACjC;AAMD,eAAO,MAAM,cAAc,EAAE,UAAU,CAAC,cAAc,CAoGrD,CAAC;AAMF,eAAO,MAAM,kBAAkB,EAAE,UAAU,CAAC,kBAAkB,CAmE7D,CAAC;AAMF,eAAO,MAAM,cAAc,EAAE,UAAU,CAAC,cAAc,CAgErD,CAAC;AAMF,eAAO,MAAM,gBAAgB,EAAE,UAAU,CAAC,gBAAgB,CAkFzD,CAAC;AAMF,eAAO,MAAM,iBAAiB,EAAE,UAAU,CAAC,iBAAiB,CAgE3D,CAAC;AAMF,eAAO,MAAM,YAAY,EAAE,UAAU,CAAC,YAAY,CAwFjD,CAAC;AAMF,eAAO,MAAM,qBAAqB,EAAE,UAAU,CAAC,qBAAqB,CAqGnE,CAAC;AAiBF,eAAO,MAAM,aAAa,EAAE,UAAU,CAAC,aAAa,CAiSnD,CAAC;AAMF,eAAO,MAAM,KAAK,EAAE,UAAU,CAAC,KAAK,CAiGnC,CAAC;AA2BF,KAAK,OAAO,GACT,IAAI,GACJ,QAAQ,GACR,UAAU,GACV,MAAM,GACN,MAAM,GACN,OAAO,GACP,SAAS,CAAC;AAEb,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,OAAO,GAC3C,CAAC,GACD,CAAC,SAAS,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GACnC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAChC,CAAC,SAAS,aAAa,CAAC,MAAM,CAAC,CAAC,GAChC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAC7B,CAAC,SAAS,EAAE,GACZ;KAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,GACtC,OAAO,CAAC,CAAC,CAAC,CAAC;AAEd,KAAK,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC;AACpD,MAAM,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,OAAO,GAClD,CAAC,GACD,CAAC,GAAG;KAAG,CAAC,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,GAAG;KAC3C,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK;CAC7C,CAAC;AAML,MAAM,WAAW,UAAU,CAAC,CAAC;IAC5B,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,YAAY,GAAG,YAAY,CAAC;IACxD,MAAM,CAAC,KAAK,EAAE,YAAY,GAAG,UAAU,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;IAC7D,QAAQ,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;IACzB,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC;IAC5B,MAAM,CAAC,CAAC,SAAS,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACxD,WAAW,CAAC,CAAC,SAAS,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;CAC9D"}