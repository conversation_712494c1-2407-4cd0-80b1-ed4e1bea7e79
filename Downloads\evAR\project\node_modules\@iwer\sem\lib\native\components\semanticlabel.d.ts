import { SemanticLabelMETA } from '../../generated/protos/openxr_scene.js';
import { SpatialEntityComponent, SpatialEntityComponentType } from './component.js';
import { Mesh } from 'three';
export declare class SemanticLabelComponent extends SpatialEntityComponent {
    private _semanticLabel;
    type: SpatialEntityComponentType;
    constructor(spatialEntity: Mesh, _semanticLabel: SemanticLabelMETA);
    get semanticLabel(): SemanticLabelMETA;
    set semanticLabel(value: SemanticLabelMETA);
    get initData(): SemanticLabelMETA;
    get pbData(): SemanticLabelMETA;
}
//# sourceMappingURL=semanticlabel.d.ts.map