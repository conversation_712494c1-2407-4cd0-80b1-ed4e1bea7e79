/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import React from 'react';
import { XRController } from 'iwer/lib/device/XRController';
interface AnalogButtonProps {
    xrController: XRController;
    buttonId: string;
    pointerLocked: boolean;
    mappedKey: string;
}
export declare const AnalogButton: React.FC<AnalogButtonProps>;
export {};
//# sourceMappingURL=analog.d.ts.map