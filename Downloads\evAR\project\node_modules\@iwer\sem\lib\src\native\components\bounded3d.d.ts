import { Mesh, Vector3 } from 'three';
import { SpatialEntityComponent, SpatialEntityComponentType } from './component.js';
import { Rect3D } from '../../generated/protos/openxr_core.js';
export declare class Bounded3DComponent extends SpatialEntityComponent {
    private _offset;
    private _extent;
    type: SpatialEntityComponentType;
    constructor(spatialEntity: Mesh, initData: Rect3D);
    buildGeometry(): void;
    get offset(): Vector3;
    get extent(): Vector3;
    get initData(): {
        offset: Vector3;
        extent: Vector3;
    };
    get pbData(): Rect3D;
}
//# sourceMappingURL=bounded3d.d.ts.map