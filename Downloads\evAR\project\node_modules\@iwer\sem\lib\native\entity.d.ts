import { Mesh } from 'three';
import { NativeMesh, NativePlane } from 'iwer';
import { SpatialEntity as PBSpatialEntity } from '../generated/protos/openxr_scene.js';
import { SpatialEntityComponent, SpatialEntityComponentType } from './components/component.js';
export declare enum SpatialEntityType {
    Plane = "plane",
    Box = "box",
    Mesh = "mesh"
}
export declare class SpatialEntity extends Mesh {
    name: string;
    isSpatialEntity: boolean;
    private _spatialUUID;
    private _componentMap;
    private _nativeEntity;
    constructor(uuid?: string);
    get spatialUUID(): string;
    updateMatrixWorld(force?: boolean): void;
    addComponent(componentType: SpatialEntityComponentType, initData: any): void;
    getComponent(componentType: SpatialEntityComponentType): SpatialEntityComponent | undefined;
    duplicate(): SpatialEntity;
    get nativeEntity(): NativePlane | NativeMesh | undefined;
    static fromPBJSON(json: any): SpatialEntity | undefined;
    get entityType(): SpatialEntityType | undefined;
    static toPBJSON(entity: SpatialEntity): PBSpatialEntity;
}
//# sourceMappingURL=entity.d.ts.map