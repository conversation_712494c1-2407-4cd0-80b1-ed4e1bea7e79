{"version": 3, "sources": ["../src/hooks/useChain.ts", "../src/helpers.ts", "../src/hooks/useSpring.ts", "../src/hooks/useSprings.ts", "../src/SpringValue.ts", "../src/AnimationConfig.ts", "../src/constants.ts", "../src/Animation.ts", "../src/scheduleProps.ts", "../src/runAsync.ts", "../src/AnimationResult.ts", "../src/FrameValue.ts", "../src/SpringPhase.ts", "../src/Controller.ts", "../src/SpringContext.tsx", "../src/SpringRef.ts", "../src/hooks/useSpringRef.ts", "../src/hooks/useSpringValue.ts", "../src/hooks/useTrail.ts", "../src/hooks/useTransition.tsx", "../src/hooks/useScroll.ts", "../src/hooks/useResize.ts", "../src/hooks/useInView.ts", "../src/components/Spring.tsx", "../src/components/Trail.tsx", "../src/components/Transition.tsx", "../src/interpolate.ts", "../src/Interpolation.ts", "../src/globals.ts", "../src/index.ts"], "sourcesContent": ["import { each, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { SpringRef } from '../SpringRef'\nimport { callProp } from '../helpers'\n\n/**\n * Used to orchestrate animation hooks in sequence with one another.\n * This is best used when you specifically want to orchestrate different\n * types of animation hook e.g. `useSpring` & `useTransition` in\n * sequence as opposed to multiple `useSpring` hooks.\n *\n *\n * ```jsx\n * export const MyComponent = () => {\n *  //...\n *  useChain([springRef, transitionRef])\n *  //...\n * }\n * ```\n *\n * @param refs – An array of `SpringRef`s.\n * @param timeSteps – Optional array of numbers that define the\n * delay between each animation from 0-1. The length should correlate\n * to the length of `refs`.\n * @param timeFrame – Optional number that defines the total duration\n *\n * @public\n */\nexport function useChain(\n  refs: ReadonlyArray<SpringRef>,\n  timeSteps?: number[],\n  timeFrame = 1000\n) {\n  useIsomorphicLayoutEffect(() => {\n    if (timeSteps) {\n      let prevDelay = 0\n      each(refs, (ref, i) => {\n        const controllers = ref.current\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i]\n\n          // Use the previous delay if none exists.\n          if (isNaN(delay)) delay = prevDelay\n          else prevDelay = delay\n\n          each(controllers, ctrl => {\n            each(ctrl.queue, props => {\n              // memoizing stops recursion https://github.com/pmndrs/react-spring/issues/1367\n              const memoizedDelayProp = props.delay\n              props.delay = key => delay + callProp(memoizedDelayProp || 0, key)\n            })\n          })\n\n          ref.start()\n        }\n      })\n    } else {\n      let p: Promise<any> = Promise.resolve()\n      each(refs, ref => {\n        const controllers = ref.current\n        if (controllers.length) {\n          // Take the queue of each controller\n          const queues = controllers.map(ctrl => {\n            const q = ctrl.queue\n            ctrl.queue = []\n            return q\n          })\n\n          // Apply the queue when the previous ref stops animating\n          p = p.then(() => {\n            each(controllers, (ctrl, i) =>\n              each(queues[i] || [], update => ctrl.queue.push(update))\n            )\n            return Promise.all(ref.start())\n          })\n        }\n      })\n    }\n  })\n}\n", "import {\n  is,\n  toArray,\n  eachProp,\n  getFluidValue,\n  isAnimatedString,\n  FluidValue,\n  Globals as G,\n} from '@react-spring/shared'\nimport { AnyFn, OneOrMore, Lookup } from '@react-spring/types'\nimport { ReservedProps, ForwardProps, InferTo } from './types'\nimport type { Controller } from './Controller'\nimport type { SpringRef } from './SpringRef'\n\nexport function callProp<T>(\n  value: T,\n  ...args: T extends AnyFn ? Parameters<T> : unknown[]\n): T extends AnyFn<any, infer U> ? U : T {\n  return is.fun(value) ? value(...args) : value\n}\n\n/** Try to coerce the given value into a boolean using the given key */\nexport const matchProp = (\n  value: boolean | OneOrMore<string> | ((key: any) => boolean) | undefined,\n  key: string | undefined\n) =>\n  value === true ||\n  !!(\n    key &&\n    value &&\n    (is.fun(value) ? value(key) : toArray(value).includes(key))\n  )\n\nexport const resolveProp = <T>(\n  prop: T | Lookup<T> | undefined,\n  key: string | undefined\n) => (is.obj(prop) ? key && (prop as any)[key] : prop)\n\nexport const concatFn = <T extends AnyFn>(first: T | undefined, last: T) =>\n  first ? (...args: Parameters<T>) => (first(...args), last(...args)) : last\n\n/** Returns `true` if the given prop is having its default value set. */\nexport const hasDefaultProp = <T extends Lookup>(props: T, key: keyof T) =>\n  !is.und(getDefaultProp(props, key))\n\n/** Get the default value being set for the given `key` */\nexport const getDefaultProp = <T extends Lookup, P extends keyof T>(\n  props: T,\n  key: P\n): T[P] =>\n  props.default === true\n    ? props[key]\n    : props.default\n      ? props.default[key]\n      : undefined\n\nconst noopTransform = (value: any) => value\n\n/**\n * Extract the default props from an update.\n *\n * When the `default` prop is falsy, this function still behaves as if\n * `default: true` was used. The `default` prop is always respected when\n * truthy.\n */\nexport const getDefaultProps = <T extends Lookup>(\n  props: Lookup,\n  transform: (value: any, key: string) => any = noopTransform\n): T => {\n  let keys: readonly string[] = DEFAULT_PROPS\n  if (props.default && props.default !== true) {\n    props = props.default\n    keys = Object.keys(props)\n  }\n  const defaults: any = {}\n  for (const key of keys) {\n    const value = transform(props[key], key)\n    if (!is.und(value)) {\n      defaults[key] = value\n    }\n  }\n  return defaults\n}\n\n/**\n * These props are implicitly used as defaults when defined in a\n * declarative update (eg: render-based) or any update with `default: true`.\n *\n * Use `default: {}` or `default: false` to opt-out of these implicit defaults\n * for any given update.\n *\n * Note: These are not the only props with default values. For example, the\n * `pause`, `cancel`, and `immediate` props. But those must be updated with\n * the object syntax (eg: `default: { immediate: true }`).\n */\nexport const DEFAULT_PROPS = [\n  'config',\n  'onProps',\n  'onStart',\n  'onChange',\n  'onPause',\n  'onResume',\n  'onRest',\n] as const\n\nconst RESERVED_PROPS: {\n  [key: string]: 1 | undefined\n} = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n\n  // Transition props\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n\n  // Internal props\n  keys: 1,\n  callId: 1,\n  parentId: 1,\n}\n\n/**\n * Extract any properties whose keys are *not* reserved for customizing your\n * animations. All hooks use this function, which means `useTransition` props\n * are reserved for `useSpring` calls, etc.\n */\nfunction getForwardProps<Props extends ReservedProps>(\n  props: Props\n): ForwardProps<Props> | undefined {\n  const forward: any = {}\n\n  let count = 0\n  eachProp(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value\n      count++\n    }\n  })\n\n  if (count) {\n    return forward\n  }\n}\n\n/**\n * Clone the given `props` and move all non-reserved props\n * into the `to` prop.\n */\nexport function inferTo<T extends object>(props: T): InferTo<T> {\n  const to = getForwardProps(props)\n  if (to) {\n    const out: any = { to }\n    eachProp(props, (val, key) => key in to || (out[key] = val))\n    return out\n  }\n  return { ...props } as any\n}\n\n// Compute the goal value, converting \"red\" to \"rgba(255, 0, 0, 1)\" in the process\nexport function computeGoal<T>(value: T | FluidValue<T>): T {\n  value = getFluidValue(value)\n  return is.arr(value)\n    ? value.map(computeGoal)\n    : isAnimatedString(value)\n      ? (G.createStringInterpolator({\n          range: [0, 1],\n          output: [value, value] as any,\n        })(1) as any)\n      : value\n}\n\nexport function hasProps(props: object) {\n  for (const _ in props) return true\n  return false\n}\n\nexport function isAsyncTo(to: any) {\n  return is.fun(to) || (is.arr(to) && is.obj(to[0]))\n}\n\n/** Detach `ctrl` from `ctrl.ref` and (optionally) the given `ref` */\nexport function detachRefs(ctrl: Controller, ref?: SpringRef) {\n  ctrl.ref?.delete(ctrl)\n  ref?.delete(ctrl)\n}\n\n/** Replace `ctrl.ref` with the given `ref` (if defined) */\nexport function replaceRef(ctrl: Controller, ref?: SpringRef) {\n  if (ref && ctrl.ref !== ref) {\n    ctrl.ref?.delete(ctrl)\n    ref.add(ctrl)\n    ctrl.ref = ref\n  }\n}\n", "import { Lookup, Remap } from '@react-spring/types'\nimport { is } from '@react-spring/shared'\n\nimport { ControllerUpdate, PickAnimated, SpringValues } from '../types'\nimport { Valid } from '../types/common'\nimport { SpringRef } from '../SpringRef'\nimport { useSprings } from './useSprings'\n\n/**\n * The props that `useSpring` recognizes.\n */\nexport type UseSpringProps<Props extends object = any> = unknown &\n  PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? Remap<\n        ControllerUpdate<State> & {\n          /**\n           * Used to access the imperative API.\n           *\n           * When defined, the render animation won't auto-start.\n           */\n          ref?: SpringRef<State>\n        }\n      >\n    : never\n  : never\n\n/**\n * The `props` function is only called on the first render, unless\n * `deps` change (when defined). State is inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props:\n    | Function\n    | (() => (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps),\n  deps?: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [SpringValues<State>, SpringRef<State>]\n    : never\n  : never\n\n/**\n * Updated on every render, with state inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props: (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps\n): SpringValues<PickAnimated<Props>>\n\n/**\n * Updated only when `deps` change, with state inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props: (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps,\n  deps: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [SpringValues<State>, SpringRef<State>]\n    : never\n  : never\n\n/** @internal */\nexport function useSpring(props: any, deps?: readonly any[]) {\n  const isFn = is.fun(props)\n  const [[values], ref] = useSprings(\n    1,\n    isFn ? props : [props],\n    isFn ? deps || [] : deps\n  )\n  return isFn || arguments.length == 2 ? [values, ref] : values\n}\n", "import { useContext, useMemo, useRef } from 'react'\nimport { Lookup } from '@react-spring/types'\nimport {\n  is,\n  each,\n  usePrev,\n  useOnce,\n  useForceUpdate,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\n\nimport {\n  ControllerFlushFn,\n  ControllerUpdate,\n  PickAnimated,\n  SpringValues,\n} from '../types'\nimport { UseSpringProps } from './useSpring'\nimport { declareUpdate } from '../SpringValue'\nimport {\n  Controller,\n  getSprings,\n  flushUpdateQueue,\n  setSprings,\n} from '../Controller'\nimport { hasProps, detachRefs, replaceRef } from '../helpers'\nimport { SpringContext } from '../SpringContext'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\n\nexport type UseSpringsProps<State extends Lookup = Lookup> = unknown &\n  ControllerUpdate<State> & {\n    ref?: SpringRefType<State>\n  }\n\n/**\n * When the `deps` argument exists, the `props` function is called whenever\n * the `deps` change on re-render.\n *\n * Without the `deps` argument, the `props` function is only called once.\n */\nexport function useSprings<Props extends UseSpringProps>(\n  length: number,\n  props: (i: number, ctrl: Controller) => Props,\n  deps?: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRefType<State>]\n    : never\n  : never\n\n/**\n * Animations are updated on re-render.\n */\nexport function useSprings<Props extends UseSpringsProps>(\n  length: number,\n  props: Props[] & UseSpringsProps<PickAnimated<Props>>[]\n): SpringValues<PickAnimated<Props>>[]\n\n/**\n * When the `deps` argument exists, you get the `update` and `stop` function.\n */\nexport function useSprings<Props extends UseSpringsProps>(\n  length: number,\n  props: Props[] & UseSpringsProps<PickAnimated<Props>>[],\n  deps: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRefType<State>]\n    : never\n  : never\n\n/** @internal */\nexport function useSprings(\n  length: number,\n  props: any[] | ((i: number, ctrl: Controller) => any),\n  deps?: readonly any[]\n): any {\n  const propsFn = is.fun(props) && props\n  if (propsFn && !deps) deps = []\n\n  // Create a local ref if a props function or deps array is ever passed.\n  const ref = useMemo(\n    () => (propsFn || arguments.length == 3 ? SpringRef() : void 0),\n    []\n  )\n\n  interface State {\n    // The controllers used for applying updates.\n    ctrls: Controller[]\n    // The queue of changes to make on commit.\n    queue: Array<() => void>\n    // The flush function used by controllers.\n    flush: ControllerFlushFn\n  }\n\n  // Set to 0 to prevent sync flush.\n  const layoutId = useRef(0)\n  const forceUpdate = useForceUpdate()\n\n  // State is updated on commit.\n  const state = useMemo(\n    (): State => ({\n      ctrls: [],\n      queue: [],\n      flush(ctrl, updates) {\n        const springs = getSprings(ctrl, updates)\n\n        // Flushing is postponed until the component's commit phase\n        // if a spring was created since the last commit.\n        const canFlushSync =\n          layoutId.current > 0 &&\n          !state.queue.length &&\n          !Object.keys(springs).some(key => !ctrl.springs[key])\n\n        return canFlushSync\n          ? flushUpdateQueue(ctrl, updates)\n          : new Promise<any>(resolve => {\n              setSprings(ctrl, springs)\n              state.queue.push(() => {\n                resolve(flushUpdateQueue(ctrl, updates))\n              })\n              forceUpdate()\n            })\n      },\n    }),\n    []\n  )\n\n  const ctrls = useRef([...state.ctrls])\n  const updates: any[] = []\n\n  // Cache old controllers to dispose in the commit phase.\n  const prevLength = usePrev(length) || 0\n\n  // Create new controllers when \"length\" increases, and destroy\n  // the affected controllers when \"length\" decreases.\n  useMemo(() => {\n    // Clean up any unused controllers\n    each(ctrls.current.slice(length, prevLength), ctrl => {\n      detachRefs(ctrl, ref)\n      ctrl.stop(true)\n    })\n    ctrls.current.length = length\n\n    declareUpdates(prevLength, length)\n  }, [length])\n\n  // Update existing controllers when \"deps\" are changed.\n  useMemo(() => {\n    declareUpdates(0, Math.min(prevLength, length))\n  }, deps)\n\n  /** Fill the `updates` array with declarative updates for the given index range. */\n  function declareUpdates(startIndex: number, endIndex: number) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl =\n        ctrls.current[i] ||\n        (ctrls.current[i] = new Controller(null, state.flush))\n\n      const update: UseSpringProps<any> = propsFn\n        ? propsFn(i, ctrl)\n        : (props as any)[i]\n\n      if (update) {\n        updates[i] = declareUpdate(update)\n      }\n    }\n  }\n\n  // New springs are created during render so users can pass them to\n  // their animated components, but new springs aren't cached until the\n  // commit phase (see the `useIsomorphicLayoutEffect` callback below).\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]))\n\n  const context = useContext(SpringContext)\n  const prevContext = usePrev(context)\n  const hasContext = context !== prevContext && hasProps(context)\n\n  useIsomorphicLayoutEffect(() => {\n    layoutId.current++\n\n    // Replace the cached controllers.\n    state.ctrls = ctrls.current\n\n    // Flush the commit queue.\n    const { queue } = state\n    if (queue.length) {\n      state.queue = []\n      each(queue, cb => cb())\n    }\n\n    // Update existing controllers.\n    each(ctrls.current, (ctrl, i) => {\n      // Attach the controller to the local ref.\n      ref?.add(ctrl)\n\n      // Update the default props.\n      if (hasContext) {\n        ctrl.start({ default: context })\n      }\n\n      // Apply updates created during render.\n      const update = updates[i]\n      if (update) {\n        // Update the injected ref if needed.\n        replaceRef(ctrl, update.ref)\n\n        // When an injected ref exists, the update is postponed\n        // until the ref has its `start` method called.\n        if (ctrl.ref) {\n          ctrl.queue.push(update)\n        } else {\n          ctrl.start(update)\n        }\n      }\n    })\n  })\n\n  // Cancel the animations of all controllers on unmount.\n  useOnce(() => () => {\n    each(state.ctrls, ctrl => ctrl.stop(true))\n  })\n\n  // Return a deep copy of the `springs` array so the caller can\n  // safely mutate it during render.\n  const values = springs.map(x => ({ ...x }))\n\n  return ref ? [values, ref] : values\n}\n", "import {\n  is,\n  raf,\n  each,\n  isEqual,\n  toArray,\n  eachProp,\n  frameLoop,\n  flushCalls,\n  getFluidValue,\n  isAnimatedString,\n  FluidValue,\n  Globals as G,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver,\n  getFluidObservers,\n} from '@react-spring/shared'\nimport {\n  Animated,\n  AnimatedValue,\n  AnimatedString,\n  getPayload,\n  getAnimated,\n  setAnimated,\n  getAnimatedType,\n} from '@react-spring/animated'\nimport { Lookup } from '@react-spring/types'\n\nimport { Animation } from './Animation'\nimport { mergeConfig } from './AnimationConfig'\nimport { scheduleProps } from './scheduleProps'\nimport { runAsync, RunAsyncState, RunAsyncProps, stopAsync } from './runAsync'\nimport {\n  callProp,\n  computeGoal,\n  matchProp,\n  inferTo,\n  getDefaultProps,\n  getDefaultProp,\n  isAsyncTo,\n  resolveProp,\n} from './helpers'\nimport { FrameValue, isFrameValue } from './FrameValue'\nimport {\n  isAnimating,\n  isPaused,\n  setPausedBit,\n  hasAnimated,\n  setActiveBit,\n} from './SpringPhase'\nimport {\n  AnimationRange,\n  AnimationResolver,\n  EventKey,\n  PickEventFns,\n} from './types/internal'\nimport { AsyncResult, SpringUpdate, VelocityProp, SpringProps } from './types'\nimport {\n  getCombinedResult,\n  getCancelledResult,\n  getFinishedResult,\n  getNoopResult,\n} from './AnimationResult'\n\ndeclare const console: any\n\ninterface DefaultSpringProps<T>\n  extends Pick<SpringProps<T>, 'pause' | 'cancel' | 'immediate' | 'config'>,\n    PickEventFns<SpringProps<T>> {}\n\n/**\n * Only numbers, strings, and arrays of numbers/strings are supported.\n * Non-animatable strings are also supported.\n */\nexport class SpringValue<T = any> extends FrameValue<T> {\n  /** The property name used when `to` or `from` is an object. Useful when debugging too. */\n  key?: string\n\n  /** The animation state */\n  animation = new Animation<T>()\n\n  /** The queue of pending props */\n  queue?: SpringUpdate<T>[]\n\n  /** Some props have customizable default values */\n  defaultProps: DefaultSpringProps<T> = {}\n\n  /** The state for `runAsync` calls */\n  protected _state: RunAsyncState<SpringValue<T>> = {\n    paused: false,\n    delayed: false,\n    pauseQueue: new Set(),\n    resumeQueue: new Set(),\n    timeouts: new Set(),\n  }\n\n  /** The promise resolvers of pending `start` calls */\n  protected _pendingCalls = new Set<AnimationResolver<this>>()\n\n  /** The counter for tracking `scheduleProps` calls */\n  protected _lastCallId = 0\n\n  /** The last `scheduleProps` call that changed the `to` prop */\n  protected _lastToId = 0\n\n  protected _memoizedDuration = 0\n\n  constructor(from: Exclude<T, object>, props?: SpringUpdate<T>)\n  constructor(props?: SpringUpdate<T>)\n  constructor(arg1?: any, arg2?: any) {\n    super()\n    if (!is.und(arg1) || !is.und(arg2)) {\n      const props = is.obj(arg1) ? { ...arg1 } : { ...arg2, from: arg1 }\n      if (is.und(props.default)) {\n        props.default = true\n      }\n      this.start(props)\n    }\n  }\n\n  /** Equals true when not advancing on each frame. */\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this)\n  }\n\n  get goal() {\n    return getFluidValue(this.animation.to) as T\n  }\n\n  get velocity(): VelocityProp<T> {\n    const node = getAnimated(this)!\n    return (\n      node instanceof AnimatedValue\n        ? node.lastVelocity || 0\n        : node.getPayload().map(node => node.lastVelocity || 0)\n    ) as any\n  }\n\n  /**\n   * When true, this value has been animated at least once.\n   */\n  get hasAnimated() {\n    return hasAnimated(this)\n  }\n\n  /**\n   * When true, this value has an unfinished animation,\n   * which is either active or paused.\n   */\n  get isAnimating() {\n    return isAnimating(this)\n  }\n\n  /**\n   * When true, all current and future animations are paused.\n   */\n  get isPaused() {\n    return isPaused(this)\n  }\n\n  /**\n   *\n   *\n   */\n  get isDelayed() {\n    return this._state.delayed\n  }\n\n  /** Advance the current animation by a number of milliseconds */\n  advance(dt: number) {\n    let idle = true\n    let changed = false\n\n    const anim = this.animation\n    let { toValues } = anim\n    const { config } = anim\n\n    const payload = getPayload(anim.to)\n    if (!payload && hasFluidValue(anim.to)) {\n      toValues = toArray(getFluidValue(anim.to)) as any\n    }\n\n    anim.values.forEach((node, i) => {\n      if (node.done) return\n\n      const to =\n        // Animated strings always go from 0 to 1.\n        node.constructor == AnimatedString\n          ? 1\n          : payload\n            ? payload[i].lastPosition\n            : toValues![i]\n\n      let finished = anim.immediate\n      let position = to\n\n      if (!finished) {\n        position = node.lastPosition\n\n        // Loose springs never move.\n        if (config.tension <= 0) {\n          node.done = true\n          return\n        }\n\n        let elapsed = (node.elapsedTime += dt)\n        const from = anim.fromValues[i]\n\n        const v0 =\n          node.v0 != null\n            ? node.v0\n            : (node.v0 = is.arr(config.velocity)\n                ? config.velocity[i]\n                : config.velocity)\n\n        let velocity: number\n\n        /** The smallest distance from a value before being treated like said value. */\n        /**\n         * TODO: make this value ~0.0001 by default in next breaking change\n         * for more info see – https://github.com/pmndrs/react-spring/issues/1389\n         */\n        const precision =\n          config.precision ||\n          (from == to ? 0.005 : Math.min(1, Math.abs(to - from) * 0.001))\n\n        // Duration easing\n        if (!is.und(config.duration)) {\n          let p = 1\n          if (config.duration > 0) {\n            /**\n             * Here we check if the duration has changed in the config\n             * and if so update the elapsed time to the percentage\n             * of completition so there is no jank in the animation\n             * https://github.com/pmndrs/react-spring/issues/1163\n             */\n            if (this._memoizedDuration !== config.duration) {\n              // update the memoized version to the new duration\n              this._memoizedDuration = config.duration\n\n              // if the value has started animating we need to update it\n              if (node.durationProgress > 0) {\n                // set elapsed time to be the same percentage of progress as the previous duration\n                node.elapsedTime = config.duration * node.durationProgress\n                // add the delta so the below updates work as expected\n                elapsed = node.elapsedTime += dt\n              }\n            }\n\n            // calculate the new progress\n            p = (config.progress || 0) + elapsed / this._memoizedDuration\n            // p is clamped between 0-1\n            p = p > 1 ? 1 : p < 0 ? 0 : p\n            // store our new progress\n            node.durationProgress = p\n          }\n\n          position = from + config.easing(p) * (to - from)\n          velocity = (position - node.lastPosition) / dt\n\n          finished = p == 1\n        }\n\n        // Decay easing\n        else if (config.decay) {\n          const decay = config.decay === true ? 0.998 : config.decay\n          const e = Math.exp(-(1 - decay) * elapsed)\n\n          position = from + (v0 / (1 - decay)) * (1 - e)\n          finished = Math.abs(node.lastPosition - position) <= precision\n\n          // derivative of position\n          velocity = v0 * e\n        }\n\n        // Spring easing\n        else {\n          velocity = node.lastVelocity == null ? v0 : node.lastVelocity\n\n          /** The velocity at which movement is essentially none */\n          const restVelocity = config.restVelocity || precision / 10\n\n          // Bouncing is opt-in (not to be confused with overshooting)\n          const bounceFactor = config.clamp ? 0 : config.bounce!\n          const canBounce = !is.und(bounceFactor)\n\n          /** When `true`, the value is increasing over time */\n          const isGrowing = from == to ? node.v0 > 0 : from < to\n\n          /** When `true`, the velocity is considered moving */\n          let isMoving!: boolean\n\n          /** When `true`, the velocity is being deflected or clamped */\n          let isBouncing = false\n\n          const step = 1 // 1ms\n          const numSteps = Math.ceil(dt / step)\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity\n\n            if (!isMoving) {\n              finished = Math.abs(to - position) <= precision\n              if (finished) {\n                break\n              }\n            }\n\n            if (canBounce) {\n              isBouncing = position == to || position > to == isGrowing\n\n              // Invert the velocity with a magnitude, or clamp it.\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor\n                position = to\n              }\n            }\n\n            const springForce = -config.tension * 0.000001 * (position - to)\n            const dampingForce = -config.friction * 0.001 * velocity\n            const acceleration = (springForce + dampingForce) / config.mass // pt/ms^2\n\n            velocity = velocity + acceleration * step // pt/ms\n            position = position + velocity * step\n          }\n        }\n\n        node.lastVelocity = velocity\n\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this)\n          finished = true\n        }\n      }\n\n      // Parent springs must finish before their children can.\n      if (payload && !payload[i].done) {\n        finished = false\n      }\n\n      if (finished) {\n        node.done = true\n      } else {\n        idle = false\n      }\n\n      if (node.setValue(position, config.round)) {\n        changed = true\n      }\n    })\n\n    const node = getAnimated(this)!\n    /**\n     * Get the node's current value, this will be different\n     * to anim.to when config.decay is true\n     */\n    const currVal = node.getValue()\n    if (idle) {\n      // get our final fluid val from the anim.to\n      const finalVal = getFluidValue(anim.to)\n      /**\n       * check if they're not equal, or if they're\n       * change and if there's no config.decay set\n       */\n      if ((currVal !== finalVal || changed) && !config.decay) {\n        // set the value to anim.to\n        node.setValue(finalVal)\n        this._onChange(finalVal)\n      } else if (changed && config.decay) {\n        /**\n         * if it's changed but there is a config.decay,\n         * just call _onChange with currrent value\n         */\n        this._onChange(currVal)\n      }\n      // call stop because the spring has stopped.\n      this._stop()\n    } else if (changed) {\n      /**\n       * if the spring has changed, but is not idle,\n       * just call the _onChange handler\n       */\n      this._onChange(currVal)\n    }\n  }\n\n  /** Set the current value, while stopping the current animation */\n  set(value: T | FluidValue<T>) {\n    raf.batchedUpdates(() => {\n      this._stop()\n\n      // These override the current value and goal value that may have\n      // been updated by `onRest` handlers in the `_stop` call above.\n      this._focus(value)\n      this._set(value)\n    })\n    return this\n  }\n\n  /**\n   * Freeze the active animation in time, as well as any updates merged\n   * before `resume` is called.\n   */\n  pause() {\n    this._update({ pause: true })\n  }\n\n  /** Resume the animation if paused. */\n  resume() {\n    this._update({ pause: false })\n  }\n\n  /** Skip to the end of the current animation. */\n  finish() {\n    if (isAnimating(this)) {\n      const { to, config } = this.animation\n      raf.batchedUpdates(() => {\n        // Ensure the \"onStart\" and \"onRest\" props are called.\n        this._onStart()\n\n        // Jump to the goal value, except for decay animations\n        // which have an undefined goal value.\n        if (!config.decay) {\n          this._set(to, false)\n        }\n\n        this._stop()\n      })\n    }\n    return this\n  }\n\n  /** Push props into the pending queue. */\n  update(props: SpringUpdate<T>) {\n    const queue = this.queue || (this.queue = [])\n    queue.push(props)\n    return this\n  }\n\n  /**\n   * Update this value's animation using the queue of pending props,\n   * and unpause the current animation (if one is frozen).\n   *\n   * When arguments are passed, a new animation is created, and the\n   * queued animations are left alone.\n   */\n  start(): AsyncResult<this>\n\n  start(props: SpringUpdate<T>): AsyncResult<this>\n\n  start(to: T, props?: SpringProps<T>): AsyncResult<this>\n\n  start(to?: any, arg2?: any) {\n    let queue: SpringUpdate<T>[]\n    if (!is.und(to)) {\n      queue = [is.obj(to) ? to : { ...arg2, to }]\n    } else {\n      queue = this.queue || []\n      this.queue = []\n    }\n\n    return Promise.all(\n      queue.map(props => {\n        const up = this._update(props)\n        return up\n      })\n    ).then(results => getCombinedResult(this, results))\n  }\n\n  /**\n   * Stop the current animation, and cancel any delayed updates.\n   *\n   * Pass `true` to call `onRest` with `cancelled: true`.\n   */\n  stop(cancel?: boolean) {\n    const { to } = this.animation\n\n    // The current value becomes the goal value.\n    this._focus(this.get())\n\n    stopAsync(this._state, cancel && this._lastCallId)\n    raf.batchedUpdates(() => this._stop(to, cancel))\n\n    return this\n  }\n\n  /** Restart the animation. */\n  reset() {\n    this._update({ reset: true })\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    if (event.type == 'change') {\n      this._start()\n    } else if (event.type == 'priority') {\n      this.priority = event.priority + 1\n    }\n  }\n\n  /**\n   * Parse the `to` and `from` range from the given `props` object.\n   *\n   * This also ensures the initial value is available to animated components\n   * during the render phase.\n   */\n  protected _prepareNode(props: {\n    to?: any\n    from?: any\n    reverse?: boolean\n    default?: any\n  }) {\n    const key = this.key || ''\n\n    let { to, from } = props\n\n    to = is.obj(to) ? to[key] : to\n    if (to == null || isAsyncTo(to)) {\n      to = undefined\n    }\n\n    from = is.obj(from) ? from[key] : from\n    if (from == null) {\n      from = undefined\n    }\n\n    // Create the range now to avoid \"reverse\" logic.\n    const range = { to, from }\n\n    // Before ever animating, this method ensures an `Animated` node\n    // exists and keeps its value in sync with the \"from\" prop.\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to, from] = [from, to]\n\n      from = getFluidValue(from)\n      if (!is.und(from)) {\n        this._set(from)\n      }\n      // Use the \"to\" value if our node is undefined.\n      else if (!getAnimated(this)) {\n        this._set(to)\n      }\n    }\n\n    return range\n  }\n\n  /** Every update is processed by this method before merging. */\n  protected _update(\n    { ...props }: SpringProps<T>,\n    isLoop?: boolean\n  ): AsyncResult<SpringValue<T>> {\n    const { key, defaultProps } = this\n\n    // Update the default props immediately.\n    if (props.default)\n      Object.assign(\n        defaultProps,\n        getDefaultProps(props, (value, prop) =>\n          /^on/.test(prop) ? resolveProp(value, key) : value\n        )\n      )\n\n    mergeActiveFn(this, props, 'onProps')\n    sendEvent(this, 'onProps', props, this)\n\n    // Ensure the initial value can be accessed by animated components.\n    const range = this._prepareNode(props)\n\n    if (Object.isFrozen(this)) {\n      throw Error(\n        'Cannot animate a `SpringValue` object that is frozen. ' +\n          'Did you forget to pass your component to `animated(...)` before animating its props?'\n      )\n    }\n\n    const state = this._state\n\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true)\n            flushCalls(state.pauseQueue)\n            sendEvent(\n              this,\n              'onPause',\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            )\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false)\n            if (isAnimating(this)) {\n              this._resume()\n            }\n            flushCalls(state.resumeQueue)\n            sendEvent(\n              this,\n              'onResume',\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            )\n          }\n        },\n        start: this._merge.bind(this, range),\n      },\n    }).then(result => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props)\n        if (nextProps) {\n          return this._update(nextProps, true)\n        }\n      }\n      return result\n    })\n  }\n\n  /** Merge props into the current animation */\n  protected _merge(\n    range: AnimationRange<T>,\n    props: RunAsyncProps<SpringValue<T>>,\n    resolve: AnimationResolver<SpringValue<T>>\n  ): void {\n    // The \"cancel\" prop cancels all pending delays and it forces the\n    // active animation to stop where it is.\n    if (props.cancel) {\n      this.stop(true)\n      return resolve(getCancelledResult(this))\n    }\n\n    /** The \"to\" prop is defined. */\n    const hasToProp = !is.und(range.to)\n\n    /** The \"from\" prop is defined. */\n    const hasFromProp = !is.und(range.from)\n\n    // Avoid merging other props if implicitly prevented, except\n    // when both the \"to\" and \"from\" props are undefined.\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId\n      } else {\n        return resolve(getCancelledResult(this))\n      }\n    }\n\n    const { key, defaultProps, animation: anim } = this\n    const { to: prevTo, from: prevFrom } = anim\n    let { to = prevTo, from = prevFrom } = range\n\n    // Focus the \"from\" value if changing without a \"to\" value.\n    // For default updates, do this only if no \"to\" value exists.\n    if (hasFromProp && !hasToProp && (!props.default || is.und(to))) {\n      to = from\n    }\n\n    // Flip the current range if \"reverse\" is true.\n    if (props.reverse) [to, from] = [from, to]\n\n    /** The \"from\" value is changing. */\n    const hasFromChanged = !isEqual(from, prevFrom)\n\n    if (hasFromChanged) {\n      anim.from = from\n    }\n\n    // Coerce \"from\" into a static value.\n    from = getFluidValue(from)\n\n    /** The \"to\" value is changing. */\n    const hasToChanged = !isEqual(to, prevTo)\n\n    if (hasToChanged) {\n      this._focus(to)\n    }\n\n    /** The \"to\" prop is async. */\n    const hasAsyncTo = isAsyncTo(props.to)\n\n    const { config } = anim\n    const { decay, velocity } = config\n\n    // Reset to default velocity when goal values are defined.\n    if (hasToProp || hasFromProp) {\n      config.velocity = 0\n    }\n\n    // The \"runAsync\" function treats the \"config\" prop as a default,\n    // so we must avoid merging it when the \"to\" prop is async.\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(\n        config,\n        callProp(props.config, key!),\n        // Avoid calling the same \"config\" prop twice.\n        props.config !== defaultProps.config\n          ? callProp(defaultProps.config, key!)\n          : void 0\n      )\n    }\n\n    // This instance might not have its Animated node yet. For example,\n    // the constructor can be given props without a \"to\" or \"from\" value.\n    let node = getAnimated(this)\n    if (!node || is.und(to)) {\n      return resolve(getFinishedResult(this, true))\n    }\n\n    /** When true, start at the \"from\" value. */\n    const reset =\n      // When `reset` is undefined, the `from` prop implies `reset: true`,\n      // except for declarative updates. When `reset` is defined, there\n      // must exist a value to animate from.\n      is.und(props.reset)\n        ? hasFromProp && !props.default\n        : !is.und(from) && matchProp(props.reset, key)\n\n    // The current value, where the animation starts from.\n    const value = reset ? (from as T) : this.get()\n\n    // The animation ends at this value, unless \"to\" is fluid.\n    const goal = computeGoal<any>(to)\n\n    // Only specific types can be animated to/from.\n    const isAnimatable = is.num(goal) || is.arr(goal) || isAnimatedString(goal)\n\n    // When true, the value changes instantly on the next frame.\n    const immediate =\n      !hasAsyncTo &&\n      (!isAnimatable ||\n        matchProp(defaultProps.immediate || props.immediate, key))\n\n    if (hasToChanged) {\n      const nodeType = getAnimatedType(to)\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal)!\n        } else\n          throw Error(\n            `Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`\n          )\n      }\n    }\n\n    // The type of Animated node for the goal value.\n    const goalType = node.constructor\n\n    // When the goal value is fluid, we don't know if its value\n    // will change before the next animation frame, so it always\n    // starts the animation to be safe.\n    let started = hasFluidValue(to)\n    let finished = false\n\n    if (!started) {\n      // When true, the current value has probably changed.\n      const hasValueChanged = reset || (!hasAnimated(this) && hasFromChanged)\n\n      // When the \"to\" value or current value are changed,\n      // start animating if not already finished.\n      if (hasToChanged || hasValueChanged) {\n        finished = isEqual(computeGoal(value), goal)\n        started = !finished\n      }\n\n      // Changing \"decay\" or \"velocity\" starts the animation.\n      if (\n        (!isEqual(anim.immediate, immediate) && !immediate) ||\n        !isEqual(config.decay, decay) ||\n        !isEqual(config.velocity, velocity)\n      ) {\n        started = true\n      }\n    }\n\n    // Was the goal value set to the current value while animating?\n    if (finished && isAnimating(this)) {\n      // If the first frame has passed, allow the animation to\n      // overshoot instead of stopping abruptly.\n      if (anim.changed && !reset) {\n        started = true\n      }\n      // Stop the animation before its first frame.\n      else if (!started) {\n        this._stop(prevTo)\n      }\n    }\n\n    if (!hasAsyncTo) {\n      // Make sure our \"toValues\" are updated even if our previous\n      // \"to\" prop is a fluid value whose current value is also ours.\n      if (started || hasFluidValue(prevTo)) {\n        anim.values = node.getPayload()\n        anim.toValues = hasFluidValue(to)\n          ? null\n          : goalType == AnimatedString\n            ? [1]\n            : toArray(goal)\n      }\n\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate\n\n        // Ensure the immediate goal is used as from value.\n        if (!immediate && !reset) {\n          this._set(prevTo)\n        }\n      }\n\n      if (started) {\n        const { onRest } = anim\n\n        // Set the active handlers when an animation starts.\n        each(ACTIVE_EVENTS, type => mergeActiveFn(this, props, type))\n\n        const result = getFinishedResult(this, checkFinished(this, prevTo))\n        flushCalls(this._pendingCalls, result)\n        this._pendingCalls.add(resolve)\n\n        if (anim.changed)\n          raf.batchedUpdates(() => {\n            // Ensure `onStart` can be called after a reset.\n            anim.changed = !reset\n\n            // Call the active `onRest` handler from the interrupted animation.\n            onRest?.(result, this)\n\n            // Notify the default `onRest` of the reset, but wait for the\n            // first frame to pass before sending an `onStart` event.\n            if (reset) {\n              callProp(defaultProps.onRest, result)\n            }\n            // Call the active `onStart` handler here since the first frame\n            // has already passed, which means this is a goal update and not\n            // an entirely new animation.\n            else {\n              anim.onStart?.(result, this)\n            }\n          })\n      }\n    }\n\n    if (reset) {\n      this._set(value)\n    }\n\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this))\n    }\n\n    // Start an animation\n    else if (started) {\n      this._start()\n    }\n\n    // Postpone promise resolution until the animation is finished,\n    // so that no-op updates still resolve at the expected time.\n    else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve)\n    }\n\n    // Resolve our promise immediately.\n    else {\n      resolve(getNoopResult(value))\n    }\n  }\n\n  /** Update the `animation.to` value, which might be a `FluidValue` */\n  protected _focus(value: T | FluidValue<T>) {\n    const anim = this.animation\n    if (value !== anim.to) {\n      if (getFluidObservers(this)) {\n        this._detach()\n      }\n      anim.to = value\n      if (getFluidObservers(this)) {\n        this._attach()\n      }\n    }\n  }\n\n  protected _attach() {\n    let priority = 0\n\n    const { to } = this.animation\n    if (hasFluidValue(to)) {\n      addFluidObserver(to, this)\n      if (isFrameValue(to)) {\n        priority = to.priority + 1\n      }\n    }\n\n    this.priority = priority\n  }\n\n  protected _detach() {\n    const { to } = this.animation\n    if (hasFluidValue(to)) {\n      removeFluidObserver(to, this)\n    }\n  }\n\n  /**\n   * Update the current value from outside the frameloop,\n   * and return the `Animated` node.\n   */\n  protected _set(arg: T | FluidValue<T>, idle = true): Animated | undefined {\n    const value = getFluidValue(arg)\n    if (!is.und(value)) {\n      const oldNode = getAnimated(this)\n      if (!oldNode || !isEqual(value, oldNode.getValue())) {\n        // Create a new node or update the existing node.\n        const nodeType = getAnimatedType(value)\n        if (!oldNode || oldNode.constructor != nodeType) {\n          setAnimated(this, nodeType.create(value))\n        } else {\n          oldNode.setValue(value)\n        }\n        // Never emit a \"change\" event for the initial value.\n        if (oldNode) {\n          raf.batchedUpdates(() => {\n            this._onChange(value, idle)\n          })\n        }\n      }\n    }\n    return getAnimated(this)\n  }\n\n  protected _onStart() {\n    const anim = this.animation\n    if (!anim.changed) {\n      anim.changed = true\n      sendEvent(\n        this,\n        'onStart',\n        getFinishedResult(this, checkFinished(this, anim.to)),\n        this\n      )\n    }\n  }\n\n  protected _onChange(value: T, idle?: boolean) {\n    if (!idle) {\n      this._onStart()\n      callProp(this.animation.onChange, value, this)\n    }\n    callProp(this.defaultProps.onChange, value, this)\n    super._onChange(value, idle)\n  }\n\n  // This method resets the animation state (even if already animating) to\n  // ensure the latest from/to range is used, and it also ensures this spring\n  // is added to the frameloop.\n  protected _start() {\n    const anim = this.animation\n\n    // Reset the state of each Animated node.\n    getAnimated(this)!.reset(getFluidValue(anim.to))\n\n    // Use the current values as the from values.\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map(node => node.lastPosition)\n    }\n\n    if (!isAnimating(this)) {\n      setActiveBit(this, true)\n      if (!isPaused(this)) {\n        this._resume()\n      }\n    }\n  }\n\n  protected _resume() {\n    // The \"skipAnimation\" global avoids the frameloop.\n    if (G.skipAnimation) {\n      this.finish()\n    } else {\n      frameLoop.start(this)\n    }\n  }\n\n  /**\n   * Exit the frameloop and notify `onRest` listeners.\n   *\n   * Always wrap `_stop` calls with `batchedUpdates`.\n   */\n  protected _stop(goal?: any, cancel?: boolean) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false)\n\n      const anim = this.animation\n      each(anim.values, node => {\n        node.done = true\n      })\n\n      // These active handlers must be reset to undefined or else\n      // they could be called while idle. But keep them defined\n      // when the goal value is dynamic.\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = undefined\n      }\n\n      callFluidObservers(this, {\n        type: 'idle',\n        parent: this,\n      })\n\n      const result = cancel\n        ? getCancelledResult(this.get())\n        : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to))\n\n      flushCalls(this._pendingCalls, result)\n      if (anim.changed) {\n        anim.changed = false\n        sendEvent(this, 'onRest', result, this)\n      }\n    }\n  }\n}\n\n/** Returns true when the current value and goal value are equal. */\nfunction checkFinished<T>(target: SpringValue<T>, to: T | FluidValue<T>) {\n  const goal = computeGoal(to)\n  const value = computeGoal(target.get())\n  return isEqual(value, goal)\n}\n\nexport function createLoopUpdate<T>(\n  props: T & { loop?: any; to?: any; from?: any; reverse?: any },\n  loop = props.loop,\n  to = props.to\n): T | undefined {\n  const loopRet = callProp(loop)\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet)\n    const reverse = (overrides || props).reverse\n    const reset = !overrides || overrides.reset\n    return createUpdate({\n      ...props,\n      loop,\n\n      // Avoid updating default props when looping.\n      default: false,\n\n      // Never loop the `pause` prop.\n      pause: undefined,\n\n      // For the \"reverse\" prop to loop as expected, the \"to\" prop\n      // must be undefined. The \"reverse\" prop is ignored when the\n      // \"to\" prop is an array or function.\n      to: !reverse || isAsyncTo(to) ? to : undefined,\n\n      // Ignore the \"from\" prop except on reset.\n      from: reset ? props.from : undefined,\n      reset,\n\n      // The \"loop\" prop can return a \"useSpring\" props object to\n      // override any of the original props.\n      ...overrides,\n    })\n  }\n}\n\n/**\n * Return a new object based on the given `props`.\n *\n * - All non-reserved props are moved into the `to` prop object.\n * - The `keys` prop is set to an array of affected keys,\n *   or `null` if all keys are affected.\n */\nexport function createUpdate(props: any) {\n  const { to, from } = (props = inferTo(props))\n\n  // Collect the keys affected by this update.\n  const keys = new Set<string>()\n\n  if (is.obj(to)) findDefined(to, keys)\n  if (is.obj(from)) findDefined(from, keys)\n\n  // The \"keys\" prop helps in applying updates to affected keys only.\n  props.keys = keys.size ? Array.from(keys) : null\n\n  return props\n}\n\n/**\n * A modified version of `createUpdate` meant for declarative APIs.\n */\nexport function declareUpdate(props: any) {\n  const update = createUpdate(props)\n  if (is.und(update.default)) {\n    update.default = getDefaultProps(update)\n  }\n  return update\n}\n\n/** Find keys with defined values */\nfunction findDefined(values: Lookup, keys: Set<string>) {\n  eachProp(values, (value, key) => value != null && keys.add(key as any))\n}\n\n/** Event props with \"active handler\" support */\nconst ACTIVE_EVENTS = [\n  'onStart',\n  'onRest',\n  'onChange',\n  'onPause',\n  'onResume',\n] as const\n\nfunction mergeActiveFn<T, P extends EventKey>(\n  target: SpringValue<T>,\n  props: SpringProps<T>,\n  type: P\n) {\n  target.animation[type] =\n    props[type] !== getDefaultProp(props, type)\n      ? resolveProp<any>(props[type], target.key)\n      : undefined\n}\n\ntype EventArgs<T, P extends EventKey> = Parameters<\n  Extract<SpringProps<T>[P], Function>\n>\n\n/** Call the active handler first, then the default handler. */\nfunction sendEvent<T, P extends EventKey>(\n  target: SpringValue<T>,\n  type: P,\n  ...args: EventArgs<T, P>\n) {\n  target.animation[type]?.(...(args as [any, any]))\n  target.defaultProps[type]?.(...(args as [any, any]))\n}\n", "import { is, easings } from '@react-spring/shared'\nimport { EasingFunction } from '@react-spring/types'\nimport { config as configs } from './constants'\n\nconst defaults: any = {\n  ...configs.default,\n  mass: 1,\n  damping: 1,\n  easing: easings.linear,\n  clamp: false,\n}\n\nexport class AnimationConfig {\n  /**\n   * With higher tension, the spring will resist bouncing and try harder to stop at its end value.\n   *\n   * When tension is zero, no animation occurs.\n   *\n   * @default 170\n   */\n  tension!: number\n\n  /**\n   * The damping ratio coefficient, or just the damping ratio when `speed` is defined.\n   *\n   * When `speed` is defined, this value should be between 0 and 1.\n   *\n   * Higher friction means the spring will slow down faster.\n   *\n   * @default 26\n   */\n  friction!: number\n\n  /**\n   * The natural frequency (in seconds), which dictates the number of bounces\n   * per second when no damping exists.\n   *\n   * When defined, `tension` is derived from this, and `friction` is derived\n   * from `tension` and `damping`.\n   */\n  frequency?: number\n\n  /**\n   * The damping ratio, which dictates how the spring slows down.\n   *\n   * Set to `0` to never slow down. Set to `1` to slow down without bouncing.\n   * Between `0` and `1` is for you to explore.\n   *\n   * Only works when `frequency` is defined.\n   *\n   * @default 1\n   */\n  damping!: number\n\n  /**\n   * Higher mass means more friction is required to slow down.\n   *\n   * Defaults to 1, which works fine most of the time.\n   *\n   * @default 1\n   */\n  mass!: number\n\n  /**\n   * The initial velocity of one or more values.\n   *\n   * @default 0\n   */\n  velocity: number | number[] = 0\n\n  /**\n   * The smallest velocity before the animation is considered \"not moving\".\n   *\n   * When undefined, `precision` is used instead.\n   */\n  restVelocity?: number\n\n  /**\n   * The smallest distance from a value before that distance is essentially zero.\n   *\n   * This helps in deciding when a spring is \"at rest\". The spring must be within\n   * this distance from its final value, and its velocity must be lower than this\n   * value too (unless `restVelocity` is defined).\n   *\n   * @default 0.01\n   */\n  precision?: number\n\n  /**\n   * For `duration` animations only. Note: The `duration` is not affected\n   * by this property.\n   *\n   * Defaults to `0`, which means \"start from the beginning\".\n   *\n   * Setting to `1+` makes an immediate animation.\n   *\n   * Setting to `0.5` means \"start from the middle of the easing function\".\n   *\n   * Any number `>= 0` and `<= 1` makes sense here.\n   */\n  progress?: number\n\n  /**\n   * Animation length in number of milliseconds.\n   */\n  duration?: number\n\n  /**\n   * The animation curve. Only used when `duration` is defined.\n   *\n   * Defaults to quadratic ease-in-out.\n   */\n  easing!: EasingFunction\n\n  /**\n   * Avoid overshooting by ending abruptly at the goal value.\n   *\n   * @default false\n   */\n  clamp!: boolean\n\n  /**\n   * When above zero, the spring will bounce instead of overshooting when\n   * exceeding its goal value. Its velocity is multiplied by `-1 + bounce`\n   * whenever its current value equals or exceeds its goal. For example,\n   * setting `bounce` to `0.5` chops the velocity in half on each bounce,\n   * in addition to any friction.\n   */\n  bounce?: number\n\n  /**\n   * \"Decay animations\" decelerate without an explicit goal value.\n   * Useful for scrolling animations.\n   *\n   * Use `true` for the default exponential decay factor (`0.998`).\n   *\n   * When a `number` between `0` and `1` is given, a lower number makes the\n   * animation slow down faster. And setting to `1` would make an unending\n   * animation.\n   *\n   * @default false\n   */\n  decay?: boolean | number\n\n  /**\n   * While animating, round to the nearest multiple of this number.\n   * The `from` and `to` values are never rounded, as well as any value\n   * passed to the `set` method of an animated value.\n   */\n  round?: number\n\n  constructor() {\n    Object.assign(this, defaults)\n  }\n}\n\nexport function mergeConfig(\n  config: AnimationConfig,\n  newConfig: Partial<AnimationConfig>,\n  defaultConfig?: Partial<AnimationConfig>\n): typeof config\n\nexport function mergeConfig(\n  config: any,\n  newConfig: object,\n  defaultConfig?: object\n) {\n  if (defaultConfig) {\n    defaultConfig = { ...defaultConfig }\n    sanitizeConfig(defaultConfig, newConfig)\n    newConfig = { ...defaultConfig, ...newConfig }\n  }\n\n  sanitizeConfig(config, newConfig)\n  Object.assign(config, newConfig)\n\n  for (const key in defaults) {\n    if (config[key] == null) {\n      config[key] = defaults[key]\n    }\n  }\n\n  let { frequency, damping } = config\n  const { mass } = config\n  if (!is.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01\n    if (damping < 0) damping = 0\n    config.tension = Math.pow((2 * Math.PI) / frequency, 2) * mass\n    config.friction = (4 * Math.PI * damping * mass) / frequency\n  }\n\n  return config\n}\n\n// Prevent a config from accidentally overriding new props.\n// This depends on which \"config\" props take precedence when defined.\nfunction sanitizeConfig(\n  config: Partial<AnimationConfig>,\n  props: Partial<AnimationConfig>\n) {\n  if (!is.und(props.decay)) {\n    config.duration = undefined\n  } else {\n    const isTensionConfig = !is.und(props.tension) || !is.und(props.friction)\n    if (\n      isTensionConfig ||\n      !is.und(props.frequency) ||\n      !is.und(props.damping) ||\n      !is.und(props.mass)\n    ) {\n      config.duration = undefined\n      config.decay = undefined\n    }\n    if (isTensionConfig) {\n      config.frequency = undefined\n    }\n  }\n}\n", "// The `mass` prop defaults to 1\nexport const config = {\n  default: { tension: 170, friction: 26 },\n  gentle: { tension: 120, friction: 14 },\n  wobbly: { tension: 180, friction: 12 },\n  stiff: { tension: 210, friction: 20 },\n  slow: { tension: 280, friction: 60 },\n  molasses: { tension: 280, friction: 120 },\n} as const\n", "import { AnimatedValue } from '@react-spring/animated'\nimport { FluidValue } from '@react-spring/shared'\nimport { AnimationConfig } from './AnimationConfig'\nimport { PickEventFns } from './types/internal'\nimport { SpringProps } from './types'\n\nconst emptyArray: readonly any[] = []\n\n/** An animation being executed by the frameloop */\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\nexport class Animation<T = any> {\n  changed = false\n  values: readonly AnimatedValue[] = emptyArray\n  toValues: readonly number[] | null = null\n  fromValues: readonly number[] = emptyArray\n\n  to!: T | FluidValue<T>\n  from!: T | FluidValue<T>\n  config = new AnimationConfig()\n  immediate = false\n}\n\nexport interface Animation<T> extends PickEventFns<SpringProps<T>> {}\n", "import { Timeout, is, raf, Globals as G } from '@react-spring/shared'\nimport { matchProp, callProp } from './helpers'\nimport { AsyncResult, MatchProp } from './types'\nimport { RunAsyncState, RunAsyncProps } from './runAsync'\nimport {\n  AnimationResolver,\n  AnimationTarget,\n  InferProps,\n  InferState,\n} from './types/internal'\n\n// The `scheduleProps` function only handles these defaults.\ntype DefaultProps<T> = { cancel?: MatchProp<T>; pause?: MatchProp<T> }\n\ninterface ScheduledProps<T extends AnimationTarget> {\n  key?: string\n  props: InferProps<T>\n  defaultProps?: DefaultProps<InferState<T>>\n  state: RunAsyncState<T>\n  actions: {\n    pause: () => void\n    resume: () => void\n    start: (props: RunAsyncProps<T>, resolve: AnimationResolver<T>) => void\n  }\n}\n\n/**\n * This function sets a timeout if both the `delay` prop exists and\n * the `cancel` prop is not `true`.\n *\n * The `actions.start` function must handle the `cancel` prop itself,\n * but the `pause` prop is taken care of.\n */\nexport function scheduleProps<T extends AnimationTarget>(\n  callId: number,\n  { key, props, defaultProps, state, actions }: ScheduledProps<T>\n): AsyncResult<T> {\n  return new Promise((resolve, reject) => {\n    let delay: number\n    let timeout: Timeout\n\n    let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key)\n    if (cancel) {\n      onStart()\n    } else {\n      // The `pause` prop updates the paused flag.\n      if (!is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key)\n      }\n      // The default `pause` takes precedence when true,\n      // which allows `SpringContext` to work as expected.\n      let pause = defaultProps?.pause\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key)\n      }\n\n      delay = callProp(props.delay || 0, key)\n      if (pause) {\n        state.resumeQueue.add(onResume)\n        actions.pause()\n      } else {\n        actions.resume()\n        onResume()\n      }\n    }\n\n    function onPause() {\n      state.resumeQueue.add(onResume)\n      state.timeouts.delete(timeout)\n      timeout.cancel()\n      // Cache the remaining delay.\n      delay = timeout.time - raf.now()\n    }\n\n    function onResume() {\n      if (delay > 0 && !G.skipAnimation) {\n        state.delayed = true\n        timeout = raf.setTimeout(onStart, delay)\n        state.pauseQueue.add(onPause)\n        state.timeouts.add(timeout)\n      } else {\n        onStart()\n      }\n    }\n\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false\n      }\n\n      state.pauseQueue.delete(onPause)\n      state.timeouts.delete(timeout)\n\n      // Maybe cancelled during its delay.\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true\n      }\n\n      try {\n        actions.start({ ...props, callId, cancel }, resolve)\n      } catch (err) {\n        reject(err)\n      }\n    }\n  })\n}\n", "import {\n  is,\n  raf,\n  flush,\n  eachProp,\n  Timeout,\n  Globals as G,\n} from '@react-spring/shared'\nimport { Falsy } from '@react-spring/types'\n\nimport { getDefaultProps } from './helpers'\nimport { AnimationTarget, InferState, InferProps } from './types/internal'\nimport { AnimationResult, AsyncResult, SpringChain, SpringToFn } from './types'\nimport { getCancelledResult, getFinishedResult } from './AnimationResult'\n\ntype AsyncTo<T> = SpringChain<T> | SpringToFn<T>\n\n/** @internal */\nexport type RunAsyncProps<T extends AnimationTarget = any> = InferProps<T> & {\n  callId: number\n  parentId?: number\n  cancel: boolean\n  to?: any\n}\n\n/** @internal */\nexport interface RunAsyncState<T extends AnimationTarget = any> {\n  paused: boolean\n  pauseQueue: Set<() => void>\n  resumeQueue: Set<() => void>\n  timeouts: Set<Timeout>\n  delayed?: boolean\n  asyncId?: number\n  asyncTo?: AsyncTo<InferState<T>>\n  promise?: AsyncResult<T>\n  cancelId?: number\n}\n\n/**\n * Start an async chain or an async script.\n *\n * Always call `runAsync` in the action callback of a `scheduleProps` call.\n *\n * The `T` parameter can be a set of animated values (as an object type)\n * or a primitive type for a single animated value.\n */\nexport function runAsync<T extends AnimationTarget>(\n  to: AsyncTo<InferState<T>>,\n  props: RunAsyncProps<T>,\n  state: RunAsyncState<T>,\n  target: T\n): AsyncResult<T> {\n  const { callId, parentId, onRest } = props\n  const { asyncTo: prevTo, promise: prevPromise } = state\n\n  if (!parentId && to === prevTo && !props.reset) {\n    return prevPromise!\n  }\n\n  return (state.promise = (async () => {\n    state.asyncId = callId\n    state.asyncTo = to\n\n    // The default props of any `animate` calls.\n    const defaultProps = getDefaultProps<InferProps<T>>(props, (value, key) =>\n      // The `onRest` prop is only called when the `runAsync` promise is resolved.\n      key === 'onRest' ? undefined : value\n    )\n\n    let preventBail!: () => void\n    let bail: (error: any) => void\n\n    // This promise is rejected when the animation is interrupted.\n    const bailPromise = new Promise<void>(\n      (resolve, reject) => ((preventBail = resolve), (bail = reject))\n    )\n\n    const bailIfEnded = (bailSignal: BailSignal) => {\n      const bailResult =\n        // The `cancel` prop or `stop` method was used.\n        (callId <= (state.cancelId || 0) && getCancelledResult(target)) ||\n        // The async `to` prop was replaced.\n        (callId !== state.asyncId && getFinishedResult(target, false))\n\n      if (bailResult) {\n        bailSignal.result = bailResult\n\n        // Reject the `bailPromise` to ensure the `runAsync` promise\n        // is not relying on the caller to rethrow the error for us.\n        bail(bailSignal)\n        throw bailSignal\n      }\n    }\n\n    const animate: any = (arg1: any, arg2?: any) => {\n      // Create the bail signal outside the returned promise,\n      // so the generated stack trace is relevant.\n      const bailSignal = new BailSignal()\n      const skipAnimationSignal = new SkipAnimationSignal()\n\n      return (async () => {\n        if (G.skipAnimation) {\n          /**\n           * We need to stop animations if `skipAnimation`\n           * is set in the Globals\n           *\n           */\n          stopAsync(state)\n\n          // create the rejection error that's handled gracefully\n          skipAnimationSignal.result = getFinishedResult(target, false)\n          bail(skipAnimationSignal)\n          throw skipAnimationSignal\n        }\n\n        bailIfEnded(bailSignal)\n\n        const props: any = is.obj(arg1) ? { ...arg1 } : { ...arg2, to: arg1 }\n        props.parentId = callId\n\n        eachProp(defaultProps, (value, key) => {\n          if (is.und(props[key])) {\n            props[key] = value\n          }\n        })\n\n        const result = await target.start(props)\n        bailIfEnded(bailSignal)\n\n        if (state.paused) {\n          await new Promise<void>(resume => {\n            state.resumeQueue.add(resume)\n          })\n        }\n\n        return result\n      })()\n    }\n\n    let result!: AnimationResult<T>\n\n    if (G.skipAnimation) {\n      /**\n       * We need to stop animations if `skipAnimation`\n       * is set in the Globals\n       */\n      stopAsync(state)\n      return getFinishedResult(target, false)\n    }\n\n    try {\n      let animating!: Promise<void>\n\n      // Async sequence\n      if (is.arr(to)) {\n        animating = (async (queue: any[]) => {\n          for (const props of queue) {\n            await animate(props)\n          }\n        })(to)\n      }\n\n      // Async script\n      else {\n        animating = Promise.resolve(to(animate, target.stop.bind(target)))\n      }\n\n      await Promise.all([animating.then(preventBail), bailPromise])\n      result = getFinishedResult(target.get(), true, false)\n\n      // Bail handling\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result\n      } else {\n        throw err\n      }\n\n      // Reset the async state.\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId\n        state.asyncTo = parentId ? prevTo : undefined\n        state.promise = parentId ? prevPromise : undefined\n      }\n    }\n\n    if (is.fun(onRest)) {\n      raf.batchedUpdates(() => {\n        onRest(result, target, target.item)\n      })\n    }\n\n    return result\n  })())\n}\n\n/** Stop the current `runAsync` call with `finished: false` (or with `cancelled: true` when `cancelId` is defined) */\nexport function stopAsync(state: RunAsyncState, cancelId?: number | Falsy) {\n  flush(state.timeouts, t => t.cancel())\n  state.pauseQueue.clear()\n  state.resumeQueue.clear()\n  state.asyncId = state.asyncTo = state.promise = undefined\n  if (cancelId) state.cancelId = cancelId\n}\n\n/** This error is thrown to signal an interrupted async animation. */\nexport class BailSignal extends Error {\n  result!: AnimationResult\n  constructor() {\n    super(\n      'An async animation has been interrupted. You see this error because you ' +\n        'forgot to use `await` or `.catch(...)` on its returned promise.'\n    )\n  }\n}\n\nexport class SkipAnimationSignal extends Error {\n  result!: AnimationResult\n\n  constructor() {\n    super('SkipAnimationSignal')\n  }\n}\n", "import { AnimationResult } from './types'\nimport { Readable } from './types/internal'\n\n/** @internal */\nexport const getCombinedResult = <T extends Readable>(\n  target: T,\n  results: AnimationResult<T>[]\n): AnimationResult<T> =>\n  results.length == 1\n    ? results[0]\n    : results.some(result => result.cancelled)\n      ? getCancelledResult(target.get())\n      : results.every(result => result.noop)\n        ? getNoopResult(target.get())\n        : getFinishedResult(\n            target.get(),\n            results.every(result => result.finished)\n          )\n\n/** No-op results are for updates that never start an animation. */\nexport const getNoopResult = (value: any) => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false,\n})\n\nexport const getFinishedResult = (\n  value: any,\n  finished: boolean,\n  cancelled = false\n) => ({\n  value,\n  finished,\n  cancelled,\n})\n\nexport const getCancelledResult = (value: any) => ({\n  value,\n  cancelled: true,\n  finished: false,\n})\n", "import {\n  deprecateInterpolate,\n  frameLoop,\n  FluidValue,\n  Globals as G,\n  callFluidObservers,\n} from '@react-spring/shared'\nimport { InterpolatorArgs } from '@react-spring/types'\nimport { getAnimated } from '@react-spring/animated'\n\nimport { Interpolation } from './Interpolation'\n\nexport const isFrameValue = (value: any): value is FrameValue =>\n  value instanceof FrameValue\n\nlet nextId = 1\n\n/**\n * A kind of `FluidValue` that manages an `AnimatedValue` node.\n *\n * Its underlying value can be accessed and even observed.\n */\nexport abstract class FrameValue<T = any> extends FluidValue<\n  T,\n  FrameValue.Event<T>\n> {\n  readonly id = nextId++\n\n  abstract key?: string\n  abstract get idle(): boolean\n\n  protected _priority = 0\n\n  get priority() {\n    return this._priority\n  }\n  set priority(priority: number) {\n    if (this._priority != priority) {\n      this._priority = priority\n      this._onPriorityChange(priority)\n    }\n  }\n\n  /** Get the current value */\n  get(): T {\n    const node = getAnimated(this)\n    return node && node.getValue()\n  }\n\n  /** Create a spring that maps our value to another value */\n  to<Out>(...args: InterpolatorArgs<T, Out>) {\n    return G.to(this, args) as Interpolation<T, Out>\n  }\n\n  /** @deprecated Use the `to` method instead. */\n  interpolate<Out>(...args: InterpolatorArgs<T, Out>) {\n    deprecateInterpolate()\n    return G.to(this, args) as Interpolation<T, Out>\n  }\n\n  toJSON() {\n    return this.get()\n  }\n\n  protected observerAdded(count: number) {\n    if (count == 1) this._attach()\n  }\n\n  protected observerRemoved(count: number) {\n    if (count == 0) this._detach()\n  }\n\n  /** @internal */\n  abstract advance(dt: number): void\n\n  /** @internal */\n  abstract eventObserved(_event: FrameValue.Event): void\n\n  /** Called when the first child is added. */\n  protected _attach() {}\n\n  /** Called when the last child is removed. */\n  protected _detach() {}\n\n  /** Tell our children about our new value */\n  protected _onChange(value: T, idle = false) {\n    callFluidObservers(this, {\n      type: 'change',\n      parent: this,\n      value,\n      idle,\n    })\n  }\n\n  /** Tell our children about our new priority */\n  protected _onPriorityChange(priority: number) {\n    if (!this.idle) {\n      frameLoop.sort(this)\n    }\n    callFluidObservers(this, {\n      type: 'priority',\n      parent: this,\n      priority,\n    })\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport declare namespace FrameValue {\n  /** A parent changed its value */\n  interface ChangeEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'change'\n    value: T\n    idle: boolean\n  }\n\n  /** A parent changed its priority */\n  interface PriorityEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'priority'\n    priority: number\n  }\n\n  /** A parent is done animating */\n  interface IdleEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'idle'\n  }\n\n  /** Events sent to children of `FrameValue` objects */\n  export type Event<T = any> = ChangeEvent<T> | PriorityEvent<T> | IdleEvent<T>\n}\n", "/** The property symbol of the current animation phase. */\nconst $P = Symbol.for('SpringPhase')\n\nconst HAS_ANIMATED = 1\nconst IS_ANIMATING = 2\nconst IS_PAUSED = 4\n\n/** Returns true if the `target` has ever animated. */\nexport const hasAnimated = (target: any) => (target[$P] & HAS_ANIMATED) > 0\n\n/** Returns true if the `target` is animating (even if paused). */\nexport const isAnimating = (target: any) => (target[$P] & IS_ANIMATING) > 0\n\n/** Returns true if the `target` is paused (even if idle). */\nexport const isPaused = (target: any) => (target[$P] & IS_PAUSED) > 0\n\n/** Set the active bit of the `target` phase. */\nexport const setActiveBit = (target: any, active: boolean) =>\n  active\n    ? (target[$P] |= IS_ANIMATING | HAS_ANIMATED)\n    : (target[$P] &= ~IS_ANIMATING)\n\nexport const setPausedBit = (target: any, paused: boolean) =>\n  paused ? (target[$P] |= IS_PAUSED) : (target[$P] &= ~IS_PAUSED)\n", "import { <PERSON><PERSON>r<PERSON>ore, UnknownProps, Lookup, Falsy } from '@react-spring/types'\nimport {\n  is,\n  raf,\n  each,\n  noop,\n  flush,\n  toArray,\n  eachProp,\n  flushCalls,\n  addFluidObserver,\n  FluidObserver,\n} from '@react-spring/shared'\n\nimport { getDefaultProp } from './helpers'\nimport { FrameValue } from './FrameValue'\nimport type { SpringRef } from './SpringRef'\nimport { SpringValue, createLoopUpdate, createUpdate } from './SpringValue'\nimport { getCancelledResult, getCombinedResult } from './AnimationResult'\nimport { runAsync, RunAsyncState, stopAsync } from './runAsync'\nimport { scheduleProps } from './scheduleProps'\nimport {\n  AnimationResult,\n  AsyncResult,\n  ControllerFlushFn,\n  ControllerUpdate,\n  OnChange,\n  OnRest,\n  OnStart,\n  SpringChain,\n  SpringToFn,\n  SpringValues,\n} from './types'\n\n/** Events batched by the `Controller` class */\nconst BATCHED_EVENTS = ['onStart', 'onChange', 'onRest'] as const\n\nlet nextId = 1\n\n/** Queue of pending updates for a `Controller` instance. */\nexport interface ControllerQueue<State extends Lookup = Lookup>\n  extends Array<\n    ControllerUpdate<State, any> & {\n      /** The keys affected by this update. When null, all keys are affected. */\n      keys: string[] | null\n    }\n  > {}\n\nexport class Controller<State extends Lookup = Lookup> {\n  readonly id = nextId++\n\n  /** The animated values */\n  springs: SpringValues<State> = {} as any\n\n  /** The queue of props passed to the `update` method. */\n  queue: ControllerQueue<State> = []\n\n  /**\n   * The injected ref. When defined, render-based updates are pushed\n   * onto the `queue` instead of being auto-started.\n   */\n  ref?: SpringRef<State>\n\n  /** Custom handler for flushing update queues */\n  protected _flush?: ControllerFlushFn<this>\n\n  /** These props are used by all future spring values */\n  protected _initialProps?: Lookup\n\n  /** The counter for tracking `scheduleProps` calls */\n  protected _lastAsyncId = 0\n\n  /** The values currently being animated */\n  protected _active = new Set<FrameValue>()\n\n  /** The values that changed recently */\n  protected _changed = new Set<FrameValue>()\n\n  /** Equals false when `onStart` listeners can be called */\n  protected _started = false\n\n  private _item?: any\n\n  /** State used by the `runAsync` function */\n  protected _state: RunAsyncState<this> = {\n    paused: false,\n    pauseQueue: new Set(),\n    resumeQueue: new Set(),\n    timeouts: new Set(),\n  }\n\n  /** The event queues that are flushed once per frame maximum */\n  protected _events = {\n    onStart: new Map<\n      OnStart<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n    onChange: new Map<\n      OnChange<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n    onRest: new Map<\n      OnRest<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n  }\n\n  constructor(\n    props?: ControllerUpdate<State> | null,\n    flush?: ControllerFlushFn<any>\n  ) {\n    this._onFrame = this._onFrame.bind(this)\n    if (flush) {\n      this._flush = flush\n    }\n    if (props) {\n      this.start({ default: true, ...props })\n    }\n  }\n\n  /**\n   * Equals `true` when no spring values are in the frameloop, and\n   * no async animation is currently active.\n   */\n  get idle() {\n    return (\n      !this._state.asyncTo &&\n      Object.values(this.springs as Lookup<SpringValue>).every(spring => {\n        return spring.idle && !spring.isDelayed && !spring.isPaused\n      })\n    )\n  }\n\n  get item() {\n    return this._item\n  }\n\n  set item(item) {\n    this._item = item\n  }\n\n  /** Get the current values of our springs */\n  get(): State & UnknownProps {\n    const values: any = {}\n    this.each((spring, key) => (values[key] = spring.get()))\n    return values\n  }\n\n  /** Set the current values without animating. */\n  set(values: Partial<State>) {\n    for (const key in values) {\n      const value = values[key]\n      if (!is.und(value)) {\n        this.springs[key].set(value)\n      }\n    }\n  }\n\n  /** Push an update onto the queue of each value. */\n  update(props: ControllerUpdate<State> | Falsy) {\n    if (props) {\n      this.queue.push(createUpdate(props))\n    }\n    return this\n  }\n\n  /**\n   * Start the queued animations for every spring, and resolve the returned\n   * promise once all queued animations have finished or been cancelled.\n   *\n   * When you pass a queue (instead of nothing), that queue is used instead of\n   * the queued animations added with the `update` method, which are left alone.\n   */\n  start(props?: OneOrMore<ControllerUpdate<State>> | null): AsyncResult<this> {\n    let { queue } = this as any\n    if (props) {\n      queue = toArray<any>(props).map(createUpdate)\n    } else {\n      this.queue = []\n    }\n\n    if (this._flush) {\n      return this._flush(this, queue)\n    }\n\n    prepareKeys(this, queue)\n    return flushUpdateQueue(this, queue)\n  }\n\n  /** Stop all animations. */\n  stop(): this\n  /** Stop animations for the given keys. */\n  stop(keys: OneOrMore<string>): this\n  /** Cancel all animations. */\n  stop(cancel: boolean): this\n  /** Cancel animations for the given keys. */\n  stop(cancel: boolean, keys: OneOrMore<string>): this\n  /** Stop some or all animations. */\n  stop(keys?: OneOrMore<string>): this\n  /** Cancel some or all animations. */\n  stop(cancel: boolean, keys?: OneOrMore<string>): this\n  /** @internal */\n  stop(arg?: boolean | OneOrMore<string>, keys?: OneOrMore<string>) {\n    if (arg !== !!arg) {\n      keys = arg as OneOrMore<string>\n    }\n    if (keys) {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].stop(!!arg))\n    } else {\n      stopAsync(this._state, this._lastAsyncId)\n      this.each(spring => spring.stop(!!arg))\n    }\n    return this\n  }\n\n  /** Freeze the active animation in time */\n  pause(keys?: OneOrMore<string>) {\n    if (is.und(keys)) {\n      this.start({ pause: true })\n    } else {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].pause())\n    }\n    return this\n  }\n\n  /** Resume the animation if paused. */\n  resume(keys?: OneOrMore<string>) {\n    if (is.und(keys)) {\n      this.start({ pause: false })\n    } else {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].resume())\n    }\n    return this\n  }\n\n  /** Call a function once per spring value */\n  each(iterator: (spring: SpringValue, key: string) => void) {\n    eachProp(this.springs, iterator as any)\n  }\n\n  /** @internal Called at the end of every animation frame */\n  protected _onFrame() {\n    const { onStart, onChange, onRest } = this._events\n\n    const active = this._active.size > 0\n    const changed = this._changed.size > 0\n\n    if ((active && !this._started) || (changed && !this._started)) {\n      this._started = true\n      flush(onStart, ([onStart, result]) => {\n        result.value = this.get()\n        onStart(result, this, this._item)\n      })\n    }\n\n    const idle = !active && this._started\n    const values = changed || (idle && onRest.size) ? this.get() : null\n\n    if (changed && onChange.size) {\n      flush(onChange, ([onChange, result]) => {\n        result.value = values\n        onChange(result, this, this._item)\n      })\n    }\n\n    // The \"onRest\" queue is only flushed when all springs are idle.\n    if (idle) {\n      this._started = false\n      flush(onRest, ([onRest, result]) => {\n        result.value = values\n        onRest(result, this, this._item)\n      })\n    }\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    if (event.type == 'change') {\n      this._changed.add(event.parent)\n      if (!event.idle) {\n        this._active.add(event.parent)\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent)\n    }\n    // The `onFrame` handler runs when a parent is changed or idle.\n    else return\n    raf.onFrame(this._onFrame)\n  }\n}\n\n/**\n * Warning: Props might be mutated.\n */\nexport function flushUpdateQueue(\n  ctrl: Controller<any>,\n  queue: ControllerQueue\n) {\n  return Promise.all(queue.map(props => flushUpdate(ctrl, props))).then(\n    results => getCombinedResult(ctrl, results)\n  )\n}\n\n/**\n * Warning: Props might be mutated.\n *\n * Process a single set of props using the given controller.\n *\n * The returned promise resolves to `true` once the update is\n * applied and any animations it starts are finished without being\n * stopped or cancelled.\n */\nexport async function flushUpdate(\n  ctrl: Controller<any>,\n  props: ControllerQueue[number],\n  isLoop?: boolean\n): AsyncResult {\n  const { keys, to, from, loop, onRest, onResolve } = props\n  const defaults = is.obj(props.default) && props.default\n\n  // Looping must be handled in this function, or else the values\n  // would end up looping out-of-sync in many common cases.\n  if (loop) {\n    props.loop = false\n  }\n\n  // Treat false like null, which gets ignored.\n  if (to === false) props.to = null\n  if (from === false) props.from = null\n\n  const asyncTo = is.arr(to) || is.fun(to) ? to : undefined\n  if (asyncTo) {\n    props.to = undefined\n    props.onRest = undefined\n    if (defaults) {\n      defaults.onRest = undefined\n    }\n  }\n  // For certain events, use batching to prevent multiple calls per frame.\n  // However, batching is avoided when the `to` prop is async, because any\n  // event props are used as default props instead.\n  else {\n    each(BATCHED_EVENTS, key => {\n      const handler: any = props[key]\n      if (is.fun(handler)) {\n        const queue = ctrl['_events'][key]\n        props[key] = (({ finished, cancelled }: AnimationResult) => {\n          const result = queue.get(handler)\n          if (result) {\n            if (!finished) result.finished = false\n            if (cancelled) result.cancelled = true\n          } else {\n            // The \"value\" is set before the \"handler\" is called.\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false,\n            })\n          }\n        }) as any\n\n        // Avoid using a batched `handler` as a default prop.\n        if (defaults) {\n          defaults[key] = props[key] as any\n        }\n      }\n    })\n  }\n\n  const state = ctrl['_state']\n\n  // Pause/resume the `asyncTo` when `props.pause` is true/false.\n  if (props.pause === !state.paused) {\n    state.paused = props.pause\n    flushCalls(props.pause ? state.pauseQueue : state.resumeQueue)\n  }\n  // When a controller is paused, its values are also paused.\n  else if (state.paused) {\n    props.pause = true\n  }\n\n  const promises: AsyncResult[] = (keys || Object.keys(ctrl.springs)).map(key =>\n    ctrl.springs[key]!.start(props as any)\n  )\n\n  const cancel =\n    props.cancel === true || getDefaultProp(props, 'cancel') === true\n\n  if (asyncTo || (cancel && state.asyncId)) {\n    promises.push(\n      scheduleProps(++ctrl['_lastAsyncId'], {\n        props,\n        state,\n        actions: {\n          pause: noop,\n          resume: noop,\n          start(props, resolve) {\n            if (cancel) {\n              stopAsync(state, ctrl['_lastAsyncId'])\n              resolve(getCancelledResult(ctrl))\n            } else {\n              props.onRest = onRest\n              resolve(\n                runAsync(\n                  asyncTo as SpringChain | SpringToFn,\n                  props,\n                  state,\n                  ctrl\n                )\n              )\n            }\n          },\n        },\n      })\n    )\n  }\n\n  // Pause after updating each spring, so they can be resumed separately\n  // and so their default `pause` and `cancel` props are updated.\n  if (state.paused) {\n    // Ensure `this` must be resumed before the returned promise\n    // is resolved and before starting the next `loop` repetition.\n    await new Promise<void>(resume => {\n      state.resumeQueue.add(resume)\n    })\n  }\n\n  const result = getCombinedResult<any>(ctrl, await Promise.all(promises))\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to)\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps])\n      return flushUpdate(ctrl, nextProps, true)\n    }\n  }\n  if (onResolve) {\n    raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item))\n  }\n  return result\n}\n\n/**\n * From an array of updates, get the map of `SpringValue` objects\n * by their keys. Springs are created when any update wants to\n * animate a new key.\n *\n * Springs created by `getSprings` are neither cached nor observed\n * until they're given to `setSprings`.\n */\nexport function getSprings<State extends Lookup>(\n  ctrl: Controller<Lookup<any>>,\n  props?: OneOrMore<ControllerUpdate<State>>\n) {\n  const springs = { ...ctrl.springs }\n  if (props) {\n    each(toArray(props), (props: any) => {\n      if (is.und(props.keys)) {\n        props = createUpdate(props)\n      }\n      if (!is.obj(props.to)) {\n        // Avoid passing array/function to each spring.\n        props = { ...props, to: undefined }\n      }\n      prepareSprings(springs as any, props, key => {\n        return createSpring(key)\n      })\n    })\n  }\n  setSprings(ctrl, springs)\n  return springs\n}\n\n/**\n * Tell a controller to manage the given `SpringValue` objects\n * whose key is not already in use.\n */\nexport function setSprings(\n  ctrl: Controller<Lookup<any>>,\n  springs: SpringValues<UnknownProps>\n) {\n  eachProp(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring\n      addFluidObserver(spring, ctrl)\n    }\n  })\n}\n\nfunction createSpring(key: string, observer?: FluidObserver<FrameValue.Event>) {\n  const spring = new SpringValue()\n  spring.key = key\n  if (observer) {\n    addFluidObserver(spring, observer)\n  }\n  return spring\n}\n\n/**\n * Ensure spring objects exist for each defined key.\n *\n * Using the `props`, the `Animated` node of each `SpringValue` may\n * be created or updated.\n */\nfunction prepareSprings(\n  springs: SpringValues,\n  props: ControllerQueue[number],\n  create: (key: string) => SpringValue\n) {\n  if (props.keys) {\n    each(props.keys, key => {\n      const spring = springs[key] || (springs[key] = create(key))\n      spring['_prepareNode'](props)\n    })\n  }\n}\n\n/**\n * Ensure spring objects exist for each defined key, and attach the\n * `ctrl` to them for observation.\n *\n * The queue is expected to contain `createUpdate` results.\n */\nfunction prepareKeys(ctrl: Controller<any>, queue: ControllerQueue[number][]) {\n  each(queue, props => {\n    prepareSprings(ctrl.springs, props, key => {\n      return createSpring(key, ctrl)\n    })\n  })\n}\n", "import * as React from 'react'\nimport { useContext, PropsWithChildren } from 'react'\nimport { useMemoOne } from '@react-spring/shared'\n\n/**\n * This context affects all new and existing `SpringValue` objects\n * created with the hook API or the renderprops API.\n */\nexport interface SpringContext {\n  /** Pause all new and existing animations. */\n  pause?: boolean\n  /** Force all new and existing animations to be immediate. */\n  immediate?: boolean\n}\n\nexport const SpringContext = ({\n  children,\n  ...props\n}: PropsWithChildren<SpringContext>) => {\n  const inherited = useContext(ctx)\n\n  // Inherited values are dominant when truthy.\n  const pause = props.pause || !!inherited.pause,\n    immediate = props.immediate || !!inherited.immediate\n\n  // Memoize the context to avoid unwanted renders.\n  props = useMemoOne(() => ({ pause, immediate }), [pause, immediate])\n\n  const { Provider } = ctx\n  return <Provider value={props}>{children}</Provider>\n}\n\nconst ctx = makeContext(SpringContext, {} as SpringContext)\n\n// Allow `useContext(SpringContext)` in TypeScript.\nSpringContext.Provider = ctx.Provider\nSpringContext.Consumer = ctx.Consumer\n\n/** Make the `target` compatible with `useContext` */\nfunction makeContext<T>(target: any, init: T): React.Context<T> {\n  Object.assign(target, React.createContext(init))\n  target.Provider._context = target\n  target.Consumer._context = target\n  return target\n}\n", "import { each, is, deprecateDirect<PERSON>all } from '@react-spring/shared'\nimport { Lookup, Falsy, OneOrMore } from '@react-spring/types'\nimport { AsyncResult, ControllerUpdate } from './types'\nimport { Controller } from './Controller'\n\nexport interface ControllerUpdateFn<State extends Lookup = Lookup> {\n  (i: number, ctrl: Controller<State>): ControllerUpdate<State> | Falsy\n}\n\nexport interface SpringRef<State extends Lookup = Lookup> {\n  (\n    props?: ControllerUpdate<State> | ControllerUpdateFn<State>\n  ): AsyncResult<Controller<State>>[]\n  current: Controller<State>[]\n\n  /** Add a controller to this ref */\n  add(ctrl: Controller<State>): void\n\n  /** Remove a controller from this ref */\n  delete(ctrl: Controller<State>): void\n\n  /** Pause all animations. */\n  pause(): this\n  /** Pause animations for the given keys. */\n  pause(keys: OneOrMore<string>): this\n  /** Pause some or all animations. */\n  pause(keys?: OneOrMore<string>): this\n\n  /** Resume all animations. */\n  resume(): this\n  /** Resume animations for the given keys. */\n  resume(keys: OneOrMore<string>): this\n  /** Resume some or all animations. */\n  resume(keys?: OneOrMore<string>): this\n\n  /** Update the state of each controller without animating. */\n  set(values: Partial<State>): void\n  /** Update the state of each controller without animating based on their passed state. */\n  set(values: (index: number, ctrl: Controller<State>) => Partial<State>): void\n\n  /** Start the queued animations of each controller. */\n  start(): AsyncResult<Controller<State>>[]\n  /** Update every controller with the same props. */\n  start(props: ControllerUpdate<State>): AsyncResult<Controller<State>>[]\n  /** Update controllers based on their state. */\n  start(props: ControllerUpdateFn<State>): AsyncResult<Controller<State>>[]\n  /** Start animating each controller. */\n  start(\n    props?: ControllerUpdate<State> | ControllerUpdateFn<State>\n  ): AsyncResult<Controller<State>>[]\n\n  /** Stop all animations. */\n  stop(): this\n  /** Stop animations for the given keys. */\n  stop(keys: OneOrMore<string>): this\n  /** Cancel all animations. */\n  stop(cancel: boolean): this\n  /** Cancel animations for the given keys. */\n  stop(cancel: boolean, keys: OneOrMore<string>): this\n  /** Stop some or all animations. */\n  stop(keys?: OneOrMore<string>): this\n  /** Cancel some or all animations. */\n  stop(cancel: boolean, keys?: OneOrMore<string>): this\n\n  /** Add the same props to each controller's update queue. */\n  update(props: ControllerUpdate<State>): this\n  /** Generate separate props for each controller's update queue. */\n  update(props: ControllerUpdateFn<State>): this\n  /** Add props to each controller's update queue. */\n  update(props: ControllerUpdate<State> | ControllerUpdateFn<State>): this\n\n  _getProps(\n    arg: ControllerUpdate<State> | ControllerUpdateFn<State>,\n    ctrl: Controller<State>,\n    index: number\n  ): ControllerUpdate<State> | Falsy\n}\n\nexport const SpringRef = <\n  State extends Lookup = Lookup,\n>(): SpringRef<State> => {\n  const current: Controller<State>[] = []\n\n  const SpringRef: SpringRef<State> = function (props) {\n    deprecateDirectCall()\n\n    const results: AsyncResult[] = []\n\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start())\n      } else {\n        const update = _getProps(props, ctrl, i)\n        if (update) {\n          results.push(ctrl.start(update))\n        }\n      }\n    })\n\n    return results\n  }\n\n  SpringRef.current = current\n\n  /** Add a controller to this ref */\n  SpringRef.add = function (ctrl: Controller<State>) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl)\n    }\n  }\n\n  /** Remove a controller from this ref */\n  SpringRef.delete = function (ctrl: Controller<State>) {\n    const i = current.indexOf(ctrl)\n    if (~i) current.splice(i, 1)\n  }\n\n  /** Pause all animations. */\n  SpringRef.pause = function () {\n    each(current, ctrl => ctrl.pause(...arguments))\n    return this\n  }\n\n  /** Resume all animations. */\n  SpringRef.resume = function () {\n    each(current, ctrl => ctrl.resume(...arguments))\n    return this\n  }\n\n  /** Update the state of each controller without animating. */\n  SpringRef.set = function (\n    values:\n      | Partial<State>\n      | ((i: number, ctrl: Controller<State>) => Partial<State>)\n  ) {\n    each(current, (ctrl, i) => {\n      const update = is.fun(values) ? values(i, ctrl) : values\n      if (update) {\n        ctrl.set(update)\n      }\n    })\n  }\n\n  SpringRef.start = function (props?: object | ControllerUpdateFn<State>) {\n    const results: AsyncResult[] = []\n\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start())\n      } else {\n        const update = this._getProps(props, ctrl, i)\n        if (update) {\n          results.push(ctrl.start(update))\n        }\n      }\n    })\n\n    return results\n  }\n\n  /** Stop all animations. */\n  SpringRef.stop = function () {\n    each(current, ctrl => ctrl.stop(...arguments))\n    return this\n  }\n\n  SpringRef.update = function (props: object | ControllerUpdateFn<State>) {\n    each(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)))\n    return this\n  }\n\n  /** Overridden by `useTrail` to manipulate props */\n  const _getProps = function (\n    arg: ControllerUpdate<State> | ControllerUpdateFn<State>,\n    ctrl: Controller<State>,\n    index: number\n  ) {\n    return is.fun(arg) ? arg(index, ctrl) : arg\n  }\n\n  SpringRef._getProps = _getProps\n\n  return SpringRef\n}\n", "import { useState } from 'react'\nimport { Lookup } from '@react-spring/types'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\n\nconst initSpringRef = () => SpringRef<any>()\n\nexport const useSpringRef = <State extends Lookup = Lookup>() =>\n  useState(initSpringRef)[0] as SpringRefType<State>\n", "import { useConstant, useOnce } from '@react-spring/shared'\n\nimport { SpringValue } from '../SpringValue'\nimport { SpringUpdate } from '../types'\n\n/**\n * Creates a constant single `SpringValue` that can be interacted\n * with imperatively. This is an advanced API and does not react\n * to updates from the parent component e.g. passing a new initial value\n *\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const opacity = useSpringValue(1)\n *\n *   return <animated.div style={{ opacity }} />\n * }\n * ```\n *\n * @param initial – The initial value of the `SpringValue`.\n * @param props – Typically the same props as `useSpring` e.g. `config`, `loop` etc.\n *\n * @public\n */\nexport const useSpringValue = <T>(\n  initial: Exclude<T, object>,\n  props?: SpringUpdate<T>\n) => {\n  const springValue = useConstant(() => new SpringValue(initial, props))\n\n  useOnce(() => () => {\n    springValue.stop()\n  })\n\n  return springValue\n}\n", "import { each, is, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { Lookup } from '@react-spring/types'\n\nimport { Valid } from '../types/common'\nimport { PickAnimated, SpringValues } from '../types'\n\nimport { SpringRef } from '../SpringRef'\nimport { Controller } from '../Controller'\n\nimport { UseSpringProps } from './useSpring'\nimport { useSprings } from './useSprings'\nimport { replaceRef } from '../helpers'\n\nexport type UseTrailProps<Props extends object = any> = UseSpringProps<Props>\n\nexport function useTrail<Props extends object>(\n  length: number,\n  props: (\n    i: number,\n    ctrl: Controller\n  ) => UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>),\n  deps?: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRef<State>]\n    : never\n  : never\n\n/**\n * This hook is an abstraction around `useSprings` and is designed to\n * automatically orchestrate the springs to stagger one after the other\n *\n * ```jsx\n * export const MyComponent = () => {\n *  const trails = useTrail(3, {opacity: 0})\n *\n *  return trails.map(styles => <animated.div style={styles} />)\n * }\n * ```\n *\n * @param length – The number of springs you want to create\n * @param propsArg – The props to pass to the internal `useSprings` hook,\n * therefore is the same as `useSprings`.\n *\n * @public\n */\nexport function useTrail<Props extends object>(\n  length: number,\n  props: UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>)\n): SpringValues<PickAnimated<Props>>[]\n\n/**\n * This hook is an abstraction around `useSprings` and is designed to\n * automatically orchestrate the springs to stagger one after the other\n *\n * ```jsx\n * export const MyComponent = () => {\n *  const trails = useTrail(3, {opacity: 0}, [])\n *\n *  return trails.map(styles => <animated.div style={styles} />)\n * }\n * ```\n *\n * @param length – The number of springs you want to create\n * @param propsArg – The props to pass to the internal `useSprings` hook,\n * therefore is the same as `useSprings`.\n * @param deps – The optional array of dependencies to pass to the internal\n * `useSprings` hook, therefore is the same as `useSprings`.\n *\n * @public\n */\nexport function useTrail<Props extends object>(\n  length: number,\n  props: UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>),\n  deps: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRef<State>]\n    : never\n  : never\n\nexport function useTrail(\n  length: number,\n  propsArg: unknown,\n  deps?: readonly any[]\n) {\n  const propsFn = is.fun(propsArg) && propsArg\n  if (propsFn && !deps) deps = []\n\n  // The trail is reversed when every render-based update is reversed.\n  let reverse = true\n  let passedRef: SpringRef | undefined = undefined\n\n  const result = useSprings(\n    length,\n    (i, ctrl) => {\n      const props = propsFn ? propsFn(i, ctrl) : propsArg\n      passedRef = props.ref\n      reverse = reverse && props.reverse\n\n      return props\n    },\n    // Ensure the props function is called when no deps exist.\n    // This works around the 3 argument rule.\n    deps || [{}]\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    /**\n     * Run through the ref passed by the `useSprings` hook.\n     */\n    each(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)]\n\n      /**\n       * If there's a passed ref then we replace the ctrl ref with it\n       */\n      replaceRef(ctrl, passedRef)\n\n      /**\n       * And if there's a ctrl ref then we update instead of start\n       * which means nothing is fired until the start method\n       * of said passedRef is called.\n       */\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({ to: parent.springs })\n        }\n\n        return\n      }\n\n      if (parent) {\n        ctrl.start({ to: parent.springs })\n      } else {\n        ctrl.start()\n      }\n    })\n  }, deps)\n\n  if (propsFn || arguments.length == 3) {\n    const ref = passedRef ?? result[1]\n\n    ref['_getProps'] = (propsArg, ctrl, i) => {\n      const props = is.fun(propsArg) ? propsArg(i, ctrl) : propsArg\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)]\n        if (parent) props.to = parent.springs\n        return props\n      }\n    }\n    return result\n  }\n\n  return result[0]\n}\n", "import * as React from 'react'\nimport { useContext, useRef, useMemo } from 'react'\nimport { Lookup, OneOrMore, UnknownProps } from '@react-spring/types'\nimport {\n  is,\n  toArray,\n  useForceUpdate,\n  useOnce,\n  usePrev,\n  each,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\n\nimport {\n  Change,\n  ControllerUpdate,\n  ItemKeys,\n  PickAnimated,\n  TransitionFn,\n  TransitionState,\n  TransitionTo,\n  UseTransitionProps,\n} from '../types'\nimport { Valid } from '../types/common'\nimport {\n  callProp,\n  detachRefs,\n  getDefaultProps,\n  hasProps,\n  inferTo,\n  replaceRef,\n} from '../helpers'\nimport { Controller, getSprings } from '../Controller'\nimport { SpringContext } from '../SpringContext'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\nimport { TransitionPhase } from '../TransitionPhase'\n\ndeclare function setTimeout(handler: Function, timeout?: number): number\ndeclare function clearTimeout(timeoutId: number): void\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props: () =>\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>),\n  deps?: any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [TransitionFn<Item, PickAnimated<Props>>, SpringRefType<State>]\n    : never\n  : never\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props:\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>)\n): TransitionFn<Item, PickAnimated<Props>>\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props:\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>),\n  deps: any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [TransitionFn<Item, State>, SpringRefType<State>]\n    : never\n  : never\n\nexport function useTransition(\n  data: unknown,\n  props: UseTransitionProps | (() => any),\n  deps?: any[]\n): any {\n  const propsFn = is.fun(props) && props\n\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig,\n  }: UseTransitionProps<any> = propsFn ? propsFn() : props\n\n  // Return a `SpringRef` if a deps array was passed.\n  const ref = useMemo(\n    () => (propsFn || arguments.length == 3 ? SpringRef() : void 0),\n    []\n  )\n\n  // Every item has its own transition.\n  const items = toArray(data)\n  const transitions: TransitionState[] = []\n\n  // The \"onRest\" callbacks need a ref to the latest transitions.\n  const usedTransitions = useRef<TransitionState[] | null>(null)\n  const prevTransitions = reset ? null : usedTransitions.current\n\n  useIsomorphicLayoutEffect(() => {\n    usedTransitions.current = transitions\n  })\n\n  useOnce(() => {\n    /**\n     * If transitions exist on mount of the component\n     * then reattach their refs on-mount, this was required\n     * for react18 strict mode to work properly.\n     *\n     * See https://github.com/pmndrs/react-spring/issues/1890\n     */\n\n    each(transitions, t => {\n      ref?.add(t.ctrl)\n      t.ctrl.ref = ref\n    })\n\n    // Destroy all transitions on dismount.\n    return () => {\n      each(usedTransitions.current!, t => {\n        if (t.expired) {\n          clearTimeout(t.expirationId!)\n        }\n        detachRefs(t.ctrl, ref)\n        t.ctrl.stop(true)\n      })\n    }\n  })\n\n  // Keys help with reusing transitions between renders.\n  // The `key` prop can be undefined (which means the items themselves are used\n  // as keys), or a function (which maps each item to its key), or an array of\n  // keys (which are assigned to each item by index).\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions)\n\n  // Expired transitions that need clean up.\n  const expired = (reset && usedTransitions.current) || []\n  useIsomorphicLayoutEffect(() =>\n    each(expired, ({ ctrl, item, key }) => {\n      detachRefs(ctrl, ref)\n      callProp(onDestroyed, item, key)\n    })\n  )\n\n  // Map old indices to new indices.\n  const reused: number[] = []\n  if (prevTransitions)\n    each(prevTransitions, (t, i) => {\n      // Expired transitions are not rendered.\n      if (t.expired) {\n        clearTimeout(t.expirationId!)\n        expired.push(t)\n      } else {\n        i = reused[i] = keys.indexOf(t.key)\n        if (~i) transitions[i] = t\n      }\n    })\n\n  // Mount new items with fresh transitions.\n  each(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: TransitionPhase.MOUNT,\n        ctrl: new Controller(),\n      }\n\n      transitions[i].ctrl.item = item\n    }\n  })\n\n  // Update the item of any transition whose key still exists,\n  // and ensure leaving transitions are rendered until they finish.\n  if (reused.length) {\n    let i = -1\n    const { leave }: UseTransitionProps<any> = propsFn ? propsFn() : props\n    each(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions![prevIndex]\n      if (~keyIndex) {\n        i = transitions.indexOf(t)\n        transitions[i] = { ...t, item: items[keyIndex] }\n      } else if (leave) {\n        transitions.splice(++i, 0, t)\n      }\n    })\n  }\n\n  if (is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item))\n  }\n\n  // Track cumulative delay for the \"trail\" prop.\n  let delay = -trail\n\n  // Expired transitions use this to dismount.\n  const forceUpdate = useForceUpdate()\n\n  // These props are inherited by every phase change.\n  const defaultProps = getDefaultProps<UseTransitionProps>(props)\n  // Generate changes to apply in useEffect.\n  const changes = new Map<TransitionState, Change>()\n  const exitingTransitions = useRef(new Map<TransitionState, Change>())\n\n  const forceChange = useRef(false)\n  each(transitions, (t, i) => {\n    const key = t.key\n    const prevPhase = t.phase\n\n    const p: UseTransitionProps<any> = propsFn ? propsFn() : props\n\n    let to: TransitionTo<any>\n    let phase: TransitionPhase\n\n    const propsDelay = callProp(p.delay || 0, key)\n\n    if (prevPhase == TransitionPhase.MOUNT) {\n      to = p.enter\n      phase = TransitionPhase.ENTER\n    } else {\n      const isLeave = keys.indexOf(key) < 0\n      if (prevPhase != TransitionPhase.LEAVE) {\n        if (isLeave) {\n          to = p.leave\n          phase = TransitionPhase.LEAVE\n        } else if ((to = p.update)) {\n          phase = TransitionPhase.UPDATE\n        } else return\n      } else if (!isLeave) {\n        to = p.enter\n        phase = TransitionPhase.ENTER\n      } else return\n    }\n\n    // When \"to\" is a function, it can return (1) an array of \"useSpring\" props,\n    // (2) an async function, or (3) an object with any \"useSpring\" props.\n    to = callProp(to, t.item, i)\n    to = is.obj(to) ? inferTo(to) : { to }\n\n    /**\n     * This would allow us to give different delays for phases.\n     * If we were to do this, we'd have to suffle the prop\n     * spreading below to set delay last.\n     * But if we were going to do that, we should consider letting\n     * the prop trail also be part of a phase.\n     */\n    // if (to.delay) {\n    //   phaseDelay = callProp(to.delay, key)\n    // }\n\n    if (!to.config) {\n      const config = propsConfig || defaultProps.config\n      to.config = callProp(config, t.item, i, phase)\n    }\n\n    delay += trail\n\n    // The payload is used to update the spring props once the current render is committed.\n    const payload: ControllerUpdate<UnknownProps> = {\n      ...defaultProps,\n      // we need to add our props.delay value you here.\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      // This prevents implied resets.\n      reset: false,\n      // Merge any phase-specific props.\n      ...(to as any),\n    }\n\n    if (phase == TransitionPhase.ENTER && is.und(payload.from)) {\n      const p = propsFn ? propsFn() : props\n      // The `initial` prop is used on the first render of our parent component,\n      // as well as when `reset: true` is passed. It overrides the `from` prop\n      // when defined, and it makes `enter` instant when null.\n      const from = is.und(p.initial) || prevTransitions ? p.from : p.initial\n\n      payload.from = callProp(from, t.item, i)\n    }\n\n    const { onResolve } = payload\n    payload.onResolve = result => {\n      callProp(onResolve, result)\n\n      const transitions = usedTransitions.current!\n      const t = transitions.find(t => t.key === key)\n      if (!t) return\n\n      // Reset the phase of a cancelled enter/leave transition, so it can\n      // retry the animation on the next render.\n      if (result.cancelled && t.phase != TransitionPhase.UPDATE) {\n        /**\n         * @legacy Reset the phase of a cancelled enter/leave transition, so it can\n         * retry the animation on the next render.\n         *\n         * Note: leaving this here made the transitioned item respawn.\n         */\n        // t.phase = prevPhase\n        return\n      }\n\n      if (t.ctrl.idle) {\n        const idle = transitions.every(t => t.ctrl.idle)\n        if (t.phase == TransitionPhase.LEAVE) {\n          const expiry = callProp(expires, t.item)\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry\n            t.expired = true\n\n            // Force update once the expiration delay ends.\n            if (!idle && expiryMs > 0) {\n              // The maximum timeout is 2^31-1\n              if (expiryMs <= 0x7fffffff)\n                t.expirationId = setTimeout(forceUpdate, expiryMs)\n              return\n            }\n          }\n        }\n        // Force update once idle and expired items exist.\n        if (idle && transitions.some(t => t.expired)) {\n          /**\n           * Remove the exited transition from the list\n           * this may not exist but we'll try anyway.\n           */\n          exitingTransitions.current.delete(t)\n\n          if (exitBeforeEnter) {\n            /**\n             * If we have exitBeforeEnter == true\n             * we need to force the animation to start\n             */\n            forceChange.current = true\n          }\n\n          forceUpdate()\n        }\n      }\n    }\n\n    const springs = getSprings(t.ctrl, payload)\n\n    /**\n     * Make a separate map for the exiting changes and \"regular\" changes\n     */\n    if (phase === TransitionPhase.LEAVE && exitBeforeEnter) {\n      exitingTransitions.current.set(t, { phase, springs, payload })\n    } else {\n      changes.set(t, { phase, springs, payload })\n    }\n  })\n\n  // The prop overrides from an ancestor.\n  const context = useContext(SpringContext)\n  const prevContext = usePrev(context)\n  const hasContext = context !== prevContext && hasProps(context)\n\n  // Merge the context into each transition.\n  useIsomorphicLayoutEffect(() => {\n    if (hasContext) {\n      each(transitions, t => {\n        t.ctrl.start({ default: context })\n      })\n    }\n  }, [context])\n\n  each(changes, (_, t) => {\n    /**\n     * If we have children to exit because exitBeforeEnter is\n     * set to true, we remove the transitions so they go to back\n     * to their initial state.\n     */\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex(state => state.key === t.key)\n      transitions.splice(ind, 1)\n    }\n  })\n\n  useIsomorphicLayoutEffect(\n    () => {\n      /*\n       * if exitingTransitions.current has a size it means we're exiting before enter\n       * so we want to map through those and fire those first.\n       */\n      each(\n        exitingTransitions.current.size ? exitingTransitions.current : changes,\n        ({ phase, payload }, t) => {\n          const { ctrl } = t\n\n          t.phase = phase\n\n          // Attach the controller to our local ref.\n          ref?.add(ctrl)\n\n          // Merge the context into new items.\n          if (hasContext && phase == TransitionPhase.ENTER) {\n            ctrl.start({ default: context })\n          }\n\n          if (payload) {\n            // Update the injected ref if needed.\n            replaceRef(ctrl, payload.ref)\n\n            /**\n             * When an injected ref exists, the update is postponed\n             * until the ref has its `start` method called.\n             * Unless we have exitBeforeEnter in which case will skip\n             * to enter the new animation straight away as if they \"overlapped\"\n             */\n            if ((ctrl.ref || ref) && !forceChange.current) {\n              ctrl.update(payload)\n            } else {\n              ctrl.start(payload)\n\n              if (forceChange.current) {\n                forceChange.current = false\n              }\n            }\n          }\n        }\n      )\n    },\n    reset ? void 0 : deps\n  )\n\n  const renderTransitions: TransitionFn = render => (\n    <>\n      {transitions.map((t, i) => {\n        const { springs } = changes.get(t) || t.ctrl\n        const elem: any = render({ ...springs }, t.item, t, i)\n        return elem && elem.type ? (\n          <elem.type\n            {...elem.props}\n            key={is.str(t.key) || is.num(t.key) ? t.key : t.ctrl.id}\n            ref={elem.ref}\n          />\n        ) : (\n          elem\n        )\n      })}\n    </>\n  )\n\n  return ref ? [renderTransitions, ref] : renderTransitions\n}\n\n/** Local state for auto-generated item keys */\nlet nextKey = 1\n\nfunction getKeys(\n  items: readonly any[],\n  { key, keys = key }: { key?: ItemKeys; keys?: ItemKeys },\n  prevTransitions: TransitionState[] | null\n): readonly any[] {\n  if (keys === null) {\n    const reused = new Set()\n    return items.map(item => {\n      const t =\n        prevTransitions &&\n        prevTransitions.find(\n          t =>\n            t.item === item &&\n            t.phase !== TransitionPhase.LEAVE &&\n            !reused.has(t)\n        )\n      if (t) {\n        reused.add(t)\n        return t.key\n      }\n      return nextKey++\n    })\n  }\n  return is.und(keys) ? items : is.fun(keys) ? items.map(keys) : toArray(keys)\n}\n", "import { MutableRefObject } from 'react'\nimport { each, onScroll, useIsomorphicLayoutEffect } from '@react-spring/shared'\n\nimport { SpringProps, SpringValues } from '../types'\n\nimport { useSpring } from './useSpring'\n\nexport interface UseScrollOptions extends Omit<SpringProps, 'to' | 'from'> {\n  container?: MutableRefObject<HTMLElement>\n}\n\n/**\n * A small utility abstraction around our signature useSpring hook. It's a great way to create\n * a scroll-linked animation. With either the raw value of distance or a 0-1 progress value.\n * You can either use the scroll values of the whole document, or just a specific element.\n *\n * \n ```jsx\n    import { useScroll, animated } from '@react-spring/web'\n\n    function MyComponent() {\n      const { scrollYProgress } = useScroll()\n\n      return (\n        <animated.div style={{ opacity: scrollYProgress }}>\n          Hello World\n        </animated.div>\n      )\n    }\n  ```\n * \n * @param {UseScrollOptions} useScrollOptions options for the useScroll hook.\n * @param {MutableRefObject<HTMLElement>} useScrollOptions.container the container to listen to scroll events on, defaults to the window.\n *\n * @returns {SpringValues<{scrollX: number; scrollY: number; scrollXProgress: number; scrollYProgress: number}>} SpringValues the collection of values returned from the inner hook\n */\nexport const useScroll = ({\n  container,\n  ...springOptions\n}: UseScrollOptions = {}): SpringValues<{\n  scrollX: number\n  scrollY: number\n  scrollXProgress: number\n  scrollYProgress: number\n}> => {\n  const [scrollValues, api] = useSpring(\n    () => ({\n      scrollX: 0,\n      scrollY: 0,\n      scrollXProgress: 0,\n      scrollYProgress: 0,\n      ...springOptions,\n    }),\n    []\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onScroll(\n      ({ x, y }) => {\n        api.start({\n          scrollX: x.current,\n          scrollXProgress: x.progress,\n          scrollY: y.current,\n          scrollYProgress: y.progress,\n        })\n      },\n      { container: container?.current || undefined }\n    )\n\n    return () => {\n      /**\n       * Stop the springs on unmount.\n       */\n      each(Object.values(scrollValues), value => value.stop())\n\n      cleanupScroll()\n    }\n  }, [])\n\n  return scrollValues\n}\n", "import { MutableRefObject } from 'react'\nimport { onResize, each, useIsomorphicLayoutEffect } from '@react-spring/shared'\n\nimport { SpringProps, SpringValues } from '../types'\n\nimport { useSpring } from './useSpring'\n\nexport interface UseResizeOptions extends Omit<SpringProps, 'to' | 'from'> {\n  container?: MutableRefObject<HTMLElement | null | undefined>\n}\n\n/**\n * A small abstraction around the `useSpring` hook. It returns a `SpringValues` \n * object with the `width` and `height` of the element it's attached to & doesn't \n * necessarily have to be attached to the window, by passing a `container` you \n * can observe that element's size instead.\n * \n ```jsx\n    import { useResize, animated } from '@react-spring/web'\n\n    function MyComponent() {\n      const { width } = useResize()\n\n      return (\n        <animated.div style={{ width }}>\n          Hello World\n        </animated.div>\n      )\n    }\n  ```\n * \n * @param {UseResizeOptions} UseResizeOptions options for the useScroll hook.\n * @param {MutableRefObject<HTMLElement>} UseResizeOptions.container the container to listen to scroll events on, defaults to the window.\n *\n * @returns {SpringValues<{width: number; height: number;}>} SpringValues the collection of values returned from the inner hook\n */\nexport const useResize = ({\n  container,\n  ...springOptions\n}: UseResizeOptions): SpringValues<{\n  width: number\n  height: number\n}> => {\n  const [sizeValues, api] = useSpring(\n    () => ({\n      width: 0,\n      height: 0,\n      ...springOptions,\n    }),\n    []\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onResize(\n      ({ width, height }) => {\n        api.start({\n          width,\n          height,\n          immediate:\n            sizeValues.width.get() === 0 || sizeValues.height.get() === 0,\n        })\n      },\n      { container: container?.current || undefined }\n    )\n\n    return () => {\n      /**\n       * Stop the springs on unmount.\n       */\n      each(Object.values(sizeValues), value => value.stop())\n\n      cleanupScroll()\n    }\n  }, [])\n\n  return sizeValues\n}\n", "import { RefObject, useRef, useState } from 'react'\nimport { is, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { Lookup } from '@react-spring/types'\n\nimport { PickAnimated, SpringValues } from '../types'\nimport { useSpring, UseSpringProps } from './useSpring'\nimport { Valid } from '../types/common'\n\nexport interface IntersectionArgs\n  extends Omit<IntersectionObserverInit, 'root' | 'threshold'> {\n  root?: React.MutableRefObject<HTMLElement>\n  once?: boolean\n  amount?: 'any' | 'all' | number | number[]\n}\n\nconst defaultThresholdOptions = {\n  any: 0,\n  all: 1,\n}\n\nexport function useInView(args?: IntersectionArgs): [RefObject<any>, boolean]\nexport function useInView<Props extends object>(\n  /**\n   * TODO: make this narrower to only accept reserved props.\n   */\n  props: () => Props & Valid<Props, UseSpringProps<Props>>,\n  args?: IntersectionArgs\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [RefObject<any>, SpringValues<State>]\n    : never\n  : never\nexport function useInView<TElement extends HTMLElement>(\n  props?: (() => UseSpringProps<any>) | IntersectionArgs,\n  args?: IntersectionArgs\n) {\n  const [isInView, setIsInView] = useState(false)\n  const ref = useRef<TElement>()\n\n  const propsFn = is.fun(props) && props\n\n  const springsProps = propsFn ? propsFn() : {}\n  const { to = {}, from = {}, ...restSpringProps } = springsProps\n\n  const intersectionArguments = propsFn ? args : props\n\n  const [springs, api] = useSpring(() => ({ from, ...restSpringProps }), [])\n\n  useIsomorphicLayoutEffect(() => {\n    const element = ref.current\n    const {\n      root,\n      once,\n      amount = 'any',\n      ...restArgs\n    } = intersectionArguments ?? {}\n\n    if (\n      !element ||\n      (once && isInView) ||\n      typeof IntersectionObserver === 'undefined'\n    )\n      return\n\n    const activeIntersections = new WeakMap<Element, VoidFunction>()\n\n    const onEnter = () => {\n      if (to) {\n        // @ts-expect-error – TODO: fix this type error\n        api.start(to)\n      }\n\n      setIsInView(true)\n\n      const cleanup = () => {\n        if (from) {\n          api.start(from)\n        }\n        setIsInView(false)\n      }\n\n      return once ? undefined : cleanup\n    }\n\n    const handleIntersection: IntersectionObserverCallback = entries => {\n      entries.forEach(entry => {\n        const onLeave = activeIntersections.get(entry.target)\n\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return\n        }\n\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter()\n          if (is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave)\n          } else {\n            observer.unobserve(entry.target)\n          }\n        } else if (onLeave) {\n          onLeave()\n          activeIntersections.delete(entry.target)\n        }\n      })\n    }\n\n    const observer = new IntersectionObserver(handleIntersection, {\n      root: (root && root.current) || undefined,\n      threshold:\n        typeof amount === 'number' || Array.isArray(amount)\n          ? amount\n          : defaultThresholdOptions[amount],\n      ...restArgs,\n    })\n\n    observer.observe(element)\n\n    return () => observer.unobserve(element)\n  }, [intersectionArguments])\n\n  if (propsFn) {\n    return [ref, springs]\n  }\n\n  return [ref, isInView]\n}\n", "import { NoInfer, UnknownProps } from '@react-spring/types'\nimport { useSpring, UseSpringProps } from '../hooks/useSpring'\nimport { SpringValues, SpringToFn, SpringChain } from '../types'\n\nexport type SpringComponentProps<State extends object = UnknownProps> =\n  unknown &\n    UseSpringProps<State> & {\n      children: (values: SpringValues<State>) => JSX.Element | null\n    }\n\n// Infer state from \"from\" object prop.\nexport function Spring<State extends object>(\n  props: {\n    from: State\n    to?: SpringChain<NoInfer<State>> | SpringToFn<NoInfer<State>>\n  } & Omit<SpringComponentProps<NoInfer<State>>, 'from' | 'to'>\n): JSX.Element | null\n\n// Infer state from \"to\" object prop.\nexport function Spring<State extends object>(\n  props: { to: State } & Omit<SpringComponentProps<NoInfer<State>>, 'to'>\n): JSX.Element | null\n\nexport function Spring({ children, ...props }: any) {\n  return children(useSpring(props))\n}\n", "import { ReactNode } from 'react'\nimport { NoInfer, Falsy } from '@react-spring/types'\nimport { is } from '@react-spring/shared'\n\nimport { Valid } from '../types/common'\nimport { PickAnimated, SpringValues } from '../types'\nimport { UseSpringProps } from '../hooks/useSpring'\nimport { useTrail } from '../hooks/useTrail'\n\nexport type TrailComponentProps<Item, Props extends object = any> = unknown &\n  UseSpringProps<Props> & {\n    items: readonly Item[]\n    children: (\n      item: NoInfer<Item>,\n      index: number\n    ) => ((values: SpringValues<PickAnimated<Props>>) => ReactNode) | Falsy\n  }\n\nexport function Trail<Item, Props extends TrailComponentProps<Item>>({\n  items,\n  children,\n  ...props\n}: Props & Valid<Props, TrailComponentProps<Item, Props>>) {\n  const trails: any[] = useTrail(items.length, props)\n  return items.map((item, index) => {\n    const result = children(item, index)\n    return is.fun(result) ? result(trails[index]) : result\n  })\n}\n", "import { Valid } from '../types/common'\nimport { TransitionComponentProps } from '../types'\nimport { useTransition } from '../hooks'\n\nexport function Transition<Item, Props extends TransitionComponentProps<Item>>(\n  props:\n    | TransitionComponentProps<Item>\n    | (Props & Valid<Props, TransitionComponentProps<Item, Props>>)\n): JSX.Element\n\nexport function Transition({\n  items,\n  children,\n  ...props\n}: TransitionComponentProps<any>) {\n  return useTransition(items, props)(children)\n}\n", "import { FluidValue, deprecateInterpolate } from '@react-spring/shared'\nimport {\n  Constrain,\n  OneOrMore,\n  Animatable,\n  ExtrapolateType,\n  InterpolatorConfig,\n  InterpolatorFn,\n} from '@react-spring/types'\nimport { Interpolation } from './Interpolation'\n\n/** Map the value of one or more dependencies */\nexport const to: Interpolator = (source: any, ...args: [any]) =>\n  new Interpolation(source, args)\n\n/** @deprecated Use the `to` export instead */\nexport const interpolate: Interpolator = (source: any, ...args: [any]) => (\n  deprecateInterpolate(), new Interpolation(source, args)\n)\n\n/** Extract the raw value types that are being interpolated */\nexport type Interpolated<T extends ReadonlyArray<any>> = {\n  [P in keyof T]: T[P] extends infer Element\n    ? Element extends FluidValue<infer U>\n      ? U\n      : Element\n    : never\n}\n\n/**\n * This interpolates one or more `FluidValue` objects.\n * The exported `interpolate` function uses this type.\n */\nexport interface Interpolator {\n  // Tuple of parent values\n  <Input extends ReadonlyArray<any>, Output>(\n    parents: Input,\n    interpolator: (...args: Interpolated<Input>) => Output\n  ): Interpolation<Output>\n\n  // Single parent value\n  <Input, Output>(\n    parent: FluidValue<Input> | Input,\n    interpolator: InterpolatorFn<Input, Output>\n  ): Interpolation<Output>\n\n  // Interpolation config\n  <Out>(\n    parents: OneOrMore<FluidValue>,\n    config: InterpolatorConfig<Out>\n  ): Interpolation<Animatable<Out>>\n\n  // Range shortcuts\n  <Out>(\n    parents: OneOrMore<FluidValue<number>> | FluidValue<number[]>,\n    range: readonly number[],\n    output: readonly Constrain<Out, Animatable>[],\n    extrapolate?: ExtrapolateType\n  ): Interpolation<Animatable<Out>>\n}\n", "import { Arrify, InterpolatorArgs, InterpolatorFn } from '@react-spring/types'\nimport {\n  is,\n  raf,\n  each,\n  isEqual,\n  toArray,\n  frameLoop,\n  FluidValue,\n  getFluidValue,\n  createInterpolator,\n  Globals as G,\n  callFluidObservers,\n  addFluidObserver,\n  removeFluidObserver,\n  hasFluidValue,\n} from '@react-spring/shared'\n\nimport { FrameValue, isFrameValue } from './FrameValue'\nimport {\n  getAnimated,\n  setAnimated,\n  getAnimatedType,\n  getPayload,\n} from '@react-spring/animated'\n\n/**\n * An `Interpolation` is a memoized value that's computed whenever one of its\n * `FluidValue` dependencies has its value changed.\n *\n * Other `FrameValue` objects can depend on this. For example, passing an\n * `Interpolation` as the `to` prop of a `useSpring` call will trigger an\n * animation toward the memoized value.\n */\nexport class Interpolation<\n  Input = any,\n  Output = any,\n> extends FrameValue<Output> {\n  /** Useful for debugging. */\n  key?: string\n\n  /** Equals false when in the frameloop */\n  idle = true\n\n  /** The function that maps inputs values to output */\n  readonly calc: InterpolatorFn<Input, Output>\n\n  /** The inputs which are currently animating */\n  protected _active = new Set<FluidValue>()\n\n  constructor(\n    /** The source of input values */\n    readonly source: unknown,\n    args: InterpolatorArgs<Input, Output>\n  ) {\n    super()\n    this.calc = createInterpolator(...args)\n\n    const value = this._get()\n    const nodeType = getAnimatedType(value)\n\n    // Assume the computed value never changes type.\n    setAnimated(this, nodeType.create(value))\n  }\n\n  advance(_dt?: number) {\n    const value = this._get()\n    const oldValue = this.get()\n    if (!isEqual(value, oldValue)) {\n      getAnimated(this)!.setValue(value)\n      this._onChange(value, this.idle)\n    }\n    // Become idle when all parents are idle or paused.\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this)\n    }\n  }\n\n  protected _get() {\n    const inputs: Arrify<Input> = is.arr(this.source)\n      ? this.source.map(getFluidValue)\n      : (toArray(getFluidValue(this.source)) as any)\n\n    return this.calc(...inputs)\n  }\n\n  protected _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false\n\n      each(getPayload(this)!, node => {\n        node.done = false\n      })\n\n      if (G.skipAnimation) {\n        raf.batchedUpdates(() => this.advance())\n        becomeIdle(this)\n      } else {\n        frameLoop.start(this)\n      }\n    }\n  }\n\n  // Observe our sources only when we're observed.\n  protected _attach() {\n    let priority = 1\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        addFluidObserver(source, this)\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source)\n        }\n        priority = Math.max(priority, source.priority + 1)\n      }\n    })\n    this.priority = priority\n    this._start()\n  }\n\n  // Stop observing our sources once we have no observers.\n  protected _detach() {\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        removeFluidObserver(source, this)\n      }\n    })\n    this._active.clear()\n    becomeIdle(this)\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    // Update our value when an idle parent is changed,\n    // and enter the frameloop when a parent is resumed.\n    if (event.type == 'change') {\n      if (event.idle) {\n        this.advance()\n      } else {\n        this._active.add(event.parent)\n        this._start()\n      }\n    }\n    // Once all parents are idle, the `advance` method runs one more time,\n    // so we should avoid updating the `idle` status here.\n    else if (event.type == 'idle') {\n      this._active.delete(event.parent)\n    }\n    // Ensure our priority is greater than all parents, which means\n    // our value won't be updated until our parents have updated.\n    else if (event.type == 'priority') {\n      this.priority = toArray(this.source).reduce(\n        (highest: number, parent) =>\n          Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1),\n        0\n      )\n    }\n  }\n}\n\n/** Returns true for an idle source. */\nfunction isIdle(source: any) {\n  return source.idle !== false\n}\n\n/** Return true if all values in the given set are idle or paused. */\nfunction checkIdle(active: Set<FluidValue>) {\n  // Parents can be active even when paused, so the `.every` check\n  // removes us from the frameloop if all active parents are paused.\n  return !active.size || Array.from(active).every(isIdle)\n}\n\n/** Become idle if not already idle. */\nfunction becomeIdle(self: Interpolation) {\n  if (!self.idle) {\n    self.idle = true\n\n    each(getPayload(self)!, node => {\n      node.done = true\n    })\n\n    callFluidObservers(self, {\n      type: 'idle',\n      parent: self,\n    })\n  }\n}\n", "import {\n  Globals,\n  frameLoop,\n  createStringInterpolator,\n} from '@react-spring/shared'\nimport { Interpolation } from './Interpolation'\n\n// Sane defaults\nGlobals.assign({\n  createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args),\n})\n\nexport { Globals }\n\n/** Advance all animations by the given time */\nexport const update = frameLoop.advance\n", "export * from './hooks'\nexport * from './components'\nexport * from './interpolate'\nexport * from './constants'\nexport * from './globals'\n\nexport { Controller } from './Controller'\nexport { SpringValue } from './SpringValue'\nexport { SpringContext } from './SpringContext'\nexport { SpringRef } from './SpringRef'\n\nexport { FrameValue } from './FrameValue'\nexport { Interpolation } from './Interpolation'\nexport { BailSignal } from './runAsync'\nexport {\n  createInterpolator,\n  useIsomorphicLayoutEffect,\n  useReducedMotion,\n  easings,\n} from '@react-spring/shared'\nexport { inferTo } from './helpers'\n\nexport * from './types'\nexport * from '@react-spring/types'\n"], "mappings": ";AAAA,SAAS,MAAM,iCAAiC;;;ACAhD;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA,WAAW;AAAA,OACN;AAMA,SAAS,SACd,UACG,MACoC;AACvC,SAAO,GAAG,IAAI,KAAK,IAAI,MAAM,GAAG,IAAI,IAAI;AAC1C;AAGO,IAAM,YAAY,CACvB,OACA,QAEA,UAAU,QACV,CAAC,EACC,OACA,UACC,GAAG,IAAI,KAAK,IAAI,MAAM,GAAG,IAAI,QAAQ,KAAK,EAAE,SAAS,GAAG;AAGtD,IAAM,cAAc,CACzB,MACA,QACI,GAAG,IAAI,IAAI,IAAI,OAAQ,KAAa,GAAG,IAAI;AAU1C,IAAM,iBAAiB,CAC5B,OACA,QAEA,MAAM,YAAY,OACd,MAAM,GAAG,IACT,MAAM,UACJ,MAAM,QAAQ,GAAG,IACjB;AAER,IAAM,gBAAgB,CAAC,UAAe;AAS/B,IAAM,kBAAkB,CAC7B,OACA,YAA8C,kBACxC;AACN,MAAI,OAA0B;AAC9B,MAAI,MAAM,WAAW,MAAM,YAAY,MAAM;AAC3C,YAAQ,MAAM;AACd,WAAO,OAAO,KAAK,KAAK;AAAA,EAC1B;AACA,QAAMA,YAAgB,CAAC;AACvB,aAAW,OAAO,MAAM;AACtB,UAAM,QAAQ,UAAU,MAAM,GAAG,GAAG,GAAG;AACvC,QAAI,CAAC,GAAG,IAAI,KAAK,GAAG;AAClB,MAAAA,UAAS,GAAG,IAAI;AAAA,IAClB;AAAA,EACF;AACA,SAAOA;AACT;AAaO,IAAM,gBAAgB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,iBAEF;AAAA,EACF,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,WAAW;AAAA;AAAA,EAGX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,UAAU;AAAA,EACV,aAAa;AAAA;AAAA,EAGb,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AACZ;AAOA,SAAS,gBACP,OACiC;AACjC,QAAM,UAAe,CAAC;AAEtB,MAAI,QAAQ;AACZ,WAAS,OAAO,CAAC,OAAO,SAAS;AAC/B,QAAI,CAAC,eAAe,IAAI,GAAG;AACzB,cAAQ,IAAI,IAAI;AAChB;AAAA,IACF;AAAA,EACF,CAAC;AAED,MAAI,OAAO;AACT,WAAO;AAAA,EACT;AACF;AAMO,SAAS,QAA0B,OAAsB;AAC9D,QAAMC,MAAK,gBAAgB,KAAK;AAChC,MAAIA,KAAI;AACN,UAAM,MAAW,EAAE,IAAAA,IAAG;AACtB,aAAS,OAAO,CAAC,KAAK,QAAQ,OAAOA,QAAO,IAAI,GAAG,IAAI,IAAI;AAC3D,WAAO;AAAA,EACT;AACA,SAAO,EAAE,GAAG,MAAM;AACpB;AAGO,SAAS,YAAe,OAA6B;AAC1D,UAAQ,cAAc,KAAK;AAC3B,SAAO,GAAG,IAAI,KAAK,IACf,MAAM,IAAI,WAAW,IACrB,iBAAiB,KAAK,IACnB,EAAE,yBAAyB;AAAA,IAC1B,OAAO,CAAC,GAAG,CAAC;AAAA,IACZ,QAAQ,CAAC,OAAO,KAAK;AAAA,EACvB,CAAC,EAAE,CAAC,IACJ;AACR;AAEO,SAAS,SAAS,OAAe;AACtC,aAAW,KAAK;AAAO,WAAO;AAC9B,SAAO;AACT;AAEO,SAAS,UAAUA,KAAS;AACjC,SAAO,GAAG,IAAIA,GAAE,KAAM,GAAG,IAAIA,GAAE,KAAK,GAAG,IAAIA,IAAG,CAAC,CAAC;AAClD;AAGO,SAAS,WAAW,MAAkB,KAAiB;AAC5D,OAAK,KAAK,OAAO,IAAI;AACrB,OAAK,OAAO,IAAI;AAClB;AAGO,SAAS,WAAW,MAAkB,KAAiB;AAC5D,MAAI,OAAO,KAAK,QAAQ,KAAK;AAC3B,SAAK,KAAK,OAAO,IAAI;AACrB,QAAI,IAAI,IAAI;AACZ,SAAK,MAAM;AAAA,EACb;AACF;;;AD/LO,SAAS,SACd,MACA,WACA,YAAY,KACZ;AACA,4BAA0B,MAAM;AAC9B,QAAI,WAAW;AACb,UAAI,YAAY;AAChB,WAAK,MAAM,CAAC,KAAK,MAAM;AACrB,cAAM,cAAc,IAAI;AACxB,YAAI,YAAY,QAAQ;AACtB,cAAI,QAAQ,YAAY,UAAU,CAAC;AAGnC,cAAI,MAAM,KAAK;AAAG,oBAAQ;AAAA;AACrB,wBAAY;AAEjB,eAAK,aAAa,UAAQ;AACxB,iBAAK,KAAK,OAAO,WAAS;AAExB,oBAAM,oBAAoB,MAAM;AAChC,oBAAM,QAAQ,SAAO,QAAQ,SAAS,qBAAqB,GAAG,GAAG;AAAA,YACnE,CAAC;AAAA,UACH,CAAC;AAED,cAAI,MAAM;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,UAAI,IAAkB,QAAQ,QAAQ;AACtC,WAAK,MAAM,SAAO;AAChB,cAAM,cAAc,IAAI;AACxB,YAAI,YAAY,QAAQ;AAEtB,gBAAM,SAAS,YAAY,IAAI,UAAQ;AACrC,kBAAM,IAAI,KAAK;AACf,iBAAK,QAAQ,CAAC;AACd,mBAAO;AAAA,UACT,CAAC;AAGD,cAAI,EAAE,KAAK,MAAM;AACf;AAAA,cAAK;AAAA,cAAa,CAAC,MAAM,MACvB,KAAK,OAAO,CAAC,KAAK,CAAC,GAAG,CAAAC,YAAU,KAAK,MAAM,KAAKA,OAAM,CAAC;AAAA,YACzD;AACA,mBAAO,QAAQ,IAAI,IAAI,MAAM,CAAC;AAAA,UAChC,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;;;AE7EA,SAAS,MAAAC,WAAU;;;ACDnB,SAAS,cAAAC,aAAY,SAAS,cAAc;AAE5C;AAAA,EACE,MAAAC;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,6BAAAC;AAAA,OACK;;;ACTP;AAAA,EACE,MAAAC;AAAA,EACA,OAAAC;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA,WAAAC;AAAA,EACA,YAAAC;AAAA,EACA,aAAAC;AAAA,EACA;AAAA,EACA,iBAAAC;AAAA,EACA,oBAAAC;AAAA,EAEA,WAAWC;AAAA,EACX,sBAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAAC;AAAA,EACA;AAAA,EACA;AAAA,OACK;;;AC3BP,SAAS,MAAAC,KAAI,eAAe;;;ACCrB,IAAM,SAAS;AAAA,EACpB,SAAS,EAAE,SAAS,KAAK,UAAU,GAAG;AAAA,EACtC,QAAQ,EAAE,SAAS,KAAK,UAAU,GAAG;AAAA,EACrC,QAAQ,EAAE,SAAS,KAAK,UAAU,GAAG;AAAA,EACrC,OAAO,EAAE,SAAS,KAAK,UAAU,GAAG;AAAA,EACpC,MAAM,EAAE,SAAS,KAAK,UAAU,GAAG;AAAA,EACnC,UAAU,EAAE,SAAS,KAAK,UAAU,IAAI;AAC1C;;;ADJA,IAAM,WAAgB;AAAA,EACpB,GAAG,OAAQ;AAAA,EACX,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ,QAAQ;AAAA,EAChB,OAAO;AACT;AAEO,IAAM,kBAAN,MAAsB;AAAA,EA2I3B,cAAc;AAnFd;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA8B;AAoF5B,WAAO,OAAO,MAAM,QAAQ;AAAA,EAC9B;AACF;AAQO,SAAS,YACdC,SACA,WACA,eACA;AACA,MAAI,eAAe;AACjB,oBAAgB,EAAE,GAAG,cAAc;AACnC,mBAAe,eAAe,SAAS;AACvC,gBAAY,EAAE,GAAG,eAAe,GAAG,UAAU;AAAA,EAC/C;AAEA,iBAAeA,SAAQ,SAAS;AAChC,SAAO,OAAOA,SAAQ,SAAS;AAE/B,aAAW,OAAO,UAAU;AAC1B,QAAIA,QAAO,GAAG,KAAK,MAAM;AACvB,MAAAA,QAAO,GAAG,IAAI,SAAS,GAAG;AAAA,IAC5B;AAAA,EACF;AAEA,MAAI,EAAE,WAAW,QAAQ,IAAIA;AAC7B,QAAM,EAAE,KAAK,IAAIA;AACjB,MAAI,CAACC,IAAG,IAAI,SAAS,GAAG;AACtB,QAAI,YAAY;AAAM,kBAAY;AAClC,QAAI,UAAU;AAAG,gBAAU;AAC3B,IAAAD,QAAO,UAAU,KAAK,IAAK,IAAI,KAAK,KAAM,WAAW,CAAC,IAAI;AAC1D,IAAAA,QAAO,WAAY,IAAI,KAAK,KAAK,UAAU,OAAQ;AAAA,EACrD;AAEA,SAAOA;AACT;AAIA,SAAS,eACPA,SACA,OACA;AACA,MAAI,CAACC,IAAG,IAAI,MAAM,KAAK,GAAG;AACxB,IAAAD,QAAO,WAAW;AAAA,EACpB,OAAO;AACL,UAAM,kBAAkB,CAACC,IAAG,IAAI,MAAM,OAAO,KAAK,CAACA,IAAG,IAAI,MAAM,QAAQ;AACxE,QACE,mBACA,CAACA,IAAG,IAAI,MAAM,SAAS,KACvB,CAACA,IAAG,IAAI,MAAM,OAAO,KACrB,CAACA,IAAG,IAAI,MAAM,IAAI,GAClB;AACA,MAAAD,QAAO,WAAW;AAClB,MAAAA,QAAO,QAAQ;AAAA,IACjB;AACA,QAAI,iBAAiB;AACnB,MAAAA,QAAO,YAAY;AAAA,IACrB;AAAA,EACF;AACF;;;AEnNA,IAAM,aAA6B,CAAC;AAI7B,IAAM,YAAN,MAAyB;AAAA,EAAzB;AACL,mBAAU;AACV,kBAAmC;AACnC,oBAAqC;AACrC,sBAAgC;AAIhC,kBAAS,IAAI,gBAAgB;AAC7B,qBAAY;AAAA;AACd;;;ACpBA,SAAkB,MAAAE,KAAI,KAAK,WAAWC,UAAS;AAiCxC,SAAS,cACd,QACA,EAAE,KAAK,OAAO,cAAc,OAAO,QAAQ,GAC3B;AAChB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI;AACJ,QAAI;AAEJ,QAAI,SAAS,UAAU,MAAM,UAAU,cAAc,QAAQ,GAAG;AAChE,QAAI,QAAQ;AACV,cAAQ;AAAA,IACV,OAAO;AAEL,UAAI,CAACC,IAAG,IAAI,MAAM,KAAK,GAAG;AACxB,cAAM,SAAS,UAAU,MAAM,OAAO,GAAG;AAAA,MAC3C;AAGA,UAAI,QAAQ,cAAc;AAC1B,UAAI,UAAU,MAAM;AAClB,gBAAQ,MAAM,UAAU,UAAU,OAAO,GAAG;AAAA,MAC9C;AAEA,cAAQ,SAAS,MAAM,SAAS,GAAG,GAAG;AACtC,UAAI,OAAO;AACT,cAAM,YAAY,IAAI,QAAQ;AAC9B,gBAAQ,MAAM;AAAA,MAChB,OAAO;AACL,gBAAQ,OAAO;AACf,iBAAS;AAAA,MACX;AAAA,IACF;AAEA,aAAS,UAAU;AACjB,YAAM,YAAY,IAAI,QAAQ;AAC9B,YAAM,SAAS,OAAO,OAAO;AAC7B,cAAQ,OAAO;AAEf,cAAQ,QAAQ,OAAO,IAAI,IAAI;AAAA,IACjC;AAEA,aAAS,WAAW;AAClB,UAAI,QAAQ,KAAK,CAACC,GAAE,eAAe;AACjC,cAAM,UAAU;AAChB,kBAAU,IAAI,WAAW,SAAS,KAAK;AACvC,cAAM,WAAW,IAAI,OAAO;AAC5B,cAAM,SAAS,IAAI,OAAO;AAAA,MAC5B,OAAO;AACL,gBAAQ;AAAA,MACV;AAAA,IACF;AAEA,aAAS,UAAU;AACjB,UAAI,MAAM,SAAS;AACjB,cAAM,UAAU;AAAA,MAClB;AAEA,YAAM,WAAW,OAAO,OAAO;AAC/B,YAAM,SAAS,OAAO,OAAO;AAG7B,UAAI,WAAW,MAAM,YAAY,IAAI;AACnC,iBAAS;AAAA,MACX;AAEA,UAAI;AACF,gBAAQ,MAAM,EAAE,GAAG,OAAO,QAAQ,OAAO,GAAG,OAAO;AAAA,MACrD,SAAS,KAAP;AACA,eAAO,GAAG;AAAA,MACZ;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;ACzGA;AAAA,EACE,MAAAC;AAAA,EACA,OAAAC;AAAA,EACA;AAAA,EACA,YAAAC;AAAA,EAEA,WAAWC;AAAA,OACN;;;ACHA,IAAM,oBAAoB,CAC/B,QACA,YAEA,QAAQ,UAAU,IACd,QAAQ,CAAC,IACT,QAAQ,KAAK,YAAU,OAAO,SAAS,IACrC,mBAAmB,OAAO,IAAI,CAAC,IAC/B,QAAQ,MAAM,YAAU,OAAO,IAAI,IACjC,cAAc,OAAO,IAAI,CAAC,IAC1B;AAAA,EACE,OAAO,IAAI;AAAA,EACX,QAAQ,MAAM,YAAU,OAAO,QAAQ;AACzC;AAGH,IAAM,gBAAgB,CAAC,WAAgB;AAAA,EAC5C;AAAA,EACA,MAAM;AAAA,EACN,UAAU;AAAA,EACV,WAAW;AACb;AAEO,IAAM,oBAAoB,CAC/B,OACA,UACA,YAAY,WACR;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AACF;AAEO,IAAM,qBAAqB,CAAC,WAAgB;AAAA,EACjD;AAAA,EACA,WAAW;AAAA,EACX,UAAU;AACZ;;;ADKO,SAAS,SACdC,KACA,OACA,OACA,QACgB;AAChB,QAAM,EAAE,QAAQ,UAAU,OAAO,IAAI;AACrC,QAAM,EAAE,SAAS,QAAQ,SAAS,YAAY,IAAI;AAElD,MAAI,CAAC,YAAYA,QAAO,UAAU,CAAC,MAAM,OAAO;AAC9C,WAAO;AAAA,EACT;AAEA,SAAQ,MAAM,WAAW,YAAY;AACnC,UAAM,UAAU;AAChB,UAAM,UAAUA;AAGhB,UAAM,eAAe;AAAA,MAA+B;AAAA,MAAO,CAAC,OAAO;AAAA;AAAA,QAEjE,QAAQ,WAAW,SAAY;AAAA;AAAA,IACjC;AAEA,QAAI;AACJ,QAAI;AAGJ,UAAM,cAAc,IAAI;AAAA,MACtB,CAAC,SAAS,YAAa,cAAc,SAAW,OAAO;AAAA,IACzD;AAEA,UAAM,cAAc,CAAC,eAA2B;AAC9C,YAAM;AAAA;AAAA,QAEH,WAAW,MAAM,YAAY,MAAM,mBAAmB,MAAM;AAAA,QAE5D,WAAW,MAAM,WAAW,kBAAkB,QAAQ,KAAK;AAAA;AAE9D,UAAI,YAAY;AACd,mBAAW,SAAS;AAIpB,aAAK,UAAU;AACf,cAAM;AAAA,MACR;AAAA,IACF;AAEA,UAAM,UAAe,CAAC,MAAW,SAAe;AAG9C,YAAM,aAAa,IAAI,WAAW;AAClC,YAAM,sBAAsB,IAAI,oBAAoB;AAEpD,cAAQ,YAAY;AAClB,YAAIC,GAAE,eAAe;AAMnB,oBAAU,KAAK;AAGf,8BAAoB,SAAS,kBAAkB,QAAQ,KAAK;AAC5D,eAAK,mBAAmB;AACxB,gBAAM;AAAA,QACR;AAEA,oBAAY,UAAU;AAEtB,cAAMC,SAAaC,IAAG,IAAI,IAAI,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,MAAM,IAAI,KAAK;AACpE,QAAAD,OAAM,WAAW;AAEjB,QAAAE,UAAS,cAAc,CAAC,OAAO,QAAQ;AACrC,cAAID,IAAG,IAAID,OAAM,GAAG,CAAC,GAAG;AACtB,YAAAA,OAAM,GAAG,IAAI;AAAA,UACf;AAAA,QACF,CAAC;AAED,cAAMG,UAAS,MAAM,OAAO,MAAMH,MAAK;AACvC,oBAAY,UAAU;AAEtB,YAAI,MAAM,QAAQ;AAChB,gBAAM,IAAI,QAAc,YAAU;AAChC,kBAAM,YAAY,IAAI,MAAM;AAAA,UAC9B,CAAC;AAAA,QACH;AAEA,eAAOG;AAAA,MACT,GAAG;AAAA,IACL;AAEA,QAAI;AAEJ,QAAIJ,GAAE,eAAe;AAKnB,gBAAU,KAAK;AACf,aAAO,kBAAkB,QAAQ,KAAK;AAAA,IACxC;AAEA,QAAI;AACF,UAAI;AAGJ,UAAIE,IAAG,IAAIH,GAAE,GAAG;AACd,qBAAa,OAAO,UAAiB;AACnC,qBAAWE,UAAS,OAAO;AACzB,kBAAM,QAAQA,MAAK;AAAA,UACrB;AAAA,QACF,GAAGF,GAAE;AAAA,MACP,OAGK;AACH,oBAAY,QAAQ,QAAQA,IAAG,SAAS,OAAO,KAAK,KAAK,MAAM,CAAC,CAAC;AAAA,MACnE;AAEA,YAAM,QAAQ,IAAI,CAAC,UAAU,KAAK,WAAW,GAAG,WAAW,CAAC;AAC5D,eAAS,kBAAkB,OAAO,IAAI,GAAG,MAAM,KAAK;AAAA,IAGtD,SAAS,KAAP;AACA,UAAI,eAAe,YAAY;AAC7B,iBAAS,IAAI;AAAA,MACf,WAAW,eAAe,qBAAqB;AAC7C,iBAAS,IAAI;AAAA,MACf,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IAGF,UAAE;AACA,UAAI,UAAU,MAAM,SAAS;AAC3B,cAAM,UAAU;AAChB,cAAM,UAAU,WAAW,SAAS;AACpC,cAAM,UAAU,WAAW,cAAc;AAAA,MAC3C;AAAA,IACF;AAEA,QAAIG,IAAG,IAAI,MAAM,GAAG;AAClB,MAAAG,KAAI,eAAe,MAAM;AACvB,eAAO,QAAQ,QAAQ,OAAO,IAAI;AAAA,MACpC,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT,GAAG;AACL;AAGO,SAAS,UAAU,OAAsB,UAA2B;AACzE,QAAM,MAAM,UAAU,OAAK,EAAE,OAAO,CAAC;AACrC,QAAM,WAAW,MAAM;AACvB,QAAM,YAAY,MAAM;AACxB,QAAM,UAAU,MAAM,UAAU,MAAM,UAAU;AAChD,MAAI;AAAU,UAAM,WAAW;AACjC;AAGO,IAAM,aAAN,cAAyB,MAAM;AAAA,EAEpC,cAAc;AACZ;AAAA,MACE;AAAA,IAEF;AAAA,EACF;AACF;AAEO,IAAM,sBAAN,cAAkC,MAAM;AAAA,EAG7C,cAAc;AACZ,UAAM,qBAAqB;AAAA,EAC7B;AACF;;;AEjOA;AAAA,EACE;AAAA,EACA;AAAA,EACA,cAAAC;AAAA,EACA,WAAWC;AAAA,EACX;AAAA,OACK;AAEP,SAAS,mBAAmB;AAIrB,IAAM,eAAe,CAAC,UAC3B,iBAAiB;AAEnB,IAAI,SAAS;AAON,IAAe,aAAf,cAA2CD,YAGhD;AAAA,EAHK;AAAA;AAIL,SAAS,KAAK;AAKd,SAAU,YAAY;AAAA;AAAA,EAEtB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,UAAkB;AAC7B,QAAI,KAAK,aAAa,UAAU;AAC9B,WAAK,YAAY;AACjB,WAAK,kBAAkB,QAAQ;AAAA,IACjC;AAAA,EACF;AAAA;AAAA,EAGA,MAAS;AACP,UAAM,OAAO,YAAY,IAAI;AAC7B,WAAO,QAAQ,KAAK,SAAS;AAAA,EAC/B;AAAA;AAAA,EAGA,MAAW,MAAgC;AACzC,WAAOC,GAAE,GAAG,MAAM,IAAI;AAAA,EACxB;AAAA;AAAA,EAGA,eAAoB,MAAgC;AAClD,yBAAqB;AACrB,WAAOA,GAAE,GAAG,MAAM,IAAI;AAAA,EACxB;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EAEU,cAAc,OAAe;AACrC,QAAI,SAAS;AAAG,WAAK,QAAQ;AAAA,EAC/B;AAAA,EAEU,gBAAgB,OAAe;AACvC,QAAI,SAAS;AAAG,WAAK,QAAQ;AAAA,EAC/B;AAAA;AAAA,EASU,UAAU;AAAA,EAAC;AAAA;AAAA,EAGX,UAAU;AAAA,EAAC;AAAA;AAAA,EAGX,UAAU,OAAU,OAAO,OAAO;AAC1C,uBAAmB,MAAM;AAAA,MACvB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAGU,kBAAkB,UAAkB;AAC5C,QAAI,CAAC,KAAK,MAAM;AACd,gBAAU,KAAK,IAAI;AAAA,IACrB;AACA,uBAAmB,MAAM;AAAA,MACvB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;ACxGA,IAAM,KAAK,OAAO,IAAI,aAAa;AAEnC,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,YAAY;AAGX,IAAM,cAAc,CAAC,YAAiB,OAAO,EAAE,IAAI,gBAAgB;AAGnE,IAAM,cAAc,CAAC,YAAiB,OAAO,EAAE,IAAI,gBAAgB;AAGnE,IAAM,WAAW,CAAC,YAAiB,OAAO,EAAE,IAAI,aAAa;AAG7D,IAAM,eAAe,CAAC,QAAa,WACxC,SACK,OAAO,EAAE,KAAK,eAAe,eAC7B,OAAO,EAAE,KAAK,CAAC;AAEf,IAAM,eAAe,CAAC,QAAa,WACxC,SAAU,OAAO,EAAE,KAAK,YAAc,OAAO,EAAE,KAAK,CAAC;;;ARqDhD,IAAM,cAAN,cAAmC,WAAc;AAAA,EAmCtD,YAAY,MAAY,MAAY;AAClC,UAAM;AA/BR;AAAA,qBAAY,IAAI,UAAa;AAM7B;AAAA,wBAAsC,CAAC;AAGvC;AAAA,SAAU,SAAwC;AAAA,MAChD,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,YAAY,oBAAI,IAAI;AAAA,MACpB,aAAa,oBAAI,IAAI;AAAA,MACrB,UAAU,oBAAI,IAAI;AAAA,IACpB;AAGA;AAAA,SAAU,gBAAgB,oBAAI,IAA6B;AAG3D;AAAA,SAAU,cAAc;AAGxB;AAAA,SAAU,YAAY;AAEtB,SAAU,oBAAoB;AAM5B,QAAI,CAACC,IAAG,IAAI,IAAI,KAAK,CAACA,IAAG,IAAI,IAAI,GAAG;AAClC,YAAM,QAAQA,IAAG,IAAI,IAAI,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,MAAM,MAAM,KAAK;AACjE,UAAIA,IAAG,IAAI,MAAM,OAAO,GAAG;AACzB,cAAM,UAAU;AAAA,MAClB;AACA,WAAK,MAAM,KAAK;AAAA,IAClB;AAAA,EACF;AAAA;AAAA,EAGA,IAAI,OAAO;AACT,WAAO,EAAE,YAAY,IAAI,KAAK,KAAK,OAAO,YAAY,SAAS,IAAI;AAAA,EACrE;AAAA,EAEA,IAAI,OAAO;AACT,WAAOC,eAAc,KAAK,UAAU,EAAE;AAAA,EACxC;AAAA,EAEA,IAAI,WAA4B;AAC9B,UAAM,OAAOC,aAAY,IAAI;AAC7B,WACE,gBAAgB,gBACZ,KAAK,gBAAgB,IACrB,KAAK,WAAW,EAAE,IAAI,CAAAC,UAAQA,MAAK,gBAAgB,CAAC;AAAA,EAE5D;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AAChB,WAAO,YAAY,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,cAAc;AAChB,WAAO,YAAY,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,SAAS,IAAI;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY;AACd,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA;AAAA,EAGA,QAAQ,IAAY;AAClB,QAAI,OAAO;AACX,QAAI,UAAU;AAEd,UAAM,OAAO,KAAK;AAClB,QAAI,EAAE,SAAS,IAAI;AACnB,UAAM,EAAE,QAAAC,QAAO,IAAI;AAEnB,UAAM,UAAU,WAAW,KAAK,EAAE;AAClC,QAAI,CAAC,WAAW,cAAc,KAAK,EAAE,GAAG;AACtC,iBAAWC,SAAQJ,eAAc,KAAK,EAAE,CAAC;AAAA,IAC3C;AAEA,SAAK,OAAO,QAAQ,CAACE,OAAM,MAAM;AAC/B,UAAIA,MAAK;AAAM;AAEf,YAAMG;AAAA;AAAA,QAEJH,MAAK,eAAe,iBAChB,IACA,UACE,QAAQ,CAAC,EAAE,eACX,SAAU,CAAC;AAAA;AAEnB,UAAI,WAAW,KAAK;AACpB,UAAI,WAAWG;AAEf,UAAI,CAAC,UAAU;AACb,mBAAWH,MAAK;AAGhB,YAAIC,QAAO,WAAW,GAAG;AACvB,UAAAD,MAAK,OAAO;AACZ;AAAA,QACF;AAEA,YAAI,UAAWA,MAAK,eAAe;AACnC,cAAM,OAAO,KAAK,WAAW,CAAC;AAE9B,cAAM,KACJA,MAAK,MAAM,OACPA,MAAK,KACJA,MAAK,KAAKH,IAAG,IAAII,QAAO,QAAQ,IAC7BA,QAAO,SAAS,CAAC,IACjBA,QAAO;AAEjB,YAAI;AAOJ,cAAM,YACJA,QAAO,cACN,QAAQE,MAAK,OAAQ,KAAK,IAAI,GAAG,KAAK,IAAIA,MAAK,IAAI,IAAI,IAAK;AAG/D,YAAI,CAACN,IAAG,IAAII,QAAO,QAAQ,GAAG;AAC5B,cAAI,IAAI;AACR,cAAIA,QAAO,WAAW,GAAG;AAOvB,gBAAI,KAAK,sBAAsBA,QAAO,UAAU;AAE9C,mBAAK,oBAAoBA,QAAO;AAGhC,kBAAID,MAAK,mBAAmB,GAAG;AAE7B,gBAAAA,MAAK,cAAcC,QAAO,WAAWD,MAAK;AAE1C,0BAAUA,MAAK,eAAe;AAAA,cAChC;AAAA,YACF;AAGA,iBAAKC,QAAO,YAAY,KAAK,UAAU,KAAK;AAE5C,gBAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAE5B,YAAAD,MAAK,mBAAmB;AAAA,UAC1B;AAEA,qBAAW,OAAOC,QAAO,OAAO,CAAC,KAAKE,MAAK;AAC3C,sBAAY,WAAWH,MAAK,gBAAgB;AAE5C,qBAAW,KAAK;AAAA,QAClB,WAGSC,QAAO,OAAO;AACrB,gBAAM,QAAQA,QAAO,UAAU,OAAO,QAAQA,QAAO;AACrD,gBAAM,IAAI,KAAK,IAAI,EAAE,IAAI,SAAS,OAAO;AAEzC,qBAAW,OAAQ,MAAM,IAAI,UAAW,IAAI;AAC5C,qBAAW,KAAK,IAAID,MAAK,eAAe,QAAQ,KAAK;AAGrD,qBAAW,KAAK;AAAA,QAClB,OAGK;AACH,qBAAWA,MAAK,gBAAgB,OAAO,KAAKA,MAAK;AAGjD,gBAAM,eAAeC,QAAO,gBAAgB,YAAY;AAGxD,gBAAM,eAAeA,QAAO,QAAQ,IAAIA,QAAO;AAC/C,gBAAM,YAAY,CAACJ,IAAG,IAAI,YAAY;AAGtC,gBAAM,YAAY,QAAQM,MAAKH,MAAK,KAAK,IAAI,OAAOG;AAGpD,cAAI;AAGJ,cAAI,aAAa;AAEjB,gBAAM,OAAO;AACb,gBAAM,WAAW,KAAK,KAAK,KAAK,IAAI;AACpC,mBAAS,IAAI,GAAG,IAAI,UAAU,EAAE,GAAG;AACjC,uBAAW,KAAK,IAAI,QAAQ,IAAI;AAEhC,gBAAI,CAAC,UAAU;AACb,yBAAW,KAAK,IAAIA,MAAK,QAAQ,KAAK;AACtC,kBAAI,UAAU;AACZ;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,WAAW;AACb,2BAAa,YAAYA,OAAM,WAAWA,OAAM;AAGhD,kBAAI,YAAY;AACd,2BAAW,CAAC,WAAW;AACvB,2BAAWA;AAAA,cACb;AAAA,YACF;AAEA,kBAAM,cAAc,CAACF,QAAO,UAAU,QAAY,WAAWE;AAC7D,kBAAM,eAAe,CAACF,QAAO,WAAW,OAAQ;AAChD,kBAAM,gBAAgB,cAAc,gBAAgBA,QAAO;AAE3D,uBAAW,WAAW,eAAe;AACrC,uBAAW,WAAW,WAAW;AAAA,UACnC;AAAA,QACF;AAEA,QAAAD,MAAK,eAAe;AAEpB,YAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,kBAAQ,KAAK,4BAA4B,IAAI;AAC7C,qBAAW;AAAA,QACb;AAAA,MACF;AAGA,UAAI,WAAW,CAAC,QAAQ,CAAC,EAAE,MAAM;AAC/B,mBAAW;AAAA,MACb;AAEA,UAAI,UAAU;AACZ,QAAAA,MAAK,OAAO;AAAA,MACd,OAAO;AACL,eAAO;AAAA,MACT;AAEA,UAAIA,MAAK,SAAS,UAAUC,QAAO,KAAK,GAAG;AACzC,kBAAU;AAAA,MACZ;AAAA,IACF,CAAC;AAED,UAAM,OAAOF,aAAY,IAAI;AAK7B,UAAM,UAAU,KAAK,SAAS;AAC9B,QAAI,MAAM;AAER,YAAM,WAAWD,eAAc,KAAK,EAAE;AAKtC,WAAK,YAAY,YAAY,YAAY,CAACG,QAAO,OAAO;AAEtD,aAAK,SAAS,QAAQ;AACtB,aAAK,UAAU,QAAQ;AAAA,MACzB,WAAW,WAAWA,QAAO,OAAO;AAKlC,aAAK,UAAU,OAAO;AAAA,MACxB;AAEA,WAAK,MAAM;AAAA,IACb,WAAW,SAAS;AAKlB,WAAK,UAAU,OAAO;AAAA,IACxB;AAAA,EACF;AAAA;AAAA,EAGA,IAAI,OAA0B;AAC5B,IAAAG,KAAI,eAAe,MAAM;AACvB,WAAK,MAAM;AAIX,WAAK,OAAO,KAAK;AACjB,WAAK,KAAK,KAAK;AAAA,IACjB,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,SAAK,QAAQ,EAAE,OAAO,KAAK,CAAC;AAAA,EAC9B;AAAA;AAAA,EAGA,SAAS;AACP,SAAK,QAAQ,EAAE,OAAO,MAAM,CAAC;AAAA,EAC/B;AAAA;AAAA,EAGA,SAAS;AACP,QAAI,YAAY,IAAI,GAAG;AACrB,YAAM,EAAE,IAAAD,KAAI,QAAAF,QAAO,IAAI,KAAK;AAC5B,MAAAG,KAAI,eAAe,MAAM;AAEvB,aAAK,SAAS;AAId,YAAI,CAACH,QAAO,OAAO;AACjB,eAAK,KAAKE,KAAI,KAAK;AAAA,QACrB;AAEA,aAAK,MAAM;AAAA,MACb,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,OAAO,OAAwB;AAC7B,UAAM,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC3C,UAAM,KAAK,KAAK;AAChB,WAAO;AAAA,EACT;AAAA,EAeA,MAAMA,KAAU,MAAY;AAC1B,QAAI;AACJ,QAAI,CAACN,IAAG,IAAIM,GAAE,GAAG;AACf,cAAQ,CAACN,IAAG,IAAIM,GAAE,IAAIA,MAAK,EAAE,GAAG,MAAM,IAAAA,IAAG,CAAC;AAAA,IAC5C,OAAO;AACL,cAAQ,KAAK,SAAS,CAAC;AACvB,WAAK,QAAQ,CAAC;AAAA,IAChB;AAEA,WAAO,QAAQ;AAAA,MACb,MAAM,IAAI,WAAS;AACjB,cAAM,KAAK,KAAK,QAAQ,KAAK;AAC7B,eAAO;AAAA,MACT,CAAC;AAAA,IACH,EAAE,KAAK,aAAW,kBAAkB,MAAM,OAAO,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,QAAkB;AACrB,UAAM,EAAE,IAAAA,IAAG,IAAI,KAAK;AAGpB,SAAK,OAAO,KAAK,IAAI,CAAC;AAEtB,cAAU,KAAK,QAAQ,UAAU,KAAK,WAAW;AACjD,IAAAC,KAAI,eAAe,MAAM,KAAK,MAAMD,KAAI,MAAM,CAAC;AAE/C,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,QAAQ;AACN,SAAK,QAAQ,EAAE,OAAO,KAAK,CAAC;AAAA,EAC9B;AAAA;AAAA,EAGA,cAAc,OAAyB;AACrC,QAAI,MAAM,QAAQ,UAAU;AAC1B,WAAK,OAAO;AAAA,IACd,WAAW,MAAM,QAAQ,YAAY;AACnC,WAAK,WAAW,MAAM,WAAW;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQU,aAAa,OAKpB;AACD,UAAM,MAAM,KAAK,OAAO;AAExB,QAAI,EAAE,IAAAA,KAAI,KAAK,IAAI;AAEnB,IAAAA,MAAKN,IAAG,IAAIM,GAAE,IAAIA,IAAG,GAAG,IAAIA;AAC5B,QAAIA,OAAM,QAAQ,UAAUA,GAAE,GAAG;AAC/B,MAAAA,MAAK;AAAA,IACP;AAEA,WAAON,IAAG,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI;AAClC,QAAI,QAAQ,MAAM;AAChB,aAAO;AAAA,IACT;AAGA,UAAM,QAAQ,EAAE,IAAAM,KAAI,KAAK;AAIzB,QAAI,CAAC,YAAY,IAAI,GAAG;AACtB,UAAI,MAAM;AAAS,SAACA,KAAI,IAAI,IAAI,CAAC,MAAMA,GAAE;AAEzC,aAAOL,eAAc,IAAI;AACzB,UAAI,CAACD,IAAG,IAAI,IAAI,GAAG;AACjB,aAAK,KAAK,IAAI;AAAA,MAChB,WAES,CAACE,aAAY,IAAI,GAAG;AAC3B,aAAK,KAAKI,GAAE;AAAA,MACd;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGU,QACR,EAAE,GAAG,MAAM,GACX,QAC6B;AAC7B,UAAM,EAAE,KAAK,aAAa,IAAI;AAG9B,QAAI,MAAM;AACR,aAAO;AAAA,QACL;AAAA,QACA;AAAA,UAAgB;AAAA,UAAO,CAAC,OAAO,SAC7B,MAAM,KAAK,IAAI,IAAI,YAAY,OAAO,GAAG,IAAI;AAAA,QAC/C;AAAA,MACF;AAEF,kBAAc,MAAM,OAAO,SAAS;AACpC,cAAU,MAAM,WAAW,OAAO,IAAI;AAGtC,UAAM,QAAQ,KAAK,aAAa,KAAK;AAErC,QAAI,OAAO,SAAS,IAAI,GAAG;AACzB,YAAM;AAAA,QACJ;AAAA,MAEF;AAAA,IACF;AAEA,UAAM,QAAQ,KAAK;AAEnB,WAAO,cAAc,EAAE,KAAK,aAAa;AAAA,MACvC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,QACP,OAAO,MAAM;AACX,cAAI,CAAC,SAAS,IAAI,GAAG;AACnB,yBAAa,MAAM,IAAI;AACvB,uBAAW,MAAM,UAAU;AAC3B;AAAA,cACE;AAAA,cACA;AAAA,cACA,kBAAkB,MAAM,cAAc,MAAM,KAAK,UAAU,EAAE,CAAC;AAAA,cAC9D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,QAAQ,MAAM;AACZ,cAAI,SAAS,IAAI,GAAG;AAClB,yBAAa,MAAM,KAAK;AACxB,gBAAI,YAAY,IAAI,GAAG;AACrB,mBAAK,QAAQ;AAAA,YACf;AACA,uBAAW,MAAM,WAAW;AAC5B;AAAA,cACE;AAAA,cACA;AAAA,cACA,kBAAkB,MAAM,cAAc,MAAM,KAAK,UAAU,EAAE,CAAC;AAAA,cAC9D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,OAAO,KAAK,OAAO,KAAK,MAAM,KAAK;AAAA,MACrC;AAAA,IACF,CAAC,EAAE,KAAK,YAAU;AAChB,UAAI,MAAM,QAAQ,OAAO,YAAY,EAAE,UAAU,OAAO,OAAO;AAC7D,cAAM,YAAY,iBAAiB,KAAK;AACxC,YAAI,WAAW;AACb,iBAAO,KAAK,QAAQ,WAAW,IAAI;AAAA,QACrC;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA,EAGU,OACR,OACA,OACA,SACM;AAGN,QAAI,MAAM,QAAQ;AAChB,WAAK,KAAK,IAAI;AACd,aAAO,QAAQ,mBAAmB,IAAI,CAAC;AAAA,IACzC;AAGA,UAAM,YAAY,CAACN,IAAG,IAAI,MAAM,EAAE;AAGlC,UAAM,cAAc,CAACA,IAAG,IAAI,MAAM,IAAI;AAItC,QAAI,aAAa,aAAa;AAC5B,UAAI,MAAM,SAAS,KAAK,WAAW;AACjC,aAAK,YAAY,MAAM;AAAA,MACzB,OAAO;AACL,eAAO,QAAQ,mBAAmB,IAAI,CAAC;AAAA,MACzC;AAAA,IACF;AAEA,UAAM,EAAE,KAAK,cAAc,WAAW,KAAK,IAAI;AAC/C,UAAM,EAAE,IAAI,QAAQ,MAAM,SAAS,IAAI;AACvC,QAAI,EAAE,IAAAM,MAAK,QAAQ,OAAO,SAAS,IAAI;AAIvC,QAAI,eAAe,CAAC,cAAc,CAAC,MAAM,WAAWN,IAAG,IAAIM,GAAE,IAAI;AAC/D,MAAAA,MAAK;AAAA,IACP;AAGA,QAAI,MAAM;AAAS,OAACA,KAAI,IAAI,IAAI,CAAC,MAAMA,GAAE;AAGzC,UAAM,iBAAiB,CAAC,QAAQ,MAAM,QAAQ;AAE9C,QAAI,gBAAgB;AAClB,WAAK,OAAO;AAAA,IACd;AAGA,WAAOL,eAAc,IAAI;AAGzB,UAAM,eAAe,CAAC,QAAQK,KAAI,MAAM;AAExC,QAAI,cAAc;AAChB,WAAK,OAAOA,GAAE;AAAA,IAChB;AAGA,UAAM,aAAa,UAAU,MAAM,EAAE;AAErC,UAAM,EAAE,QAAAF,QAAO,IAAI;AACnB,UAAM,EAAE,OAAO,SAAS,IAAIA;AAG5B,QAAI,aAAa,aAAa;AAC5B,MAAAA,QAAO,WAAW;AAAA,IACpB;AAIA,QAAI,MAAM,UAAU,CAAC,YAAY;AAC/B;AAAA,QACEA;AAAA,QACA,SAAS,MAAM,QAAQ,GAAI;AAAA;AAAA,QAE3B,MAAM,WAAW,aAAa,SAC1B,SAAS,aAAa,QAAQ,GAAI,IAClC;AAAA,MACN;AAAA,IACF;AAIA,QAAI,OAAOF,aAAY,IAAI;AAC3B,QAAI,CAAC,QAAQF,IAAG,IAAIM,GAAE,GAAG;AACvB,aAAO,QAAQ,kBAAkB,MAAM,IAAI,CAAC;AAAA,IAC9C;AAGA,UAAM;AAAA;AAAA;AAAA;AAAA,MAIJN,IAAG,IAAI,MAAM,KAAK,IACd,eAAe,CAAC,MAAM,UACtB,CAACA,IAAG,IAAI,IAAI,KAAK,UAAU,MAAM,OAAO,GAAG;AAAA;AAGjD,UAAM,QAAQ,QAAS,OAAa,KAAK,IAAI;AAG7C,UAAM,OAAO,YAAiBM,GAAE;AAGhC,UAAM,eAAeN,IAAG,IAAI,IAAI,KAAKA,IAAG,IAAI,IAAI,KAAKQ,kBAAiB,IAAI;AAG1E,UAAM,YACJ,CAAC,eACA,CAAC,gBACA,UAAU,aAAa,aAAa,MAAM,WAAW,GAAG;AAE5D,QAAI,cAAc;AAChB,YAAM,WAAW,gBAAgBF,GAAE;AACnC,UAAI,aAAa,KAAK,aAAa;AACjC,YAAI,WAAW;AACb,iBAAO,KAAK,KAAK,IAAI;AAAA,QACvB;AACE,gBAAM;AAAA,YACJ,0BAA0B,KAAK,YAAY,YAAY,SAAS;AAAA,UAClE;AAAA,MACJ;AAAA,IACF;AAGA,UAAM,WAAW,KAAK;AAKtB,QAAI,UAAU,cAAcA,GAAE;AAC9B,QAAI,WAAW;AAEf,QAAI,CAAC,SAAS;AAEZ,YAAM,kBAAkB,SAAU,CAAC,YAAY,IAAI,KAAK;AAIxD,UAAI,gBAAgB,iBAAiB;AACnC,mBAAW,QAAQ,YAAY,KAAK,GAAG,IAAI;AAC3C,kBAAU,CAAC;AAAA,MACb;AAGA,UACG,CAAC,QAAQ,KAAK,WAAW,SAAS,KAAK,CAAC,aACzC,CAAC,QAAQF,QAAO,OAAO,KAAK,KAC5B,CAAC,QAAQA,QAAO,UAAU,QAAQ,GAClC;AACA,kBAAU;AAAA,MACZ;AAAA,IACF;AAGA,QAAI,YAAY,YAAY,IAAI,GAAG;AAGjC,UAAI,KAAK,WAAW,CAAC,OAAO;AAC1B,kBAAU;AAAA,MACZ,WAES,CAAC,SAAS;AACjB,aAAK,MAAM,MAAM;AAAA,MACnB;AAAA,IACF;AAEA,QAAI,CAAC,YAAY;AAGf,UAAI,WAAW,cAAc,MAAM,GAAG;AACpC,aAAK,SAAS,KAAK,WAAW;AAC9B,aAAK,WAAW,cAAcE,GAAE,IAC5B,OACA,YAAY,iBACV,CAAC,CAAC,IACFD,SAAQ,IAAI;AAAA,MACpB;AAEA,UAAI,KAAK,aAAa,WAAW;AAC/B,aAAK,YAAY;AAGjB,YAAI,CAAC,aAAa,CAAC,OAAO;AACxB,eAAK,KAAK,MAAM;AAAA,QAClB;AAAA,MACF;AAEA,UAAI,SAAS;AACX,cAAM,EAAE,OAAO,IAAI;AAGnB,QAAAI,MAAK,eAAe,UAAQ,cAAc,MAAM,OAAO,IAAI,CAAC;AAE5D,cAAM,SAAS,kBAAkB,MAAM,cAAc,MAAM,MAAM,CAAC;AAClE,mBAAW,KAAK,eAAe,MAAM;AACrC,aAAK,cAAc,IAAI,OAAO;AAE9B,YAAI,KAAK;AACP,UAAAF,KAAI,eAAe,MAAM;AAEvB,iBAAK,UAAU,CAAC;AAGhB,qBAAS,QAAQ,IAAI;AAIrB,gBAAI,OAAO;AACT,uBAAS,aAAa,QAAQ,MAAM;AAAA,YACtC,OAIK;AACH,mBAAK,UAAU,QAAQ,IAAI;AAAA,YAC7B;AAAA,UACF,CAAC;AAAA,MACL;AAAA,IACF;AAEA,QAAI,OAAO;AACT,WAAK,KAAK,KAAK;AAAA,IACjB;AAEA,QAAI,YAAY;AACd,cAAQ,SAAS,MAAM,IAAI,OAAO,KAAK,QAAQ,IAAI,CAAC;AAAA,IACtD,WAGS,SAAS;AAChB,WAAK,OAAO;AAAA,IACd,WAIS,YAAY,IAAI,KAAK,CAAC,cAAc;AAC3C,WAAK,cAAc,IAAI,OAAO;AAAA,IAChC,OAGK;AACH,cAAQ,cAAc,KAAK,CAAC;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAGU,OAAO,OAA0B;AACzC,UAAM,OAAO,KAAK;AAClB,QAAI,UAAU,KAAK,IAAI;AACrB,UAAI,kBAAkB,IAAI,GAAG;AAC3B,aAAK,QAAQ;AAAA,MACf;AACA,WAAK,KAAK;AACV,UAAI,kBAAkB,IAAI,GAAG;AAC3B,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA,EAEU,UAAU;AAClB,QAAI,WAAW;AAEf,UAAM,EAAE,IAAAD,IAAG,IAAI,KAAK;AACpB,QAAI,cAAcA,GAAE,GAAG;AACrB,uBAAiBA,KAAI,IAAI;AACzB,UAAI,aAAaA,GAAE,GAAG;AACpB,mBAAWA,IAAG,WAAW;AAAA,MAC3B;AAAA,IACF;AAEA,SAAK,WAAW;AAAA,EAClB;AAAA,EAEU,UAAU;AAClB,UAAM,EAAE,IAAAA,IAAG,IAAI,KAAK;AACpB,QAAI,cAAcA,GAAE,GAAG;AACrB,0BAAoBA,KAAI,IAAI;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMU,KAAK,KAAwB,OAAO,MAA4B;AACxE,UAAM,QAAQL,eAAc,GAAG;AAC/B,QAAI,CAACD,IAAG,IAAI,KAAK,GAAG;AAClB,YAAM,UAAUE,aAAY,IAAI;AAChC,UAAI,CAAC,WAAW,CAAC,QAAQ,OAAO,QAAQ,SAAS,CAAC,GAAG;AAEnD,cAAM,WAAW,gBAAgB,KAAK;AACtC,YAAI,CAAC,WAAW,QAAQ,eAAe,UAAU;AAC/C,sBAAY,MAAM,SAAS,OAAO,KAAK,CAAC;AAAA,QAC1C,OAAO;AACL,kBAAQ,SAAS,KAAK;AAAA,QACxB;AAEA,YAAI,SAAS;AACX,UAAAK,KAAI,eAAe,MAAM;AACvB,iBAAK,UAAU,OAAO,IAAI;AAAA,UAC5B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,WAAOL,aAAY,IAAI;AAAA,EACzB;AAAA,EAEU,WAAW;AACnB,UAAM,OAAO,KAAK;AAClB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf;AAAA,QACE;AAAA,QACA;AAAA,QACA,kBAAkB,MAAM,cAAc,MAAM,KAAK,EAAE,CAAC;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEU,UAAU,OAAU,MAAgB;AAC5C,QAAI,CAAC,MAAM;AACT,WAAK,SAAS;AACd,eAAS,KAAK,UAAU,UAAU,OAAO,IAAI;AAAA,IAC/C;AACA,aAAS,KAAK,aAAa,UAAU,OAAO,IAAI;AAChD,UAAM,UAAU,OAAO,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAKU,SAAS;AACjB,UAAM,OAAO,KAAK;AAGlB,IAAAA,aAAY,IAAI,EAAG,MAAMD,eAAc,KAAK,EAAE,CAAC;AAG/C,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,aAAa,KAAK,OAAO,IAAI,UAAQ,KAAK,YAAY;AAAA,IAC7D;AAEA,QAAI,CAAC,YAAY,IAAI,GAAG;AACtB,mBAAa,MAAM,IAAI;AACvB,UAAI,CAAC,SAAS,IAAI,GAAG;AACnB,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA,EAEU,UAAU;AAElB,QAAIS,GAAE,eAAe;AACnB,WAAK,OAAO;AAAA,IACd,OAAO;AACL,MAAAC,WAAU,MAAM,IAAI;AAAA,IACtB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,MAAM,MAAY,QAAkB;AAC5C,QAAI,YAAY,IAAI,GAAG;AACrB,mBAAa,MAAM,KAAK;AAExB,YAAM,OAAO,KAAK;AAClB,MAAAF,MAAK,KAAK,QAAQ,UAAQ;AACxB,aAAK,OAAO;AAAA,MACd,CAAC;AAKD,UAAI,KAAK,UAAU;AACjB,aAAK,WAAW,KAAK,UAAU,KAAK,WAAW;AAAA,MACjD;AAEA,MAAAG,oBAAmB,MAAM;AAAA,QACvB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAED,YAAM,SAAS,SACX,mBAAmB,KAAK,IAAI,CAAC,IAC7B,kBAAkB,KAAK,IAAI,GAAG,cAAc,MAAM,QAAQ,KAAK,EAAE,CAAC;AAEtE,iBAAW,KAAK,eAAe,MAAM;AACrC,UAAI,KAAK,SAAS;AAChB,aAAK,UAAU;AACf,kBAAU,MAAM,UAAU,QAAQ,IAAI;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACF;AAGA,SAAS,cAAiB,QAAwBN,KAAuB;AACvE,QAAM,OAAO,YAAYA,GAAE;AAC3B,QAAM,QAAQ,YAAY,OAAO,IAAI,CAAC;AACtC,SAAO,QAAQ,OAAO,IAAI;AAC5B;AAEO,SAAS,iBACd,OACA,OAAO,MAAM,MACbA,MAAK,MAAM,IACI;AACf,QAAM,UAAU,SAAS,IAAI;AAC7B,MAAI,SAAS;AACX,UAAM,YAAY,YAAY,QAAQ,QAAQ,OAAO;AACrD,UAAM,WAAW,aAAa,OAAO;AACrC,UAAM,QAAQ,CAAC,aAAa,UAAU;AACtC,WAAO,aAAa;AAAA,MAClB,GAAG;AAAA,MACH;AAAA;AAAA,MAGA,SAAS;AAAA;AAAA,MAGT,OAAO;AAAA;AAAA;AAAA;AAAA,MAKP,IAAI,CAAC,WAAW,UAAUA,GAAE,IAAIA,MAAK;AAAA;AAAA,MAGrC,MAAM,QAAQ,MAAM,OAAO;AAAA,MAC3B;AAAA;AAAA;AAAA,MAIA,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF;AASO,SAAS,aAAa,OAAY;AACvC,QAAM,EAAE,IAAAA,KAAI,KAAK,IAAK,QAAQ,QAAQ,KAAK;AAG3C,QAAM,OAAO,oBAAI,IAAY;AAE7B,MAAIN,IAAG,IAAIM,GAAE;AAAG,gBAAYA,KAAI,IAAI;AACpC,MAAIN,IAAG,IAAI,IAAI;AAAG,gBAAY,MAAM,IAAI;AAGxC,QAAM,OAAO,KAAK,OAAO,MAAM,KAAK,IAAI,IAAI;AAE5C,SAAO;AACT;AAKO,SAAS,cAAc,OAAY;AACxC,QAAMa,UAAS,aAAa,KAAK;AACjC,MAAIb,IAAG,IAAIa,QAAO,OAAO,GAAG;AAC1B,IAAAA,QAAO,UAAU,gBAAgBA,OAAM;AAAA,EACzC;AACA,SAAOA;AACT;AAGA,SAAS,YAAY,QAAgB,MAAmB;AACtD,EAAAC,UAAS,QAAQ,CAAC,OAAO,QAAQ,SAAS,QAAQ,KAAK,IAAI,GAAU,CAAC;AACxE;AAGA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,cACP,QACA,OACA,MACA;AACA,SAAO,UAAU,IAAI,IACnB,MAAM,IAAI,MAAM,eAAe,OAAO,IAAI,IACtC,YAAiB,MAAM,IAAI,GAAG,OAAO,GAAG,IACxC;AACR;AAOA,SAAS,UACP,QACA,SACG,MACH;AACA,SAAO,UAAU,IAAI,IAAI,GAAI,IAAmB;AAChD,SAAO,aAAa,IAAI,IAAI,GAAI,IAAmB;AACrD;;;ASnnCA;AAAA,EACE,MAAAC;AAAA,EACA,OAAAC;AAAA,EACA,QAAAC;AAAA,EACA;AAAA,EACA,SAAAC;AAAA,EACA,WAAAC;AAAA,EACA,YAAAC;AAAA,EACA,cAAAC;AAAA,EACA,oBAAAC;AAAA,OAEK;AAuBP,IAAM,iBAAiB,CAAC,WAAW,YAAY,QAAQ;AAEvD,IAAIC,UAAS;AAWN,IAAM,aAAN,MAAgD;AAAA,EA2DrD,YACE,OACAC,QACA;AA7DF,SAAS,KAAKD;AAGd;AAAA,mBAA+B,CAAC;AAGhC;AAAA,iBAAgC,CAAC;AAejC;AAAA,SAAU,eAAe;AAGzB;AAAA,SAAU,UAAU,oBAAI,IAAgB;AAGxC;AAAA,SAAU,WAAW,oBAAI,IAAgB;AAGzC;AAAA,SAAU,WAAW;AAKrB;AAAA,SAAU,SAA8B;AAAA,MACtC,QAAQ;AAAA,MACR,YAAY,oBAAI,IAAI;AAAA,MACpB,aAAa,oBAAI,IAAI;AAAA,MACrB,UAAU,oBAAI,IAAI;AAAA,IACpB;AAGA;AAAA,SAAU,UAAU;AAAA,MAClB,SAAS,oBAAI,IAGX;AAAA,MACF,UAAU,oBAAI,IAGZ;AAAA,MACF,QAAQ,oBAAI,IAGV;AAAA,IACJ;AAME,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,QAAIC,QAAO;AACT,WAAK,SAASA;AAAA,IAChB;AACA,QAAI,OAAO;AACT,WAAK,MAAM,EAAE,SAAS,MAAM,GAAG,MAAM,CAAC;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WACE,CAAC,KAAK,OAAO,WACb,OAAO,OAAO,KAAK,OAA8B,EAAE,MAAM,YAAU;AACjE,aAAO,OAAO,QAAQ,CAAC,OAAO,aAAa,CAAC,OAAO;AAAA,IACrD,CAAC;AAAA,EAEL;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,KAAK,MAAM;AACb,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAGA,MAA4B;AAC1B,UAAM,SAAc,CAAC;AACrB,SAAK,KAAK,CAAC,QAAQ,QAAS,OAAO,GAAG,IAAI,OAAO,IAAI,CAAE;AACvD,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,IAAI,QAAwB;AAC1B,eAAW,OAAO,QAAQ;AACxB,YAAM,QAAQ,OAAO,GAAG;AACxB,UAAI,CAACC,IAAG,IAAI,KAAK,GAAG;AAClB,aAAK,QAAQ,GAAG,EAAE,IAAI,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,OAAO,OAAwC;AAC7C,QAAI,OAAO;AACT,WAAK,MAAM,KAAK,aAAa,KAAK,CAAC;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,OAAsE;AAC1E,QAAI,EAAE,MAAM,IAAI;AAChB,QAAI,OAAO;AACT,cAAQC,SAAa,KAAK,EAAE,IAAI,YAAY;AAAA,IAC9C,OAAO;AACL,WAAK,QAAQ,CAAC;AAAA,IAChB;AAEA,QAAI,KAAK,QAAQ;AACf,aAAO,KAAK,OAAO,MAAM,KAAK;AAAA,IAChC;AAEA,gBAAY,MAAM,KAAK;AACvB,WAAO,iBAAiB,MAAM,KAAK;AAAA,EACrC;AAAA;AAAA,EAeA,KAAK,KAAmC,MAA0B;AAChE,QAAI,QAAQ,CAAC,CAAC,KAAK;AACjB,aAAO;AAAA,IACT;AACA,QAAI,MAAM;AACR,YAAM,UAAU,KAAK;AACrB,MAAAC,MAAKD,SAAQ,IAAI,GAAe,SAAO,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC;AAAA,IACjE,OAAO;AACL,gBAAU,KAAK,QAAQ,KAAK,YAAY;AACxC,WAAK,KAAK,YAAU,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,MAA0B;AAC9B,QAAID,IAAG,IAAI,IAAI,GAAG;AAChB,WAAK,MAAM,EAAE,OAAO,KAAK,CAAC;AAAA,IAC5B,OAAO;AACL,YAAM,UAAU,KAAK;AACrB,MAAAE,MAAKD,SAAQ,IAAI,GAAe,SAAO,QAAQ,GAAG,EAAE,MAAM,CAAC;AAAA,IAC7D;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,OAAO,MAA0B;AAC/B,QAAID,IAAG,IAAI,IAAI,GAAG;AAChB,WAAK,MAAM,EAAE,OAAO,MAAM,CAAC;AAAA,IAC7B,OAAO;AACL,YAAM,UAAU,KAAK;AACrB,MAAAE,MAAKD,SAAQ,IAAI,GAAe,SAAO,QAAQ,GAAG,EAAE,OAAO,CAAC;AAAA,IAC9D;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,KAAK,UAAsD;AACzD,IAAAE,UAAS,KAAK,SAAS,QAAe;AAAA,EACxC;AAAA;AAAA,EAGU,WAAW;AACnB,UAAM,EAAE,SAAS,UAAU,OAAO,IAAI,KAAK;AAE3C,UAAM,SAAS,KAAK,QAAQ,OAAO;AACnC,UAAM,UAAU,KAAK,SAAS,OAAO;AAErC,QAAK,UAAU,CAAC,KAAK,YAAc,WAAW,CAAC,KAAK,UAAW;AAC7D,WAAK,WAAW;AAChB,MAAAJ,OAAM,SAAS,CAAC,CAACK,UAAS,MAAM,MAAM;AACpC,eAAO,QAAQ,KAAK,IAAI;AACxB,QAAAA,SAAQ,QAAQ,MAAM,KAAK,KAAK;AAAA,MAClC,CAAC;AAAA,IACH;AAEA,UAAM,OAAO,CAAC,UAAU,KAAK;AAC7B,UAAM,SAAS,WAAY,QAAQ,OAAO,OAAQ,KAAK,IAAI,IAAI;AAE/D,QAAI,WAAW,SAAS,MAAM;AAC5B,MAAAL,OAAM,UAAU,CAAC,CAACM,WAAU,MAAM,MAAM;AACtC,eAAO,QAAQ;AACf,QAAAA,UAAS,QAAQ,MAAM,KAAK,KAAK;AAAA,MACnC,CAAC;AAAA,IACH;AAGA,QAAI,MAAM;AACR,WAAK,WAAW;AAChB,MAAAN,OAAM,QAAQ,CAAC,CAACO,SAAQ,MAAM,MAAM;AAClC,eAAO,QAAQ;AACf,QAAAA,QAAO,QAAQ,MAAM,KAAK,KAAK;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAGA,cAAc,OAAyB;AACrC,QAAI,MAAM,QAAQ,UAAU;AAC1B,WAAK,SAAS,IAAI,MAAM,MAAM;AAC9B,UAAI,CAAC,MAAM,MAAM;AACf,aAAK,QAAQ,IAAI,MAAM,MAAM;AAAA,MAC/B;AAAA,IACF,WAAW,MAAM,QAAQ,QAAQ;AAC/B,WAAK,QAAQ,OAAO,MAAM,MAAM;AAAA,IAClC;AAEK;AACL,IAAAC,KAAI,QAAQ,KAAK,QAAQ;AAAA,EAC3B;AACF;AAKO,SAAS,iBACd,MACA,OACA;AACA,SAAO,QAAQ,IAAI,MAAM,IAAI,WAAS,YAAY,MAAM,KAAK,CAAC,CAAC,EAAE;AAAA,IAC/D,aAAW,kBAAkB,MAAM,OAAO;AAAA,EAC5C;AACF;AAWA,eAAsB,YACpB,MACA,OACA,QACa;AACb,QAAM,EAAE,MAAM,IAAAC,KAAI,MAAM,MAAM,QAAQ,UAAU,IAAI;AACpD,QAAMC,YAAWT,IAAG,IAAI,MAAM,OAAO,KAAK,MAAM;AAIhD,MAAI,MAAM;AACR,UAAM,OAAO;AAAA,EACf;AAGA,MAAIQ,QAAO;AAAO,UAAM,KAAK;AAC7B,MAAI,SAAS;AAAO,UAAM,OAAO;AAEjC,QAAM,UAAUR,IAAG,IAAIQ,GAAE,KAAKR,IAAG,IAAIQ,GAAE,IAAIA,MAAK;AAChD,MAAI,SAAS;AACX,UAAM,KAAK;AACX,UAAM,SAAS;AACf,QAAIC,WAAU;AACZ,MAAAA,UAAS,SAAS;AAAA,IACpB;AAAA,EACF,OAIK;AACH,IAAAP,MAAK,gBAAgB,SAAO;AAC1B,YAAM,UAAe,MAAM,GAAG;AAC9B,UAAIF,IAAG,IAAI,OAAO,GAAG;AACnB,cAAM,QAAQ,KAAK,SAAS,EAAE,GAAG;AACjC,cAAM,GAAG,IAAK,CAAC,EAAE,UAAU,UAAU,MAAuB;AAC1D,gBAAMU,UAAS,MAAM,IAAI,OAAO;AAChC,cAAIA,SAAQ;AACV,gBAAI,CAAC;AAAU,cAAAA,QAAO,WAAW;AACjC,gBAAI;AAAW,cAAAA,QAAO,YAAY;AAAA,UACpC,OAAO;AAEL,kBAAM,IAAI,SAAS;AAAA,cACjB,OAAO;AAAA,cACP,UAAU,YAAY;AAAA,cACtB,WAAW,aAAa;AAAA,YAC1B,CAAC;AAAA,UACH;AAAA,QACF;AAGA,YAAID,WAAU;AACZ,UAAAA,UAAS,GAAG,IAAI,MAAM,GAAG;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,QAAQ,KAAK,QAAQ;AAG3B,MAAI,MAAM,UAAU,CAAC,MAAM,QAAQ;AACjC,UAAM,SAAS,MAAM;AACrB,IAAAE,YAAW,MAAM,QAAQ,MAAM,aAAa,MAAM,WAAW;AAAA,EAC/D,WAES,MAAM,QAAQ;AACrB,UAAM,QAAQ;AAAA,EAChB;AAEA,QAAM,YAA2B,QAAQ,OAAO,KAAK,KAAK,OAAO,GAAG;AAAA,IAAI,SACtE,KAAK,QAAQ,GAAG,EAAG,MAAM,KAAY;AAAA,EACvC;AAEA,QAAM,SACJ,MAAM,WAAW,QAAQ,eAAe,OAAO,QAAQ,MAAM;AAE/D,MAAI,WAAY,UAAU,MAAM,SAAU;AACxC,aAAS;AAAA,MACP,cAAc,EAAE,KAAK,cAAc,GAAG;AAAA,QACpC;AAAA,QACA;AAAA,QACA,SAAS;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAMC,QAAO,SAAS;AACpB,gBAAI,QAAQ;AACV,wBAAU,OAAO,KAAK,cAAc,CAAC;AACrC,sBAAQ,mBAAmB,IAAI,CAAC;AAAA,YAClC,OAAO;AACL,cAAAA,OAAM,SAAS;AACf;AAAA,gBACE;AAAA,kBACE;AAAA,kBACAA;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAIA,MAAI,MAAM,QAAQ;AAGhB,UAAM,IAAI,QAAc,YAAU;AAChC,YAAM,YAAY,IAAI,MAAM;AAAA,IAC9B,CAAC;AAAA,EACH;AAEA,QAAM,SAAS,kBAAuB,MAAM,MAAM,QAAQ,IAAI,QAAQ,CAAC;AACvE,MAAI,QAAQ,OAAO,YAAY,EAAE,UAAU,OAAO,OAAO;AACvD,UAAM,YAAY,iBAAiB,OAAO,MAAMJ,GAAE;AAClD,QAAI,WAAW;AACb,kBAAY,MAAM,CAAC,SAAS,CAAC;AAC7B,aAAO,YAAY,MAAM,WAAW,IAAI;AAAA,IAC1C;AAAA,EACF;AACA,MAAI,WAAW;AACb,IAAAD,KAAI,eAAe,MAAM,UAAU,QAAQ,MAAM,KAAK,IAAI,CAAC;AAAA,EAC7D;AACA,SAAO;AACT;AAUO,SAAS,WACd,MACA,OACA;AACA,QAAM,UAAU,EAAE,GAAG,KAAK,QAAQ;AAClC,MAAI,OAAO;AACT,IAAAL,MAAKD,SAAQ,KAAK,GAAG,CAACW,WAAe;AACnC,UAAIZ,IAAG,IAAIY,OAAM,IAAI,GAAG;AACtB,QAAAA,SAAQ,aAAaA,MAAK;AAAA,MAC5B;AACA,UAAI,CAACZ,IAAG,IAAIY,OAAM,EAAE,GAAG;AAErB,QAAAA,SAAQ,EAAE,GAAGA,QAAO,IAAI,OAAU;AAAA,MACpC;AACA,qBAAe,SAAgBA,QAAO,SAAO;AAC3C,eAAO,aAAa,GAAG;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,aAAW,MAAM,OAAO;AACxB,SAAO;AACT;AAMO,SAAS,WACd,MACA,SACA;AACA,EAAAT,UAAS,SAAS,CAAC,QAAQ,QAAQ;AACjC,QAAI,CAAC,KAAK,QAAQ,GAAG,GAAG;AACtB,WAAK,QAAQ,GAAG,IAAI;AACpB,MAAAU,kBAAiB,QAAQ,IAAI;AAAA,IAC/B;AAAA,EACF,CAAC;AACH;AAEA,SAAS,aAAa,KAAa,UAA4C;AAC7E,QAAM,SAAS,IAAI,YAAY;AAC/B,SAAO,MAAM;AACb,MAAI,UAAU;AACZ,IAAAA,kBAAiB,QAAQ,QAAQ;AAAA,EACnC;AACA,SAAO;AACT;AAQA,SAAS,eACP,SACA,OACA,QACA;AACA,MAAI,MAAM,MAAM;AACd,IAAAX,MAAK,MAAM,MAAM,SAAO;AACtB,YAAM,SAAS,QAAQ,GAAG,MAAM,QAAQ,GAAG,IAAI,OAAO,GAAG;AACzD,aAAO,cAAc,EAAE,KAAK;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AAQA,SAAS,YAAY,MAAuB,OAAkC;AAC5E,EAAAA,MAAK,OAAO,WAAS;AACnB,mBAAe,KAAK,SAAS,OAAO,SAAO;AACzC,aAAO,aAAa,KAAK,IAAI;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC;AACH;;;ACnhBA,YAAY,WAAW;AACvB,SAAS,kBAAqC;AAC9C,SAAS,kBAAkB;AAapB,IAAM,gBAAgB,CAAC;AAAA,EAC5B;AAAA,EACA,GAAG;AACL,MAAwC;AACtC,QAAM,YAAY,WAAW,GAAG;AAGhC,QAAM,QAAQ,MAAM,SAAS,CAAC,CAAC,UAAU,OACvC,YAAY,MAAM,aAAa,CAAC,CAAC,UAAU;AAG7C,UAAQ,WAAW,OAAO,EAAE,OAAO,UAAU,IAAI,CAAC,OAAO,SAAS,CAAC;AAEnE,QAAM,EAAE,SAAS,IAAI;AACrB,SAAO,oCAAC,YAAS,OAAO,SAAQ,QAAS;AAC3C;AAEA,IAAM,MAAM,YAAY,eAAe,CAAC,CAAkB;AAG1D,cAAc,WAAW,IAAI;AAC7B,cAAc,WAAW,IAAI;AAG7B,SAAS,YAAe,QAAa,MAA2B;AAC9D,SAAO,OAAO,QAAc,oBAAc,IAAI,CAAC;AAC/C,SAAO,SAAS,WAAW;AAC3B,SAAO,SAAS,WAAW;AAC3B,SAAO;AACT;;;AC5CA,SAAS,QAAAY,OAAM,MAAAC,KAAI,2BAA2B;AA8EvC,IAAM,YAAY,MAEA;AACvB,QAAM,UAA+B,CAAC;AAEtC,QAAMC,aAA8B,SAAU,OAAO;AACnD,wBAAoB;AAEpB,UAAM,UAAyB,CAAC;AAEhC,IAAAF,MAAK,SAAS,CAAC,MAAM,MAAM;AACzB,UAAIC,IAAG,IAAI,KAAK,GAAG;AACjB,gBAAQ,KAAK,KAAK,MAAM,CAAC;AAAA,MAC3B,OAAO;AACL,cAAME,UAAS,UAAU,OAAO,MAAM,CAAC;AACvC,YAAIA,SAAQ;AACV,kBAAQ,KAAK,KAAK,MAAMA,OAAM,CAAC;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,EAAAD,WAAU,UAAU;AAGpB,EAAAA,WAAU,MAAM,SAAU,MAAyB;AACjD,QAAI,CAAC,QAAQ,SAAS,IAAI,GAAG;AAC3B,cAAQ,KAAK,IAAI;AAAA,IACnB;AAAA,EACF;AAGA,EAAAA,WAAU,SAAS,SAAU,MAAyB;AACpD,UAAM,IAAI,QAAQ,QAAQ,IAAI;AAC9B,QAAI,CAAC;AAAG,cAAQ,OAAO,GAAG,CAAC;AAAA,EAC7B;AAGA,EAAAA,WAAU,QAAQ,WAAY;AAC5B,IAAAF,MAAK,SAAS,UAAQ,KAAK,MAAM,GAAG,SAAS,CAAC;AAC9C,WAAO;AAAA,EACT;AAGA,EAAAE,WAAU,SAAS,WAAY;AAC7B,IAAAF,MAAK,SAAS,UAAQ,KAAK,OAAO,GAAG,SAAS,CAAC;AAC/C,WAAO;AAAA,EACT;AAGA,EAAAE,WAAU,MAAM,SACd,QAGA;AACA,IAAAF,MAAK,SAAS,CAAC,MAAM,MAAM;AACzB,YAAMG,UAASF,IAAG,IAAI,MAAM,IAAI,OAAO,GAAG,IAAI,IAAI;AAClD,UAAIE,SAAQ;AACV,aAAK,IAAIA,OAAM;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,EAAAD,WAAU,QAAQ,SAAU,OAA4C;AACtE,UAAM,UAAyB,CAAC;AAEhC,IAAAF,MAAK,SAAS,CAAC,MAAM,MAAM;AACzB,UAAIC,IAAG,IAAI,KAAK,GAAG;AACjB,gBAAQ,KAAK,KAAK,MAAM,CAAC;AAAA,MAC3B,OAAO;AACL,cAAME,UAAS,KAAK,UAAU,OAAO,MAAM,CAAC;AAC5C,YAAIA,SAAQ;AACV,kBAAQ,KAAK,KAAK,MAAMA,OAAM,CAAC;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAGA,EAAAD,WAAU,OAAO,WAAY;AAC3B,IAAAF,MAAK,SAAS,UAAQ,KAAK,KAAK,GAAG,SAAS,CAAC;AAC7C,WAAO;AAAA,EACT;AAEA,EAAAE,WAAU,SAAS,SAAU,OAA2C;AACtE,IAAAF,MAAK,SAAS,CAAC,MAAM,MAAM,KAAK,OAAO,KAAK,UAAU,OAAO,MAAM,CAAC,CAAC,CAAC;AACtE,WAAO;AAAA,EACT;AAGA,QAAM,YAAY,SAChB,KACA,MACA,OACA;AACA,WAAOC,IAAG,IAAI,GAAG,IAAI,IAAI,OAAO,IAAI,IAAI;AAAA,EAC1C;AAEA,EAAAC,WAAU,YAAY;AAEtB,SAAOA;AACT;;;AZ9GO,SAAS,WACd,QACA,OACA,MACK;AACL,QAAM,UAAUE,IAAG,IAAI,KAAK,KAAK;AACjC,MAAI,WAAW,CAAC;AAAM,WAAO,CAAC;AAG9B,QAAM,MAAM;AAAA,IACV,MAAO,WAAW,UAAU,UAAU,IAAI,UAAU,IAAI;AAAA,IACxD,CAAC;AAAA,EACH;AAYA,QAAM,WAAW,OAAO,CAAC;AACzB,QAAM,cAAc,eAAe;AAGnC,QAAM,QAAQ;AAAA,IACZ,OAAc;AAAA,MACZ,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,MAAM,MAAMC,UAAS;AACnB,cAAMC,WAAU,WAAW,MAAMD,QAAO;AAIxC,cAAM,eACJ,SAAS,UAAU,KACnB,CAAC,MAAM,MAAM,UACb,CAAC,OAAO,KAAKC,QAAO,EAAE,KAAK,SAAO,CAAC,KAAK,QAAQ,GAAG,CAAC;AAEtD,eAAO,eACH,iBAAiB,MAAMD,QAAO,IAC9B,IAAI,QAAa,aAAW;AAC1B,qBAAW,MAAMC,QAAO;AACxB,gBAAM,MAAM,KAAK,MAAM;AACrB,oBAAQ,iBAAiB,MAAMD,QAAO,CAAC;AAAA,UACzC,CAAC;AACD,sBAAY;AAAA,QACd,CAAC;AAAA,MACP;AAAA,IACF;AAAA,IACA,CAAC;AAAA,EACH;AAEA,QAAM,QAAQ,OAAO,CAAC,GAAG,MAAM,KAAK,CAAC;AACrC,QAAM,UAAiB,CAAC;AAGxB,QAAM,aAAa,QAAQ,MAAM,KAAK;AAItC,UAAQ,MAAM;AAEZ,IAAAE,MAAK,MAAM,QAAQ,MAAM,QAAQ,UAAU,GAAG,UAAQ;AACpD,iBAAW,MAAM,GAAG;AACpB,WAAK,KAAK,IAAI;AAAA,IAChB,CAAC;AACD,UAAM,QAAQ,SAAS;AAEvB,mBAAe,YAAY,MAAM;AAAA,EACnC,GAAG,CAAC,MAAM,CAAC;AAGX,UAAQ,MAAM;AACZ,mBAAe,GAAG,KAAK,IAAI,YAAY,MAAM,CAAC;AAAA,EAChD,GAAG,IAAI;AAGP,WAAS,eAAe,YAAoB,UAAkB;AAC5D,aAAS,IAAI,YAAY,IAAI,UAAU,KAAK;AAC1C,YAAM,OACJ,MAAM,QAAQ,CAAC,MACd,MAAM,QAAQ,CAAC,IAAI,IAAI,WAAW,MAAM,MAAM,KAAK;AAEtD,YAAMC,UAA8B,UAChC,QAAQ,GAAG,IAAI,IACd,MAAc,CAAC;AAEpB,UAAIA,SAAQ;AACV,gBAAQ,CAAC,IAAI,cAAcA,OAAM;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAKA,QAAM,UAAU,MAAM,QAAQ,IAAI,CAAC,MAAM,MAAM,WAAW,MAAM,QAAQ,CAAC,CAAC,CAAC;AAE3E,QAAM,UAAUC,YAAW,aAAa;AACxC,QAAM,cAAc,QAAQ,OAAO;AACnC,QAAM,aAAa,YAAY,eAAe,SAAS,OAAO;AAE9D,EAAAC,2BAA0B,MAAM;AAC9B,aAAS;AAGT,UAAM,QAAQ,MAAM;AAGpB,UAAM,EAAE,MAAM,IAAI;AAClB,QAAI,MAAM,QAAQ;AAChB,YAAM,QAAQ,CAAC;AACf,MAAAH,MAAK,OAAO,QAAM,GAAG,CAAC;AAAA,IACxB;AAGA,IAAAA,MAAK,MAAM,SAAS,CAAC,MAAM,MAAM;AAE/B,WAAK,IAAI,IAAI;AAGb,UAAI,YAAY;AACd,aAAK,MAAM,EAAE,SAAS,QAAQ,CAAC;AAAA,MACjC;AAGA,YAAMC,UAAS,QAAQ,CAAC;AACxB,UAAIA,SAAQ;AAEV,mBAAW,MAAMA,QAAO,GAAG;AAI3B,YAAI,KAAK,KAAK;AACZ,eAAK,MAAM,KAAKA,OAAM;AAAA,QACxB,OAAO;AACL,eAAK,MAAMA,OAAM;AAAA,QACnB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAGD,UAAQ,MAAM,MAAM;AAClB,IAAAD,MAAK,MAAM,OAAO,UAAQ,KAAK,KAAK,IAAI,CAAC;AAAA,EAC3C,CAAC;AAID,QAAM,SAAS,QAAQ,IAAI,QAAM,EAAE,GAAG,EAAE,EAAE;AAE1C,SAAO,MAAM,CAAC,QAAQ,GAAG,IAAI;AAC/B;;;ADvKO,SAAS,UAAU,OAAY,MAAuB;AAC3D,QAAM,OAAOI,IAAG,IAAI,KAAK;AACzB,QAAM,CAAC,CAAC,MAAM,GAAG,GAAG,IAAI;AAAA,IACtB;AAAA,IACA,OAAO,QAAQ,CAAC,KAAK;AAAA,IACrB,OAAO,QAAQ,CAAC,IAAI;AAAA,EACtB;AACA,SAAO,QAAQ,UAAU,UAAU,IAAI,CAAC,QAAQ,GAAG,IAAI;AACzD;;;ActEA,SAAS,gBAAgB;AAKzB,IAAM,gBAAgB,MAAM,UAAe;AAEpC,IAAM,eAAe,MAC1B,SAAS,aAAa,EAAE,CAAC;;;ACR3B,SAAS,aAAa,WAAAC,gBAAe;AAwB9B,IAAM,iBAAiB,CAC5B,SACA,UACG;AACH,QAAM,cAAc,YAAY,MAAM,IAAI,YAAY,SAAS,KAAK,CAAC;AAErE,EAAAC,SAAQ,MAAM,MAAM;AAClB,gBAAY,KAAK;AAAA,EACnB,CAAC;AAED,SAAO;AACT;;;ACnCA,SAAS,QAAAC,OAAM,MAAAC,MAAI,6BAAAC,kCAAiC;AAiF7C,SAAS,SACd,QACA,UACA,MACA;AACA,QAAM,UAAUC,KAAG,IAAI,QAAQ,KAAK;AACpC,MAAI,WAAW,CAAC;AAAM,WAAO,CAAC;AAG9B,MAAI,UAAU;AACd,MAAI,YAAmC;AAEvC,QAAM,SAAS;AAAA,IACb;AAAA,IACA,CAAC,GAAG,SAAS;AACX,YAAM,QAAQ,UAAU,QAAQ,GAAG,IAAI,IAAI;AAC3C,kBAAY,MAAM;AAClB,gBAAU,WAAW,MAAM;AAE3B,aAAO;AAAA,IACT;AAAA;AAAA;AAAA,IAGA,QAAQ,CAAC,CAAC,CAAC;AAAA,EACb;AAEA,EAAAC,2BAA0B,MAAM;AAI9B,IAAAC,MAAK,OAAO,CAAC,EAAE,SAAS,CAAC,MAAM,MAAM;AACnC,YAAM,SAAS,OAAO,CAAC,EAAE,QAAQ,KAAK,UAAU,IAAI,GAAG;AAKvD,iBAAW,MAAM,SAAS;AAO1B,UAAI,KAAK,KAAK;AACZ,YAAI,QAAQ;AACV,eAAK,OAAO,EAAE,IAAI,OAAO,QAAQ,CAAC;AAAA,QACpC;AAEA;AAAA,MACF;AAEA,UAAI,QAAQ;AACV,aAAK,MAAM,EAAE,IAAI,OAAO,QAAQ,CAAC;AAAA,MACnC,OAAO;AACL,aAAK,MAAM;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,GAAG,IAAI;AAEP,MAAI,WAAW,UAAU,UAAU,GAAG;AACpC,UAAM,MAAM,aAAa,OAAO,CAAC;AAEjC,QAAI,WAAW,IAAI,CAACC,WAAU,MAAM,MAAM;AACxC,YAAM,QAAQH,KAAG,IAAIG,SAAQ,IAAIA,UAAS,GAAG,IAAI,IAAIA;AACrD,UAAI,OAAO;AACT,cAAM,SAAS,IAAI,QAAQ,KAAK,MAAM,UAAU,IAAI,GAAG;AACvD,YAAI;AAAQ,gBAAM,KAAK,OAAO;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,CAAC;AACjB;;;AC3JA,YAAYC,YAAW;AACvB,SAAS,cAAAC,aAAY,UAAAC,SAAQ,WAAAC,gBAAe;AAE5C;AAAA,EACE,MAAAC;AAAA,EACA,WAAAC;AAAA,EACA,kBAAAC;AAAA,EACA,WAAAC;AAAA,EACA,WAAAC;AAAA,EACA,QAAAC;AAAA,EACA,6BAAAC;AAAA,OACK;AA6DA,SAAS,cACd,MACA,OACA,MACK;AACL,QAAM,UAAUC,KAAG,IAAI,KAAK,KAAK;AAEjC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB;AAAA,IACA,KAAK;AAAA,IACL,QAAQ;AAAA,EACV,IAA6B,UAAU,QAAQ,IAAI;AAGnD,QAAM,MAAMC;AAAA,IACV,MAAO,WAAW,UAAU,UAAU,IAAI,UAAU,IAAI;AAAA,IACxD,CAAC;AAAA,EACH;AAGA,QAAM,QAAQC,SAAQ,IAAI;AAC1B,QAAM,cAAiC,CAAC;AAGxC,QAAM,kBAAkBC,QAAiC,IAAI;AAC7D,QAAM,kBAAkB,QAAQ,OAAO,gBAAgB;AAEvD,EAAAC,2BAA0B,MAAM;AAC9B,oBAAgB,UAAU;AAAA,EAC5B,CAAC;AAED,EAAAC,SAAQ,MAAM;AASZ,IAAAC,MAAK,aAAa,OAAK;AACrB,WAAK,IAAI,EAAE,IAAI;AACf,QAAE,KAAK,MAAM;AAAA,IACf,CAAC;AAGD,WAAO,MAAM;AACX,MAAAA,MAAK,gBAAgB,SAAU,OAAK;AAClC,YAAI,EAAE,SAAS;AACb,uBAAa,EAAE,YAAa;AAAA,QAC9B;AACA,mBAAW,EAAE,MAAM,GAAG;AACtB,UAAE,KAAK,KAAK,IAAI;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAMD,QAAM,OAAO,QAAQ,OAAO,UAAU,QAAQ,IAAI,OAAO,eAAe;AAGxE,QAAM,UAAW,SAAS,gBAAgB,WAAY,CAAC;AACvD,EAAAF;AAAA,IAA0B,MACxBE,MAAK,SAAS,CAAC,EAAE,MAAM,MAAM,IAAI,MAAM;AACrC,iBAAW,MAAM,GAAG;AACpB,eAAS,aAAa,MAAM,GAAG;AAAA,IACjC,CAAC;AAAA,EACH;AAGA,QAAM,SAAmB,CAAC;AAC1B,MAAI;AACF,IAAAA,MAAK,iBAAiB,CAAC,GAAG,MAAM;AAE9B,UAAI,EAAE,SAAS;AACb,qBAAa,EAAE,YAAa;AAC5B,gBAAQ,KAAK,CAAC;AAAA,MAChB,OAAO;AACL,YAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,YAAI,CAAC;AAAG,sBAAY,CAAC,IAAI;AAAA,MAC3B;AAAA,IACF,CAAC;AAGH,EAAAA,MAAK,OAAO,CAAC,MAAM,MAAM;AACvB,QAAI,CAAC,YAAY,CAAC,GAAG;AACnB,kBAAY,CAAC,IAAI;AAAA,QACf,KAAK,KAAK,CAAC;AAAA,QACX;AAAA,QACA;AAAA,QACA,MAAM,IAAI,WAAW;AAAA,MACvB;AAEA,kBAAY,CAAC,EAAE,KAAK,OAAO;AAAA,IAC7B;AAAA,EACF,CAAC;AAID,MAAI,OAAO,QAAQ;AACjB,QAAI,IAAI;AACR,UAAM,EAAE,MAAM,IAA6B,UAAU,QAAQ,IAAI;AACjE,IAAAA,MAAK,QAAQ,CAAC,UAAU,cAAc;AACpC,YAAM,IAAI,gBAAiB,SAAS;AACpC,UAAI,CAAC,UAAU;AACb,YAAI,YAAY,QAAQ,CAAC;AACzB,oBAAY,CAAC,IAAI,EAAE,GAAG,GAAG,MAAM,MAAM,QAAQ,EAAE;AAAA,MACjD,WAAW,OAAO;AAChB,oBAAY,OAAO,EAAE,GAAG,GAAG,CAAC;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAIN,KAAG,IAAI,IAAI,GAAG;AAChB,gBAAY,KAAK,CAAC,GAAG,MAAM,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC;AAAA,EACjD;AAGA,MAAI,QAAQ,CAAC;AAGb,QAAM,cAAcO,gBAAe;AAGnC,QAAM,eAAe,gBAAoC,KAAK;AAE9D,QAAM,UAAU,oBAAI,IAA6B;AACjD,QAAM,qBAAqBJ,QAAO,oBAAI,IAA6B,CAAC;AAEpE,QAAM,cAAcA,QAAO,KAAK;AAChC,EAAAG,MAAK,aAAa,CAAC,GAAG,MAAM;AAC1B,UAAM,MAAM,EAAE;AACd,UAAM,YAAY,EAAE;AAEpB,UAAM,IAA6B,UAAU,QAAQ,IAAI;AAEzD,QAAIE;AACJ,QAAI;AAEJ,UAAM,aAAa,SAAS,EAAE,SAAS,GAAG,GAAG;AAE7C,QAAI,kCAAoC;AACtC,MAAAA,MAAK,EAAE;AACP;AAAA,IACF,OAAO;AACL,YAAM,UAAU,KAAK,QAAQ,GAAG,IAAI;AACpC,UAAI,kCAAoC;AACtC,YAAI,SAAS;AACX,UAAAA,MAAK,EAAE;AACP;AAAA,QACF,WAAYA,MAAK,EAAE,QAAS;AAC1B;AAAA,QACF;AAAO;AAAA,MACT,WAAW,CAAC,SAAS;AACnB,QAAAA,MAAK,EAAE;AACP;AAAA,MACF;AAAO;AAAA,IACT;AAIA,IAAAA,MAAK,SAASA,KAAI,EAAE,MAAM,CAAC;AAC3B,IAAAA,MAAKR,KAAG,IAAIQ,GAAE,IAAI,QAAQA,GAAE,IAAI,EAAE,IAAAA,IAAG;AAarC,QAAI,CAACA,IAAG,QAAQ;AACd,YAAMC,UAAS,eAAe,aAAa;AAC3C,MAAAD,IAAG,SAAS,SAASC,SAAQ,EAAE,MAAM,GAAG,KAAK;AAAA,IAC/C;AAEA,aAAS;AAGT,UAAM,UAA0C;AAAA,MAC9C,GAAG;AAAA;AAAA,MAEH,OAAO,aAAa;AAAA,MACpB,KAAK;AAAA,MACL,WAAW,EAAE;AAAA;AAAA,MAEb,OAAO;AAAA;AAAA,MAEP,GAAID;AAAA,IACN;AAEA,QAAI,gCAAkCR,KAAG,IAAI,QAAQ,IAAI,GAAG;AAC1D,YAAMU,KAAI,UAAU,QAAQ,IAAI;AAIhC,YAAM,OAAOV,KAAG,IAAIU,GAAE,OAAO,KAAK,kBAAkBA,GAAE,OAAOA,GAAE;AAE/D,cAAQ,OAAO,SAAS,MAAM,EAAE,MAAM,CAAC;AAAA,IACzC;AAEA,UAAM,EAAE,UAAU,IAAI;AACtB,YAAQ,YAAY,YAAU;AAC5B,eAAS,WAAW,MAAM;AAE1B,YAAMC,eAAc,gBAAgB;AACpC,YAAMC,KAAID,aAAY,KAAK,CAAAC,OAAKA,GAAE,QAAQ,GAAG;AAC7C,UAAI,CAACA;AAAG;AAIR,UAAI,OAAO,aAAaA,GAAE,gCAAiC;AAQzD;AAAA,MACF;AAEA,UAAIA,GAAE,KAAK,MAAM;AACf,cAAM,OAAOD,aAAY,MAAM,CAAAC,OAAKA,GAAE,KAAK,IAAI;AAC/C,YAAIA,GAAE,8BAAgC;AACpC,gBAAM,SAAS,SAAS,SAASA,GAAE,IAAI;AACvC,cAAI,WAAW,OAAO;AACpB,kBAAM,WAAW,WAAW,OAAO,IAAI;AACvC,YAAAA,GAAE,UAAU;AAGZ,gBAAI,CAAC,QAAQ,WAAW,GAAG;AAEzB,kBAAI,YAAY;AACd,gBAAAA,GAAE,eAAe,WAAW,aAAa,QAAQ;AACnD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,QAAQD,aAAY,KAAK,CAAAC,OAAKA,GAAE,OAAO,GAAG;AAK5C,6BAAmB,QAAQ,OAAOA,EAAC;AAEnC,cAAI,iBAAiB;AAKnB,wBAAY,UAAU;AAAA,UACxB;AAEA,sBAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAEA,UAAM,UAAU,WAAW,EAAE,MAAM,OAAO;AAK1C,QAAI,iCAAmC,iBAAiB;AACtD,yBAAmB,QAAQ,IAAI,GAAG,EAAE,OAAO,SAAS,QAAQ,CAAC;AAAA,IAC/D,OAAO;AACL,cAAQ,IAAI,GAAG,EAAE,OAAO,SAAS,QAAQ,CAAC;AAAA,IAC5C;AAAA,EACF,CAAC;AAGD,QAAM,UAAUC,YAAW,aAAa;AACxC,QAAM,cAAcC,SAAQ,OAAO;AACnC,QAAM,aAAa,YAAY,eAAe,SAAS,OAAO;AAG9D,EAAAV,2BAA0B,MAAM;AAC9B,QAAI,YAAY;AACd,MAAAE,MAAK,aAAa,OAAK;AACrB,UAAE,KAAK,MAAM,EAAE,SAAS,QAAQ,CAAC;AAAA,MACnC,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AAEZ,EAAAA,MAAK,SAAS,CAAC,GAAG,MAAM;AAMtB,QAAI,mBAAmB,QAAQ,MAAM;AACnC,YAAM,MAAM,YAAY,UAAU,WAAS,MAAM,QAAQ,EAAE,GAAG;AAC9D,kBAAY,OAAO,KAAK,CAAC;AAAA,IAC3B;AAAA,EACF,CAAC;AAED,EAAAF;AAAA,IACE,MAAM;AAKJ,MAAAE;AAAA,QACE,mBAAmB,QAAQ,OAAO,mBAAmB,UAAU;AAAA,QAC/D,CAAC,EAAE,OAAO,QAAQ,GAAG,MAAM;AACzB,gBAAM,EAAE,KAAK,IAAI;AAEjB,YAAE,QAAQ;AAGV,eAAK,IAAI,IAAI;AAGb,cAAI,cAAc,8BAAgC;AAChD,iBAAK,MAAM,EAAE,SAAS,QAAQ,CAAC;AAAA,UACjC;AAEA,cAAI,SAAS;AAEX,uBAAW,MAAM,QAAQ,GAAG;AAQ5B,iBAAK,KAAK,OAAO,QAAQ,CAAC,YAAY,SAAS;AAC7C,mBAAK,OAAO,OAAO;AAAA,YACrB,OAAO;AACL,mBAAK,MAAM,OAAO;AAElB,kBAAI,YAAY,SAAS;AACvB,4BAAY,UAAU;AAAA,cACxB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ,SAAS;AAAA,EACnB;AAEA,QAAM,oBAAkC,YACtC,4DACG,YAAY,IAAI,CAAC,GAAG,MAAM;AACzB,UAAM,EAAE,QAAQ,IAAI,QAAQ,IAAI,CAAC,KAAK,EAAE;AACxC,UAAM,OAAY,OAAO,EAAE,GAAG,QAAQ,GAAG,EAAE,MAAM,GAAG,CAAC;AACrD,WAAO,QAAQ,KAAK,OAClB;AAAA,MAAC,KAAK;AAAA,MAAL;AAAA,QACE,GAAG,KAAK;AAAA,QACT,KAAKN,KAAG,IAAI,EAAE,GAAG,KAAKA,KAAG,IAAI,EAAE,GAAG,IAAI,EAAE,MAAM,EAAE,KAAK;AAAA,QACrD,KAAK,KAAK;AAAA;AAAA,IACZ,IAEA;AAAA,EAEJ,CAAC,CACH;AAGF,SAAO,MAAM,CAAC,mBAAmB,GAAG,IAAI;AAC1C;AAGA,IAAI,UAAU;AAEd,SAAS,QACP,OACA,EAAE,KAAK,OAAO,IAAI,GAClB,iBACgB;AAChB,MAAI,SAAS,MAAM;AACjB,UAAM,SAAS,oBAAI,IAAI;AACvB,WAAO,MAAM,IAAI,UAAQ;AACvB,YAAM,IACJ,mBACA,gBAAgB;AAAA,QACd,CAAAY,OACEA,GAAE,SAAS,QACXA,GAAE,iCACF,CAAC,OAAO,IAAIA,EAAC;AAAA,MACjB;AACF,UAAI,GAAG;AACL,eAAO,IAAI,CAAC;AACZ,eAAO,EAAE;AAAA,MACX;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,SAAOZ,KAAG,IAAI,IAAI,IAAI,QAAQA,KAAG,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAIE,SAAQ,IAAI;AAC7E;;;AC5dA,SAAS,QAAAa,OAAM,UAAU,6BAAAC,kCAAiC;AAmCnD,IAAM,YAAY,CAAC;AAAA,EACxB;AAAA,EACA,GAAG;AACL,IAAsB,CAAC,MAKjB;AACJ,QAAM,CAAC,cAAc,GAAG,IAAI;AAAA,IAC1B,OAAO;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,GAAG;AAAA,IACL;AAAA,IACA,CAAC;AAAA,EACH;AAEA,EAAAC,2BAA0B,MAAM;AAC9B,UAAM,gBAAgB;AAAA,MACpB,CAAC,EAAE,GAAG,EAAE,MAAM;AACZ,YAAI,MAAM;AAAA,UACR,SAAS,EAAE;AAAA,UACX,iBAAiB,EAAE;AAAA,UACnB,SAAS,EAAE;AAAA,UACX,iBAAiB,EAAE;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,MACA,EAAE,WAAW,WAAW,WAAW,OAAU;AAAA,IAC/C;AAEA,WAAO,MAAM;AAIX,MAAAC,MAAK,OAAO,OAAO,YAAY,GAAG,WAAS,MAAM,KAAK,CAAC;AAEvD,oBAAc;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;;;AC/EA,SAAS,UAAU,QAAAC,OAAM,6BAAAC,kCAAiC;AAmCnD,IAAM,YAAY,CAAC;AAAA,EACxB;AAAA,EACA,GAAG;AACL,MAGM;AACJ,QAAM,CAAC,YAAY,GAAG,IAAI;AAAA,IACxB,OAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,GAAG;AAAA,IACL;AAAA,IACA,CAAC;AAAA,EACH;AAEA,EAAAC,2BAA0B,MAAM;AAC9B,UAAM,gBAAgB;AAAA,MACpB,CAAC,EAAE,OAAO,OAAO,MAAM;AACrB,YAAI,MAAM;AAAA,UACR;AAAA,UACA;AAAA,UACA,WACE,WAAW,MAAM,IAAI,MAAM,KAAK,WAAW,OAAO,IAAI,MAAM;AAAA,QAChE,CAAC;AAAA,MACH;AAAA,MACA,EAAE,WAAW,WAAW,WAAW,OAAU;AAAA,IAC/C;AAEA,WAAO,MAAM;AAIX,MAAAC,MAAK,OAAO,OAAO,UAAU,GAAG,WAAS,MAAM,KAAK,CAAC;AAErD,oBAAc;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;;;AC5EA,SAAoB,UAAAC,SAAQ,YAAAC,iBAAgB;AAC5C,SAAS,MAAAC,MAAI,6BAAAC,kCAAiC;AAc9C,IAAM,0BAA0B;AAAA,EAC9B,KAAK;AAAA,EACL,KAAK;AACP;AAcO,SAAS,UACd,OACA,MACA;AACA,QAAM,CAAC,UAAU,WAAW,IAAIC,UAAS,KAAK;AAC9C,QAAM,MAAMC,QAAiB;AAE7B,QAAM,UAAUC,KAAG,IAAI,KAAK,KAAK;AAEjC,QAAM,eAAe,UAAU,QAAQ,IAAI,CAAC;AAC5C,QAAM,EAAE,IAAAC,MAAK,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,gBAAgB,IAAI;AAEnD,QAAM,wBAAwB,UAAU,OAAO;AAE/C,QAAM,CAAC,SAAS,GAAG,IAAI,UAAU,OAAO,EAAE,MAAM,GAAG,gBAAgB,IAAI,CAAC,CAAC;AAEzE,EAAAC,2BAA0B,MAAM;AAC9B,UAAM,UAAU,IAAI;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT,GAAG;AAAA,IACL,IAAI,yBAAyB,CAAC;AAE9B,QACE,CAAC,WACA,QAAQ,YACT,OAAO,yBAAyB;AAEhC;AAEF,UAAM,sBAAsB,oBAAI,QAA+B;AAE/D,UAAM,UAAU,MAAM;AACpB,UAAID,KAAI;AAEN,YAAI,MAAMA,GAAE;AAAA,MACd;AAEA,kBAAY,IAAI;AAEhB,YAAM,UAAU,MAAM;AACpB,YAAI,MAAM;AACR,cAAI,MAAM,IAAI;AAAA,QAChB;AACA,oBAAY,KAAK;AAAA,MACnB;AAEA,aAAO,OAAO,SAAY;AAAA,IAC5B;AAEA,UAAM,qBAAmD,aAAW;AAClE,cAAQ,QAAQ,WAAS;AACvB,cAAM,UAAU,oBAAoB,IAAI,MAAM,MAAM;AAEpD,YAAI,MAAM,mBAAmB,QAAQ,OAAO,GAAG;AAC7C;AAAA,QACF;AAEA,YAAI,MAAM,gBAAgB;AACxB,gBAAM,aAAa,QAAQ;AAC3B,cAAID,KAAG,IAAI,UAAU,GAAG;AACtB,gCAAoB,IAAI,MAAM,QAAQ,UAAU;AAAA,UAClD,OAAO;AACL,qBAAS,UAAU,MAAM,MAAM;AAAA,UACjC;AAAA,QACF,WAAW,SAAS;AAClB,kBAAQ;AACR,8BAAoB,OAAO,MAAM,MAAM;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,WAAW,IAAI,qBAAqB,oBAAoB;AAAA,MAC5D,MAAO,QAAQ,KAAK,WAAY;AAAA,MAChC,WACE,OAAO,WAAW,YAAY,MAAM,QAAQ,MAAM,IAC9C,SACA,wBAAwB,MAAM;AAAA,MACpC,GAAG;AAAA,IACL,CAAC;AAED,aAAS,QAAQ,OAAO;AAExB,WAAO,MAAM,SAAS,UAAU,OAAO;AAAA,EACzC,GAAG,CAAC,qBAAqB,CAAC;AAE1B,MAAI,SAAS;AACX,WAAO,CAAC,KAAK,OAAO;AAAA,EACtB;AAEA,SAAO,CAAC,KAAK,QAAQ;AACvB;;;ACtGO,SAAS,OAAO,EAAE,UAAU,GAAG,MAAM,GAAQ;AAClD,SAAO,SAAS,UAAU,KAAK,CAAC;AAClC;;;ACvBA,SAAS,MAAAG,YAAU;AAgBZ,SAAS,MAAqD;AAAA,EACnE;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAA2D;AACzD,QAAM,SAAgB,SAAS,MAAM,QAAQ,KAAK;AAClD,SAAO,MAAM,IAAI,CAAC,MAAM,UAAU;AAChC,UAAM,SAAS,SAAS,MAAM,KAAK;AACnC,WAAOC,KAAG,IAAI,MAAM,IAAI,OAAO,OAAO,KAAK,CAAC,IAAI;AAAA,EAClD,CAAC;AACH;;;AClBO,SAAS,WAAW;AAAA,EACzB;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAkC;AAChC,SAAO,cAAc,OAAO,KAAK,EAAE,QAAQ;AAC7C;;;AChBA,SAAqB,wBAAAC,6BAA4B;;;ACCjD;AAAA,EACE,MAAAC;AAAA,EACA,OAAAC;AAAA,EACA,QAAAC;AAAA,EACA,WAAAC;AAAA,EACA,WAAAC;AAAA,EACA,aAAAC;AAAA,EAEA,iBAAAC;AAAA,EACA;AAAA,EACA,WAAWC;AAAA,EACX,sBAAAC;AAAA,EACA,oBAAAC;AAAA,EACA,uBAAAC;AAAA,EACA,iBAAAC;AAAA,OACK;AAGP;AAAA,EACE,eAAAC;AAAA,EACA,eAAAC;AAAA,EACA,mBAAAC;AAAA,EACA,cAAAC;AAAA,OACK;AAUA,IAAM,gBAAN,cAGG,WAAmB;AAAA,EAa3B,YAEW,QACT,MACA;AACA,UAAM;AAHG;AAVX;AAAA,gBAAO;AAMP;AAAA,SAAU,UAAU,oBAAI,IAAgB;AAQtC,SAAK,OAAO,mBAAmB,GAAG,IAAI;AAEtC,UAAM,QAAQ,KAAK,KAAK;AACxB,UAAM,WAAWD,iBAAgB,KAAK;AAGtC,IAAAD,aAAY,MAAM,SAAS,OAAO,KAAK,CAAC;AAAA,EAC1C;AAAA,EAEA,QAAQ,KAAc;AACpB,UAAM,QAAQ,KAAK,KAAK;AACxB,UAAM,WAAW,KAAK,IAAI;AAC1B,QAAI,CAACG,SAAQ,OAAO,QAAQ,GAAG;AAC7B,MAAAJ,aAAY,IAAI,EAAG,SAAS,KAAK;AACjC,WAAK,UAAU,OAAO,KAAK,IAAI;AAAA,IACjC;AAEA,QAAI,CAAC,KAAK,QAAQ,UAAU,KAAK,OAAO,GAAG;AACzC,iBAAW,IAAI;AAAA,IACjB;AAAA,EACF;AAAA,EAEU,OAAO;AACf,UAAM,SAAwBK,KAAG,IAAI,KAAK,MAAM,IAC5C,KAAK,OAAO,IAAIC,cAAa,IAC5BC,SAAQD,eAAc,KAAK,MAAM,CAAC;AAEvC,WAAO,KAAK,KAAK,GAAG,MAAM;AAAA,EAC5B;AAAA,EAEU,SAAS;AACjB,QAAI,KAAK,QAAQ,CAAC,UAAU,KAAK,OAAO,GAAG;AACzC,WAAK,OAAO;AAEZ,MAAAE,OAAKL,YAAW,IAAI,GAAI,UAAQ;AAC9B,aAAK,OAAO;AAAA,MACd,CAAC;AAED,UAAIM,GAAE,eAAe;AACnB,QAAAC,KAAI,eAAe,MAAM,KAAK,QAAQ,CAAC;AACvC,mBAAW,IAAI;AAAA,MACjB,OAAO;AACL,QAAAC,WAAU,MAAM,IAAI;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGU,UAAU;AAClB,QAAI,WAAW;AACf,IAAAH,OAAKD,SAAQ,KAAK,MAAM,GAAG,YAAU;AACnC,UAAIK,eAAc,MAAM,GAAG;AACzB,QAAAC,kBAAiB,QAAQ,IAAI;AAAA,MAC/B;AACA,UAAI,aAAa,MAAM,GAAG;AACxB,YAAI,CAAC,OAAO,MAAM;AAChB,eAAK,QAAQ,IAAI,MAAM;AAAA,QACzB;AACA,mBAAW,KAAK,IAAI,UAAU,OAAO,WAAW,CAAC;AAAA,MACnD;AAAA,IACF,CAAC;AACD,SAAK,WAAW;AAChB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAGU,UAAU;AAClB,IAAAL,OAAKD,SAAQ,KAAK,MAAM,GAAG,YAAU;AACnC,UAAIK,eAAc,MAAM,GAAG;AACzB,QAAAE,qBAAoB,QAAQ,IAAI;AAAA,MAClC;AAAA,IACF,CAAC;AACD,SAAK,QAAQ,MAAM;AACnB,eAAW,IAAI;AAAA,EACjB;AAAA;AAAA,EAGA,cAAc,OAAyB;AAGrC,QAAI,MAAM,QAAQ,UAAU;AAC1B,UAAI,MAAM,MAAM;AACd,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ,IAAI,MAAM,MAAM;AAC7B,aAAK,OAAO;AAAA,MACd;AAAA,IACF,WAGS,MAAM,QAAQ,QAAQ;AAC7B,WAAK,QAAQ,OAAO,MAAM,MAAM;AAAA,IAClC,WAGS,MAAM,QAAQ,YAAY;AACjC,WAAK,WAAWP,SAAQ,KAAK,MAAM,EAAE;AAAA,QACnC,CAAC,SAAiB,WAChB,KAAK,IAAI,UAAU,aAAa,MAAM,IAAI,OAAO,WAAW,KAAK,CAAC;AAAA,QACpE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAGA,SAAS,OAAO,QAAa;AAC3B,SAAO,OAAO,SAAS;AACzB;AAGA,SAAS,UAAU,QAAyB;AAG1C,SAAO,CAAC,OAAO,QAAQ,MAAM,KAAK,MAAM,EAAE,MAAM,MAAM;AACxD;AAGA,SAAS,WAAW,MAAqB;AACvC,MAAI,CAAC,KAAK,MAAM;AACd,SAAK,OAAO;AAEZ,IAAAC,OAAKL,YAAW,IAAI,GAAI,UAAQ;AAC9B,WAAK,OAAO;AAAA,IACd,CAAC;AAED,IAAAY,oBAAmB,MAAM;AAAA,MACvB,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACF;;;AD/KO,IAAM,KAAmB,CAAC,WAAgB,SAC/C,IAAI,cAAc,QAAQ,IAAI;AAGzB,IAAM,cAA4B,CAAC,WAAgB,UACxDC,sBAAqB,GAAG,IAAI,cAAc,QAAQ,IAAI;;;AEjBxD;AAAA,EACE;AAAA,EACA,aAAAC;AAAA,EACA;AAAA,OACK;AAIP,QAAQ,OAAO;AAAA,EACb;AAAA,EACA,IAAI,CAAC,QAAQ,SAAS,IAAI,cAAc,QAAQ,IAAI;AACtD,CAAC;AAKM,IAAM,SAASC,WAAU;;;ACFhC;AAAA,EACE,sBAAAC;AAAA,EACA,6BAAAC;AAAA,EACA;AAAA,EACA,WAAAC;AAAA,OACK;AAIP,cAAc;", "names": ["defaults", "to", "update", "is", "useContext", "is", "each", "useIsomorphicLayoutEffect", "is", "raf", "each", "toArray", "eachProp", "frameLoop", "getFluidValue", "isAnimatedString", "G", "callFluidObservers", "getAnimated", "is", "config", "is", "is", "G", "is", "G", "is", "raf", "eachProp", "G", "to", "G", "props", "is", "eachProp", "result", "raf", "FluidValue", "G", "is", "getFluidValue", "getAnimated", "node", "config", "toArray", "to", "raf", "isAnimatedString", "each", "G", "frameLoop", "callFluidObservers", "update", "eachProp", "is", "raf", "each", "flush", "toArray", "eachProp", "flushCalls", "addFluidObserver", "nextId", "flush", "is", "toArray", "each", "eachProp", "onStart", "onChange", "onRest", "raf", "to", "defaults", "result", "flushCalls", "props", "addFluidObserver", "each", "is", "SpringRef", "update", "is", "updates", "springs", "each", "update", "useContext", "useIsomorphicLayoutEffect", "is", "useOnce", "useOnce", "each", "is", "useIsomorphicLayoutEffect", "is", "useIsomorphicLayoutEffect", "each", "propsArg", "React", "useContext", "useRef", "useMemo", "is", "toArray", "useForceUpdate", "useOnce", "usePrev", "each", "useIsomorphicLayoutEffect", "is", "useMemo", "toArray", "useRef", "useIsomorphicLayoutEffect", "useOnce", "each", "useForceUpdate", "to", "config", "p", "transitions", "t", "useContext", "usePrev", "each", "useIsomorphicLayoutEffect", "useIsomorphicLayoutEffect", "each", "each", "useIsomorphicLayoutEffect", "useIsomorphicLayoutEffect", "each", "useRef", "useState", "is", "useIsomorphicLayoutEffect", "useState", "useRef", "is", "to", "useIsomorphicLayoutEffect", "is", "is", "deprecateInterpolate", "is", "raf", "each", "isEqual", "toArray", "frameLoop", "getFluidValue", "G", "callFluidObservers", "addFluidObserver", "removeFluidObserver", "hasFluidValue", "getAnimated", "setAnimated", "getAnimatedType", "getPayload", "isEqual", "is", "getFluidValue", "toArray", "each", "G", "raf", "frameLoop", "hasFluidValue", "addFluidObserver", "removeFluidObserver", "callFluidObservers", "deprecateInterpolate", "frameLoop", "frameLoop", "createInterpolator", "useIsomorphicLayoutEffect", "easings"]}