{"version": 3, "sources": ["../src/globals.ts", "../src/helpers.ts", "../src/FrameLoop.ts", "../src/clamp.ts", "../src/colors.ts", "../src/colorMatchers.ts", "../src/normalizeColor.ts", "../src/colorToRgba.ts", "../src/createInterpolator.ts", "../src/easings.ts", "../src/fluids.ts", "../src/regexs.ts", "../src/variableToRgba.ts", "../src/stringInterpolation.ts", "../src/deprecations.ts", "../src/isAnimatedString.ts", "../src/dom-events/scroll/index.ts", "../src/dom-events/resize/resizeElement.ts", "../src/dom-events/resize/resizeWindow.ts", "../src/dom-events/resize/index.ts", "../src/progress.ts", "../src/dom-events/scroll/ScrollHandler.ts", "../src/hooks/useConstant.ts", "../src/hooks/useForceUpdate.ts", "../src/hooks/useIsMounted.ts", "../src/hooks/useIsomorphicLayoutEffect.ts", "../src/hooks/useMemoOne.ts", "../src/hooks/useOnce.ts", "../src/hooks/usePrev.ts", "../src/hooks/useReducedMotion.ts", "../src/index.ts"], "sourcesContent": ["import { raf, <PERSON><PERSON><PERSON> } from '@react-spring/rafz'\nimport {\n  OneOrMore,\n  InterpolatorConfig,\n  InterpolatorArgs,\n} from '@react-spring/types'\n\nimport { FluidValue } from './fluids'\nimport type { OpaqueAnimation } from './FrameLoop'\nimport { noop } from './helpers'\n\n//\n// Required\n//\n\nexport let createStringInterpolator: (\n  config: InterpolatorConfig<string>\n) => (input: number) => string\n\n//\n// Optional\n//\n\nexport let to: <Input, Output>(\n  source: OneOrMore<FluidValue>,\n  args: InterpolatorArgs<Input, Output>\n) => FluidValue<Output>\n\nexport let colors = null as { [key: string]: number } | null\n\nexport let skipAnimation = false as boolean\n\nexport let willAdvance: (animation: OpaqueAnimation) => void = noop\n\n//\n// Configuration\n//\n\nexport interface AnimatedGlobals {\n  /** Returns a new `Interpolation` object */\n  to?: typeof to\n  /** Used to measure frame length. Read more [here](https://developer.mozilla.org/en-US/docs/Web/API/Performance/now) */\n  now?: typeof raf.now\n  /** Provide custom color names for interpolation */\n  colors?: typeof colors\n  /** Make all animations instant and skip the frameloop entirely */\n  skipAnimation?: typeof skipAnimation\n  /** Provide custom logic for string interpolation */\n  createStringInterpolator?: typeof createStringInterpolator\n  /** Schedule a function to run on the next frame */\n  requestAnimationFrame?: (cb: () => void) => void\n  /** Event props are called with `batchedUpdates` to reduce extraneous renders */\n  batchedUpdates?: typeof raf.batchedUpdates\n  /** @internal Exposed for testing purposes */\n  willAdvance?: typeof willAdvance\n  /** sets the global frameLoop setting for the global raf instance */\n  frameLoop?: Rafz['frameLoop']\n}\n\nexport const assign = (globals: AnimatedGlobals) => {\n  if (globals.to) to = globals.to\n  if (globals.now) raf.now = globals.now\n  if (globals.colors !== undefined) colors = globals.colors\n  if (globals.skipAnimation != null) skipAnimation = globals.skipAnimation\n  if (globals.createStringInterpolator)\n    createStringInterpolator = globals.createStringInterpolator\n  if (globals.requestAnimationFrame) raf.use(globals.requestAnimationFrame)\n  if (globals.batchedUpdates) raf.batchedUpdates = globals.batchedUpdates\n  if (globals.willAdvance) willAdvance = globals.willAdvance\n  if (globals.frameLoop) raf.frameLoop = globals.frameLoop\n}\n", "import { Lookup, Arrify, AnyFn, Any } from '@react-spring/types'\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop() {}\n\nexport const defineHidden = (obj: any, key: any, value: any) =>\n  Object.defineProperty(obj, key, { value, writable: true, configurable: true })\n\ntype IsType<U> = <T>(arg: T & any) => arg is Narrow<T, U>\ntype Narrow<T, U> = [T] extends [Any] ? U : [T] extends [U] ? Extract<T, U> : U\n\ntype PlainObject<T> = Exclude<T & Lookup, Function | readonly any[]>\n\nexport const is = {\n  arr: Array.isArray as IsType<readonly any[]>,\n  obj: <T>(a: T & any): a is PlainObject<T> =>\n    !!a && a.constructor.name === 'Object',\n  fun: ((a: unknown) => typeof a === 'function') as IsType<Function>,\n  str: (a: unknown): a is string => typeof a === 'string',\n  num: (a: unknown): a is number => typeof a === 'number',\n  und: (a: unknown): a is undefined => a === undefined,\n}\n\n/** Compare animatable values */\nexport function isEqual(a: any, b: any) {\n  if (is.arr(a)) {\n    if (!is.arr(b) || a.length !== b.length) return false\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) return false\n    }\n    return true\n  }\n  return a === b\n}\n\ntype EachFn<Value, Key, This> = (this: This, value: Value, key: Key) => void\ntype Eachable<Value = any, Key = any, This = any> = {\n  forEach(cb: EachFn<Value, Key, This>, ctx?: This): void\n}\n\n/** Minifiable `.forEach` call */\nexport const each = <Value, Key, This>(\n  obj: Eachable<Value, Key, This>,\n  fn: EachFn<Value, Key, This>\n) => obj.forEach(fn)\n\n/** Iterate the properties of an object */\nexport function eachProp<T extends object, This>(\n  obj: T,\n  fn: (\n    this: This,\n    value: T extends any[] ? T[number] : T[keyof T],\n    key: string\n  ) => void,\n  ctx?: This\n) {\n  if (is.arr(obj)) {\n    for (let i = 0; i < obj.length; i++) {\n      fn.call(ctx as any, obj[i] as any, `${i}`)\n    }\n    return\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      fn.call(ctx as any, obj[key] as any, key)\n    }\n  }\n}\n\nexport const toArray = <T>(a: T): Arrify<Exclude<T, void>> =>\n  is.und(a) ? [] : is.arr(a) ? (a as any) : [a]\n\n/** Copy the `queue`, then iterate it after the `queue` is cleared */\nexport function flush<P, T>(\n  queue: Map<P, T>,\n  iterator: (entry: [P, T]) => void\n): void\nexport function flush<T>(queue: Set<T>, iterator: (value: T) => void): void\nexport function flush(queue: any, iterator: any) {\n  if (queue.size) {\n    const items = Array.from(queue)\n    queue.clear()\n    each(items, iterator)\n  }\n}\n\n/** Call every function in the queue with the same arguments. */\nexport const flushCalls = <T extends AnyFn>(\n  queue: Set<T>,\n  ...args: Parameters<T>\n) => flush(queue, fn => fn(...args))\n\n// For server-side rendering: https://github.com/react-spring/zustand/pull/34\n// Deno support: https://github.com/pmndrs/zustand/issues/347\n\nexport const isSSR = () =>\n  typeof window === 'undefined' ||\n  !window.navigator ||\n  /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent)\n", "import { raf } from '@react-spring/rafz'\nimport * as G from './globals'\n\nexport interface OpaqueAnimation {\n  idle: boolean\n  priority: number\n  advance(dt: number): void\n}\n\n// Animations starting on the next frame\nconst startQueue = new Set<OpaqueAnimation>()\n\n// The animations being updated in the current frame, sorted by lowest\n// priority first. These two arrays are swapped at the end of each frame.\nlet currentFrame: OpaqueAnimation[] = []\nlet prevFrame: OpaqueAnimation[] = []\n\n// The priority of the currently advancing animation.\n// To protect against a race condition whenever a frame is being processed,\n// where the filtering of `animations` is corrupted with a shifting index,\n// causing animations to potentially advance 2x faster than intended.\nlet priority = 0\n\n/**\n * The frameloop executes its animations in order of lowest priority first.\n * Animations are retained until idle.\n */\nexport const frameLoop = {\n  get idle() {\n    return !startQueue.size && !currentFrame.length\n  },\n\n  /** Advance the given animation on every frame until idle. */\n  start(animation: OpaqueAnimation) {\n    // An animation can be added while a frame is being processed,\n    // unless its priority is lower than the animation last updated.\n    if (priority > animation.priority) {\n      startQueue.add(animation)\n      raf.onStart(flushStartQueue)\n    } else {\n      startSafely(animation)\n      raf(advance)\n    }\n  },\n\n  /** Advance all animations by the given time. */\n  advance,\n\n  /** Call this when an animation's priority changes. */\n  sort(animation: OpaqueAnimation) {\n    if (priority) {\n      raf.onFrame(() => frameLoop.sort(animation))\n    } else {\n      const prevIndex = currentFrame.indexOf(animation)\n      if (~prevIndex) {\n        currentFrame.splice(prevIndex, 1)\n        startUnsafely(animation)\n      }\n    }\n  },\n\n  /**\n   * Clear all animations. For testing purposes.\n   *\n   * ☠️ Never call this from within the frameloop.\n   */\n  clear() {\n    currentFrame = []\n    startQueue.clear()\n  },\n}\n\nfunction flushStartQueue() {\n  startQueue.forEach(startSafely)\n  startQueue.clear()\n  raf(advance)\n}\n\nfunction startSafely(animation: OpaqueAnimation) {\n  if (!currentFrame.includes(animation)) startUnsafely(animation)\n}\n\nfunction startUnsafely(animation: OpaqueAnimation) {\n  currentFrame.splice(\n    findIndex(currentFrame, other => other.priority > animation.priority),\n    0,\n    animation\n  )\n}\n\nfunction advance(dt: number) {\n  const nextFrame = prevFrame\n\n  for (let i = 0; i < currentFrame.length; i++) {\n    const animation = currentFrame[i]\n    priority = animation.priority\n\n    // Animations may go idle before advancing.\n    if (!animation.idle) {\n      G.willAdvance(animation)\n      animation.advance(dt)\n      if (!animation.idle) {\n        nextFrame.push(animation)\n      }\n    }\n  }\n  priority = 0\n\n  // Reuse the `currentFrame` array to avoid garbage collection.\n  prevFrame = currentFrame\n  prevFrame.length = 0\n\n  // Set `currentFrame` for next frame, so the `start` function\n  // adds new animations to the proper array.\n  currentFrame = nextFrame\n\n  return currentFrame.length > 0\n}\n\n/** Like `Array.prototype.findIndex` but returns `arr.length` instead of `-1` */\nfunction findIndex<T>(arr: T[], test: (value: T) => boolean) {\n  const index = arr.findIndex(test)\n  return index < 0 ? arr.length : index\n}\n", "export const clamp = (min: number, max: number, v: number) =>\n  Math.min(Math.max(v, min), max)\n", "export type ColorName = keyof typeof colors\n\n// http://www.w3.org/TR/css3-color/#svg-color\nexport const colors = {\n  transparent: 0x00000000,\n  aliceblue: 0xf0f8ffff,\n  antiquewhite: 0xfaebd7ff,\n  aqua: 0x00ffffff,\n  aquamarine: 0x7fffd4ff,\n  azure: 0xf0ffffff,\n  beige: 0xf5f5dcff,\n  bisque: 0xffe4c4ff,\n  black: 0x000000ff,\n  blanchedalmond: 0xffebcdff,\n  blue: 0x0000ffff,\n  blueviolet: 0x8a2be2ff,\n  brown: 0xa52a2aff,\n  burlywood: 0xdeb887ff,\n  burntsienna: 0xea7e5dff,\n  cadetblue: 0x5f9ea0ff,\n  chartreuse: 0x7fff00ff,\n  chocolate: 0xd2691eff,\n  coral: 0xff7f50ff,\n  cornflowerblue: 0x6495edff,\n  cornsilk: 0xfff8dcff,\n  crimson: 0xdc143cff,\n  cyan: 0x00ffffff,\n  darkblue: 0x00008bff,\n  darkcyan: 0x008b8bff,\n  darkgoldenrod: 0xb8860bff,\n  darkgray: 0xa9a9a9ff,\n  darkgreen: 0x006400ff,\n  darkgrey: 0xa9a9a9ff,\n  darkkhaki: 0xbdb76bff,\n  darkmagenta: 0x8b008bff,\n  darkolivegreen: 0x556b2fff,\n  darkorange: 0xff8c00ff,\n  darkorchid: 0x9932ccff,\n  darkred: 0x8b0000ff,\n  darksalmon: 0xe9967aff,\n  darkseagreen: 0x8fbc8fff,\n  darkslateblue: 0x483d8bff,\n  darkslategray: 0x2f4f4fff,\n  darkslategrey: 0x2f4f4fff,\n  darkturquoise: 0x00ced1ff,\n  darkviolet: 0x9400d3ff,\n  deeppink: 0xff1493ff,\n  deepskyblue: 0x00bfffff,\n  dimgray: 0x696969ff,\n  dimgrey: 0x696969ff,\n  dodgerblue: 0x1e90ffff,\n  firebrick: 0xb22222ff,\n  floralwhite: 0xfffaf0ff,\n  forestgreen: 0x228b22ff,\n  fuchsia: 0xff00ffff,\n  gainsboro: 0xdcdcdcff,\n  ghostwhite: 0xf8f8ffff,\n  gold: 0xffd700ff,\n  goldenrod: 0xdaa520ff,\n  gray: 0x808080ff,\n  green: 0x008000ff,\n  greenyellow: 0xadff2fff,\n  grey: 0x808080ff,\n  honeydew: 0xf0fff0ff,\n  hotpink: 0xff69b4ff,\n  indianred: 0xcd5c5cff,\n  indigo: 0x4b0082ff,\n  ivory: 0xfffff0ff,\n  khaki: 0xf0e68cff,\n  lavender: 0xe6e6faff,\n  lavenderblush: 0xfff0f5ff,\n  lawngreen: 0x7cfc00ff,\n  lemonchiffon: 0xfffacdff,\n  lightblue: 0xadd8e6ff,\n  lightcoral: 0xf08080ff,\n  lightcyan: 0xe0ffffff,\n  lightgoldenrodyellow: 0xfafad2ff,\n  lightgray: 0xd3d3d3ff,\n  lightgreen: 0x90ee90ff,\n  lightgrey: 0xd3d3d3ff,\n  lightpink: 0xffb6c1ff,\n  lightsalmon: 0xffa07aff,\n  lightseagreen: 0x20b2aaff,\n  lightskyblue: 0x87cefaff,\n  lightslategray: 0x778899ff,\n  lightslategrey: 0x778899ff,\n  lightsteelblue: 0xb0c4deff,\n  lightyellow: 0xffffe0ff,\n  lime: 0x00ff00ff,\n  limegreen: 0x32cd32ff,\n  linen: 0xfaf0e6ff,\n  magenta: 0xff00ffff,\n  maroon: 0x800000ff,\n  mediumaquamarine: 0x66cdaaff,\n  mediumblue: 0x0000cdff,\n  mediumorchid: 0xba55d3ff,\n  mediumpurple: 0x9370dbff,\n  mediumseagreen: 0x3cb371ff,\n  mediumslateblue: 0x7b68eeff,\n  mediumspringgreen: 0x00fa9aff,\n  mediumturquoise: 0x48d1ccff,\n  mediumvioletred: 0xc71585ff,\n  midnightblue: 0x191970ff,\n  mintcream: 0xf5fffaff,\n  mistyrose: 0xffe4e1ff,\n  moccasin: 0xffe4b5ff,\n  navajowhite: 0xffdeadff,\n  navy: 0x000080ff,\n  oldlace: 0xfdf5e6ff,\n  olive: 0x808000ff,\n  olivedrab: 0x6b8e23ff,\n  orange: 0xffa500ff,\n  orangered: 0xff4500ff,\n  orchid: 0xda70d6ff,\n  palegoldenrod: 0xeee8aaff,\n  palegreen: 0x98fb98ff,\n  paleturquoise: 0xafeeeeff,\n  palevioletred: 0xdb7093ff,\n  papayawhip: 0xffefd5ff,\n  peachpuff: 0xffdab9ff,\n  peru: 0xcd853fff,\n  pink: 0xffc0cbff,\n  plum: 0xdda0ddff,\n  powderblue: 0xb0e0e6ff,\n  purple: 0x800080ff,\n  rebeccapurple: 0x663399ff,\n  red: 0xff0000ff,\n  rosybrown: 0xbc8f8fff,\n  royalblue: 0x4169e1ff,\n  saddlebrown: 0x8b4513ff,\n  salmon: 0xfa8072ff,\n  sandybrown: 0xf4a460ff,\n  seagreen: 0x2e8b57ff,\n  seashell: 0xfff5eeff,\n  sienna: 0xa0522dff,\n  silver: 0xc0c0c0ff,\n  skyblue: 0x87ceebff,\n  slateblue: 0x6a5acdff,\n  slategray: 0x708090ff,\n  slategrey: 0x708090ff,\n  snow: 0xfffafaff,\n  springgreen: 0x00ff7fff,\n  steelblue: 0x4682b4ff,\n  tan: 0xd2b48cff,\n  teal: 0x008080ff,\n  thistle: 0xd8bfd8ff,\n  tomato: 0xff6347ff,\n  turquoise: 0x40e0d0ff,\n  violet: 0xee82eeff,\n  wheat: 0xf5deb3ff,\n  white: 0xffffffff,\n  whitesmoke: 0xf5f5f5ff,\n  yellow: 0xffff00ff,\n  yellowgreen: 0x9acd32ff,\n}\n", "// const INTEGER = '[-+]?\\\\d+';\nconst NUMBER = '[-+]?\\\\d*\\\\.?\\\\d+'\nconst PERCENTAGE = NUMBER + '%'\n\nfunction call(...parts: string[]) {\n  return '\\\\(\\\\s*(' + parts.join(')\\\\s*,\\\\s*(') + ')\\\\s*\\\\)'\n}\n\nexport const rgb = new RegExp('rgb' + call(NUMBER, NUMBER, NUMBER))\nexport const rgba = new RegExp('rgba' + call(NUMBER, NUMBER, NUMBER, NUMBER))\nexport const hsl = new RegExp('hsl' + call(NUMBER, PERCENTAGE, PERCENTAGE))\nexport const hsla = new RegExp(\n  'hsla' + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER)\n)\nexport const hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/\nexport const hex4 =\n  /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/\nexport const hex6 = /^#([0-9a-fA-F]{6})$/\nexport const hex8 = /^#([0-9a-fA-F]{8})$/\n", "/*\nhttps://github.com/react-community/normalize-css-color\n\nBSD 3-Clause License\n\nCopyright (c) 2016, React Community\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n* Redistributions of source code must retain the above copyright notice, this\n  list of conditions and the following disclaimer.\n\n* Redistributions in binary form must reproduce the above copyright notice,\n  this list of conditions and the following disclaimer in the documentation\n  and/or other materials provided with the distribution.\n\n* Neither the name of the copyright holder nor the names of its\n  contributors may be used to endorse or promote products derived from\n  this software without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\nDISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\nFOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, <PERSON>XEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL\nDAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\nSERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\nCAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\nOR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n\nimport * as matchers from './colorMatchers'\nimport * as G from './globals'\n\nexport function normalizeColor(color: number | string) {\n  let match\n\n  if (typeof color === 'number') {\n    return color >>> 0 === color && color >= 0 && color <= 0xffffffff\n      ? color\n      : null\n  }\n\n  // Ordered based on occurrences on Facebook codebase\n  if ((match = matchers.hex6.exec(color)))\n    return parseInt(match[1] + 'ff', 16) >>> 0\n\n  if (G.colors && G.colors[color] !== undefined) {\n    return G.colors[color]\n  }\n\n  if ((match = matchers.rgb.exec(color))) {\n    return (\n      ((parse255(match[1]) << 24) | // r\n        (parse255(match[2]) << 16) | // g\n        (parse255(match[3]) << 8) | // b\n        0x000000ff) >>> // a\n      0\n    )\n  }\n\n  if ((match = matchers.rgba.exec(color))) {\n    return (\n      ((parse255(match[1]) << 24) | // r\n        (parse255(match[2]) << 16) | // g\n        (parse255(match[3]) << 8) | // b\n        parse1(match[4])) >>> // a\n      0\n    )\n  }\n\n  if ((match = matchers.hex3.exec(color))) {\n    return (\n      parseInt(\n        match[1] +\n          match[1] + // r\n          match[2] +\n          match[2] + // g\n          match[3] +\n          match[3] + // b\n          'ff', // a\n        16\n      ) >>> 0\n    )\n  }\n\n  // https://drafts.csswg.org/css-color-4/#hex-notation\n  if ((match = matchers.hex8.exec(color))) return parseInt(match[1], 16) >>> 0\n\n  if ((match = matchers.hex4.exec(color))) {\n    return (\n      parseInt(\n        match[1] +\n          match[1] + // r\n          match[2] +\n          match[2] + // g\n          match[3] +\n          match[3] + // b\n          match[4] +\n          match[4], // a\n        16\n      ) >>> 0\n    )\n  }\n\n  if ((match = matchers.hsl.exec(color))) {\n    return (\n      (hslToRgb(\n        parse360(match[1]), // h\n        parsePercentage(match[2]), // s\n        parsePercentage(match[3]) // l\n      ) |\n        0x000000ff) >>> // a\n      0\n    )\n  }\n\n  if ((match = matchers.hsla.exec(color))) {\n    return (\n      (hslToRgb(\n        parse360(match[1]), // h\n        parsePercentage(match[2]), // s\n        parsePercentage(match[3]) // l\n      ) |\n        parse1(match[4])) >>> // a\n      0\n    )\n  }\n  return null\n}\n\nfunction hue2rgb(p: number, q: number, t: number) {\n  if (t < 0) t += 1\n  if (t > 1) t -= 1\n  if (t < 1 / 6) return p + (q - p) * 6 * t\n  if (t < 1 / 2) return q\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6\n  return p\n}\n\nfunction hslToRgb(h: number, s: number, l: number) {\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s\n  const p = 2 * l - q\n  const r = hue2rgb(p, q, h + 1 / 3)\n  const g = hue2rgb(p, q, h)\n  const b = hue2rgb(p, q, h - 1 / 3)\n  return (\n    (Math.round(r * 255) << 24) |\n    (Math.round(g * 255) << 16) |\n    (Math.round(b * 255) << 8)\n  )\n}\n\nfunction parse255(str: string) {\n  const int = parseInt(str, 10)\n  if (int < 0) return 0\n  if (int > 255) return 255\n  return int\n}\n\nfunction parse360(str: string) {\n  const int = parseFloat(str)\n  return (((int % 360) + 360) % 360) / 360\n}\n\nfunction parse1(str: string) {\n  const num = parseFloat(str)\n  if (num < 0) return 0\n  if (num > 1) return 255\n  return Math.round(num * 255)\n}\n\nfunction parsePercentage(str: string) {\n  // parseFloat conveniently ignores the final %\n  const int = parseFloat(str)\n  if (int < 0) return 0\n  if (int > 100) return 1\n  return int / 100\n}\n", "import { normalizeColor } from './normalizeColor'\n\nexport function colorToRgba(input: string) {\n  let int32Color = normalizeColor(input)\n  if (int32Color === null) return input\n  int32Color = int32Color || 0\n  const r = (int32Color & 0xff000000) >>> 24\n  const g = (int32Color & 0x00ff0000) >>> 16\n  const b = (int32Color & 0x0000ff00) >>> 8\n  const a = (int32Color & 0x000000ff) / 255\n  return `rgba(${r}, ${g}, ${b}, ${a})`\n}\n", "import * as G from './globals'\nimport { is } from './helpers'\nimport {\n  Animatable,\n  InterpolatorFn,\n  EasingFunction,\n  ExtrapolateType,\n  InterpolatorConfig,\n  InterpolatorFactory,\n} from '@react-spring/types'\n\nexport const createInterpolator: InterpolatorFactory = (\n  range: readonly number[] | InterpolatorFn<any, any> | InterpolatorConfig<any>,\n  output?: readonly Animatable[],\n  extrapolate?: ExtrapolateType\n) => {\n  if (is.fun(range)) {\n    return range\n  }\n\n  if (is.arr(range)) {\n    return createInterpolator({\n      range,\n      output: output!,\n      extrapolate,\n    })\n  }\n\n  if (is.str(range.output[0])) {\n    return G.createStringInterpolator(range as any) as any\n  }\n\n  const config = range as InterpolatorConfig<number>\n  const outputRange = config.output\n  const inputRange = config.range || [0, 1]\n\n  const extrapolateLeft =\n    config.extrapolateLeft || config.extrapolate || 'extend'\n  const extrapolateRight =\n    config.extrapolateRight || config.extrapolate || 'extend'\n  const easing = config.easing || (t => t)\n\n  return (input: number) => {\n    const range = findRange(input, inputRange)\n    return interpolate(\n      input,\n      inputRange[range],\n      inputRange[range + 1],\n      outputRange[range],\n      outputRange[range + 1],\n      easing,\n      extrapolateLeft,\n      extrapolateRight,\n      config.map\n    )\n  }\n}\n\nfunction interpolate(\n  input: number,\n  inputMin: number,\n  inputMax: number,\n  outputMin: number,\n  outputMax: number,\n  easing: EasingFunction,\n  extrapolateLeft: ExtrapolateType,\n  extrapolateRight: ExtrapolateType,\n  map?: (x: number) => number\n) {\n  let result = map ? map(input) : input\n  // Extrapolate\n  if (result < inputMin) {\n    if (extrapolateLeft === 'identity') return result\n    else if (extrapolateLeft === 'clamp') result = inputMin\n  }\n  if (result > inputMax) {\n    if (extrapolateRight === 'identity') return result\n    else if (extrapolateRight === 'clamp') result = inputMax\n  }\n  if (outputMin === outputMax) return outputMin\n  if (inputMin === inputMax) return input <= inputMin ? outputMin : outputMax\n  // Input Range\n  if (inputMin === -Infinity) result = -result\n  else if (inputMax === Infinity) result = result - inputMin\n  else result = (result - inputMin) / (inputMax - inputMin)\n  // Easing\n  result = easing(result)\n  // Output Range\n  if (outputMin === -Infinity) result = -result\n  else if (outputMax === Infinity) result = result + outputMin\n  else result = result * (outputMax - outputMin) + outputMin\n  return result\n}\n\nfunction findRange(input: number, inputRange: readonly number[]) {\n  // eslint-disable-next-line no-var\n  for (var i = 1; i < inputRange.length - 1; ++i)\n    if (inputRange[i] >= input) break\n  return i - 1\n}\n", "import { EasingFunction } from '@react-spring/types'\n\nimport { clamp } from './clamp'\n\nexport type Direction = 'start' | 'end'\n\ntype StepsEasing = (steps: number, direction?: Direction) => EasingFunction\n\nconst steps: StepsEasing =\n  (steps: number, direction: Direction = 'end') =>\n  (progress: number) => {\n    progress =\n      direction === 'end'\n        ? Math.min(progress, 0.999)\n        : Math.max(progress, 0.001)\n    const expanded = progress * steps\n    const rounded =\n      direction === 'end' ? Math.floor(expanded) : Math.ceil(expanded)\n\n    return clamp(0, 1, rounded / steps)\n  }\n\n/**\n * With thanks to ai easings.net\n * https://github.com/ai/easings.net/blob/master/src/easings/easingsFunctions.ts\n */\ninterface EasingDictionary {\n  linear: (t: number) => number\n  easeInQuad: (t: number) => number\n  easeOutQuad: (t: number) => number\n  easeInOutQuad: (t: number) => number\n  easeInCubic: (t: number) => number\n  easeOutCubic: (t: number) => number\n  easeInOutCubic: (t: number) => number\n  easeInQuart: (t: number) => number\n  easeOutQuart: (t: number) => number\n  easeInOutQuart: (t: number) => number\n  easeInQuint: (t: number) => number\n  easeOutQuint: (t: number) => number\n  easeInOutQuint: (t: number) => number\n  easeInSine: (t: number) => number\n  easeOutSine: (t: number) => number\n  easeInOutSine: (t: number) => number\n  easeInExpo: (t: number) => number\n  easeOutExpo: (t: number) => number\n  easeInOutExpo: (t: number) => number\n  easeInCirc: (t: number) => number\n  easeOutCirc: (t: number) => number\n  easeInOutCirc: (t: number) => number\n  easeInBack: (t: number) => number\n  easeOutBack: (t: number) => number\n  easeInOutBack: (t: number) => number\n  easeInElastic: (t: number) => number\n  easeOutElastic: (t: number) => number\n  easeInOutElastic: (t: number) => number\n  easeInBounce: (t: number) => number\n  easeOutBounce: (t: number) => number\n  easeInOutBounce: (t: number) => number\n  steps: StepsEasing\n}\n\nconst c1 = 1.70158\nconst c2 = c1 * 1.525\nconst c3 = c1 + 1\nconst c4 = (2 * Math.PI) / 3\nconst c5 = (2 * Math.PI) / 4.5\n\nconst bounceOut: EasingFunction = x => {\n  const n1 = 7.5625\n  const d1 = 2.75\n\n  if (x < 1 / d1) {\n    return n1 * x * x\n  } else if (x < 2 / d1) {\n    return n1 * (x -= 1.5 / d1) * x + 0.75\n  } else if (x < 2.5 / d1) {\n    return n1 * (x -= 2.25 / d1) * x + 0.9375\n  } else {\n    return n1 * (x -= 2.625 / d1) * x + 0.984375\n  }\n}\n\nexport const easings: EasingDictionary = {\n  linear: x => x,\n  easeInQuad: x => x * x,\n  easeOutQuad: x => 1 - (1 - x) * (1 - x),\n  easeInOutQuad: x => (x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2),\n  easeInCubic: x => x * x * x,\n  easeOutCubic: x => 1 - Math.pow(1 - x, 3),\n  easeInOutCubic: x =>\n    x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,\n  easeInQuart: x => x * x * x * x,\n  easeOutQuart: x => 1 - Math.pow(1 - x, 4),\n  easeInOutQuart: x =>\n    x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,\n  easeInQuint: x => x * x * x * x * x,\n  easeOutQuint: x => 1 - Math.pow(1 - x, 5),\n  easeInOutQuint: x =>\n    x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,\n  easeInSine: x => 1 - Math.cos((x * Math.PI) / 2),\n  easeOutSine: x => Math.sin((x * Math.PI) / 2),\n  easeInOutSine: x => -(Math.cos(Math.PI * x) - 1) / 2,\n  easeInExpo: x => (x === 0 ? 0 : Math.pow(2, 10 * x - 10)),\n  easeOutExpo: x => (x === 1 ? 1 : 1 - Math.pow(2, -10 * x)),\n  easeInOutExpo: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : x < 0.5\n          ? Math.pow(2, 20 * x - 10) / 2\n          : (2 - Math.pow(2, -20 * x + 10)) / 2,\n  easeInCirc: x => 1 - Math.sqrt(1 - Math.pow(x, 2)),\n  easeOutCirc: x => Math.sqrt(1 - Math.pow(x - 1, 2)),\n  easeInOutCirc: x =>\n    x < 0.5\n      ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2\n      : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,\n  easeInBack: x => c3 * x * x * x - c1 * x * x,\n  easeOutBack: x => 1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),\n  easeInOutBack: x =>\n    x < 0.5\n      ? (Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2)) / 2\n      : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,\n  easeInElastic: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),\n  easeOutElastic: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,\n  easeInOutElastic: x =>\n    x === 0\n      ? 0\n      : x === 1\n        ? 1\n        : x < 0.5\n          ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2\n          : (Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5)) / 2 +\n            1,\n  easeInBounce: x => 1 - bounceOut(1 - x),\n  easeOutBounce: bounceOut,\n  easeInOutBounce: x =>\n    x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,\n  steps,\n} as const\n", "/**\n * MIT License\n * Copyright (c) <PERSON>\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\nconst $get = Symbol.for('FluidValue.get')\nconst $observers = Symbol.for('FluidValue.observers')\n\nexport {\n  FluidValue,\n  hasFluidValue,\n  getFluidValue,\n  getFluidObservers,\n  callFluidObserver,\n  callFluidObservers,\n  // Mutations\n  setFluidGetter,\n  addFluidObserver,\n  removeFluidObserver,\n}\n\n/** Returns true if `arg` can be observed. */\nconst hasFluidValue = (arg: any): arg is FluidValue => Boolean(arg && arg[$get])\n\n/**\n * Get the current value.\n * If `arg` is not observable, `arg` is returned.\n */\nconst getFluidValue: GetFluidValue = (arg: any) =>\n  arg && arg[$get] ? arg[$get]() : arg\n\n/** Get the current observer set. Never mutate it directly! */\nconst getFluidObservers: GetFluidObservers = (target: any) =>\n  target[$observers] || null\n\n/** Send an event to an observer. */\nfunction callFluidObserver<E extends FluidEvent>(\n  observer: FluidObserver<E>,\n  event: E\n): void\n\nfunction callFluidObserver(observer: any, event: FluidEvent) {\n  if (observer.eventObserved) {\n    observer.eventObserved(event)\n  } else {\n    observer(event)\n  }\n}\n\n/** Send an event to all observers. */\nfunction callFluidObservers<E extends FluidEvent>(\n  target: FluidValue<any, E>,\n  event: E\n): void\n\nfunction callFluidObservers(target: object, event: FluidEvent): void\n\nfunction callFluidObservers(target: any, event: FluidEvent) {\n  const observers: Set<FluidObserver> = target[$observers]\n  if (observers) {\n    observers.forEach(observer => {\n      callFluidObserver(observer, event)\n    })\n  }\n}\n\ntype GetFluidValue = {\n  <T, U = never>(target: T | FluidValue<U>): Exclude<T, FluidValue> | U\n}\n\ntype GetFluidObservers = {\n  <E extends FluidEvent>(\n    target: FluidValue<any, E>\n  ): ReadonlySet<FluidObserver<E>> | null\n  (target: object): ReadonlySet<FluidObserver> | null\n}\n\n/** An event sent to `FluidObserver` objects. */\nexport interface FluidEvent<T = any> {\n  type: string\n  parent: FluidValue<T>\n}\n\n/**\n * Extend this class for automatic TypeScript support when passing this\n * value to `fluids`-compatible libraries.\n */\nabstract class FluidValue<T = any, E extends FluidEvent<T> = any> {\n  // @ts-expect-error (TS 4.4)\n  private [$get]: () => T\n  // @ts-expect-error (TS 4.4)\n  private [$observers]?: Set<FluidObserver<E>>\n\n  constructor(get?: () => T) {\n    if (!get && !(get = this.get)) {\n      throw Error('Unknown getter')\n    }\n    setFluidGetter(this, get)\n  }\n\n  /** Get the current value. */\n  protected get?(): T\n  /** Called after an observer is added. */\n  protected observerAdded?(count: number, observer: FluidObserver<E>): void\n  /** Called after an observer is removed. */\n  protected observerRemoved?(count: number, observer: FluidObserver<E>): void\n}\n\n/** An observer of `FluidValue` objects. */\nexport type FluidObserver<E extends FluidEvent = any> =\n  | { eventObserved(event: E): void }\n  | { (event: E): void }\n\n/** Add the `FluidValue` type to every property. */\nexport type FluidProps<T> = T extends object\n  ? { [P in keyof T]: T[P] | FluidValue<Exclude<T[P], void>> }\n  : unknown\n\n/** Remove the `FluidValue` type from every property. */\nexport type StaticProps<T extends object> = {\n  [P in keyof T]: T[P] extends FluidValue<infer U> ? U : T[P]\n}\n\n/** Define the getter called by `getFluidValue`. */\nconst setFluidGetter = (target: object, get: () => any) =>\n  setHidden(target, $get, get)\n\n/** Observe a `fluids`-compatible object. */\nfunction addFluidObserver<T, E extends FluidEvent>(\n  target: FluidValue<T, E>,\n  observer: FluidObserver<E>\n): typeof observer\n\nfunction addFluidObserver<E extends FluidEvent>(\n  target: object,\n  observer: FluidObserver<E>\n): typeof observer\n\nfunction addFluidObserver(target: any, observer: FluidObserver) {\n  if (target[$get]) {\n    let observers: Set<FluidObserver> = target[$observers]\n    if (!observers) {\n      setHidden(target, $observers, (observers = new Set()))\n    }\n    if (!observers.has(observer)) {\n      observers.add(observer)\n      if (target.observerAdded) {\n        target.observerAdded(observers.size, observer)\n      }\n    }\n  }\n  return observer\n}\n\n/** Stop observing a `fluids`-compatible object. */\nfunction removeFluidObserver<E extends FluidEvent>(\n  target: FluidValue<any, E>,\n  observer: FluidObserver<E>\n): void\n\nfunction removeFluidObserver<E extends FluidEvent>(\n  target: object,\n  observer: FluidObserver<E>\n): void\n\nfunction removeFluidObserver(target: any, observer: FluidObserver) {\n  const observers: Set<FluidObserver> = target[$observers]\n  if (observers && observers.has(observer)) {\n    const count = observers.size - 1\n    if (count) {\n      observers.delete(observer)\n    } else {\n      target[$observers] = null\n    }\n    if (target.observerRemoved) {\n      target.observerRemoved(count, observer)\n    }\n  }\n}\n\nconst setHidden = (target: any, key: any, value: any) =>\n  Object.defineProperty(target, key, {\n    value,\n    writable: true,\n    configurable: true,\n  })\n", "// Problem: https://github.com/animatedjs/animated/pull/102\n// Solution: https://stackoverflow.com/questions/638565/parsing-scientific-notation-sensibly/658662\nexport const numberRegex = /[+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g\n\n// Covers rgb, rgba, hsl, hsla\n// Taken from https://gist.github.com/olmokramer/82ccce673f86db7cda5e\nexport const colorRegex =\n  /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d\\.]+%?\\))/gi\n\n// Gets numbers with units when specified\nexport const unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, 'i')\n\n// get values of rgba string\nexport const rgbaRegex =\n  /rgba\\(([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+)\\)/gi\n\n/**\n * Parse special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n */\nexport const cssVariableRegex =\n  /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/\n", "import { isSSR } from './helpers'\nimport { cssVariableRegex } from './regexs'\n\n/**\n * takes a CSS variable and attempts\n * to turn it into a RGBA value\n *\n * ```\n * 'var(--white)' => 'rgba(255,255,255,1)'\n * ```\n *\n * @param input string\n * @returns string\n */\nexport const variableToRgba = (input: string): string => {\n  const [token, fallback] = parseCSSVariable(input)\n\n  if (!token || isSSR()) {\n    return input\n  }\n\n  const value = window\n    .getComputedStyle(document.documentElement)\n    .getPropertyValue(token)\n\n  if (value) {\n    /**\n     * We have a valid variable returned\n     * trim and return\n     */\n    return value.trim()\n  } else if (fallback && fallback.startsWith('--')) {\n    /**\n     * fallback is something like --my-variable\n     * so we try get property value\n     */\n    const value = window\n      .getComputedStyle(document.documentElement)\n      .getPropertyValue(fallback)\n\n    /**\n     * if it exists, return else nothing was found so just return the input\n     */\n    if (value) {\n      return value\n    } else {\n      return input\n    }\n  } else if (fallback && cssVariableRegex.test(fallback)) {\n    /**\n     * We have a fallback and it's another CSS variable\n     */\n    return variableToRgba(fallback)\n  } else if (fallback) {\n    /**\n     * We have a fallback and it's not a CSS variable\n     */\n    return fallback\n  }\n\n  /**\n   * Nothing worked so just return the input\n   * like our other FluidValue replace functions do\n   */\n  return input\n}\n\nconst parseCSSVariable = (current: string) => {\n  const match = cssVariableRegex.exec(current)\n  if (!match) return [,]\n\n  const [, token, fallback] = match\n  return [token, fallback]\n}\n", "import { InterpolatorConfig } from '@react-spring/types'\n\nimport { getFluidValue } from './fluids'\nimport { createInterpolator } from './createInterpolator'\nimport { colorToRgba } from './colorToRgba'\nimport * as G from './globals'\nimport {\n  cssVariableRegex,\n  colorRegex,\n  unitRegex,\n  numberRegex,\n  rgbaRegex,\n} from './regexs'\nimport { variableToRgba } from './variableToRgba'\n\n// Covers color names (transparent, blue, etc.)\nlet namedColorRegex: RegExp\n\n// rgba requires that the r,g,b are integers.... so we want to round them,\n// but we *dont* want to round the opacity (4th column).\nconst rgbaRound = (_: any, p1: number, p2: number, p3: number, p4: number) =>\n  `rgba(${Math.round(p1)}, ${Math.round(p2)}, ${Math.round(p3)}, ${p4})`\n\n/**\n * Supports string shapes by extracting numbers so new values can be computed,\n * and recombines those values into new strings of the same shape.  Supports\n * things like:\n *\n *     \"rgba(123, 42, 99, 0.36)\"           // colors\n *     \"-45deg\"                            // values with units\n *     \"0 2px 2px 0px rgba(0, 0, 0, 0.12)\" // CSS box-shadows\n *     \"rotate(0deg) translate(2px, 3px)\"  // CSS transforms\n */\nexport const createStringInterpolator = (\n  config: InterpolatorConfig<string>\n) => {\n  if (!namedColorRegex)\n    namedColorRegex = G.colors\n      ? // match color names, ignore partial matches\n        new RegExp(`(${Object.keys(G.colors).join('|')})(?!\\\\w)`, 'g')\n      : // never match\n        /^\\b$/\n\n  // Convert colors to rgba(...)\n  const output = config.output.map(value => {\n    return getFluidValue(value)\n      .replace(cssVariableRegex, variableToRgba)\n      .replace(colorRegex, colorToRgba)\n      .replace(namedColorRegex, colorToRgba)\n  })\n\n  // Convert [\"1px 2px\", \"0px 0px\"] into [[1, 2], [0, 0]]\n  const keyframes = output.map(value => value.match(numberRegex)!.map(Number))\n\n  // Convert [\"1px 2px\", \"0px 0px\"] into [[1, 0], [2, 0]]\n  const outputRanges = keyframes[0].map((_, i) =>\n    keyframes.map(values => {\n      if (!(i in values)) {\n        throw Error('The arity of each \"output\" value must be equal')\n      }\n      return values[i]\n    })\n  )\n\n  // Create an interpolator for each animated number\n  const interpolators = outputRanges.map(output =>\n    createInterpolator({ ...config, output })\n  )\n\n  // Use the first `output` as a template for each call\n  return (input: number) => {\n    // Convert numbers to units if available (allows for [\"0\", \"100%\"])\n    const missingUnit =\n      !unitRegex.test(output[0]) &&\n      output.find(value => unitRegex.test(value))?.replace(numberRegex, '')\n\n    let i = 0\n    return output[0]\n      .replace(\n        numberRegex,\n        () => `${interpolators[i++](input)}${missingUnit || ''}`\n      )\n      .replace(rgbaRegex, rgbaRound)\n  }\n}\n", "declare const console: any\n\nexport const prefix = 'react-spring: '\n\nexport const once = <TFunc extends (...args: any) => any>(fn: TFunc) => {\n  const func = fn\n  let called = false\n\n  if (typeof func != 'function') {\n    throw new TypeError(`${prefix}once requires a function parameter`)\n  }\n\n  return (...args: any) => {\n    if (!called) {\n      func(...args)\n      called = true\n    }\n  }\n}\n\nconst warnInterpolate = once(console.warn)\nexport function deprecateInterpolate() {\n  warnInterpolate(\n    `${prefix}The \"interpolate\" function is deprecated in v9 (use \"to\" instead)`\n  )\n}\n\nconst warnDirectCall = once(console.warn)\nexport function deprecateDirectCall() {\n  warnDirectCall(\n    `${prefix}Directly calling start instead of using the api object is deprecated in v9 (use \".start\" instead), this will be removed in later 0.X.0 versions`\n  )\n}\n", "import * as G from './globals'\nimport { is, isSSR } from './helpers'\nimport { cssVariableRegex } from './regexs'\n\n// Not all strings can be animated (eg: {display: \"none\"})\nexport function isAnimatedString(value: unknown): value is string {\n  return (\n    is.str(value) &&\n    (value[0] == '#' ||\n      /\\d/.test(value) ||\n      // Do not identify a CSS variable as an AnimatedString if its SSR\n      (!isSSR() && cssVariableRegex.test(value)) ||\n      value in (G.colors || {}))\n  )\n}\n", "import { raf } from '@react-spring/rafz'\nimport { onResize } from '../resize'\n\nimport { ScrollInfo, ScrollHandler } from './ScrollHandler'\n\nexport type OnScrollCallback = (info: ScrollInfo) => void\n\nexport type OnScrollOptions = {\n  /**\n   * The root container to measure against\n   */\n  container?: HTMLElement\n}\n\nconst scrollListeners = new WeakMap<Element, () => boolean>()\nconst resizeListeners = new WeakMap<Element, VoidFunction>()\nconst onScrollHandlers = new WeakMap<Element, Set<ScrollHandler>>()\n\nconst getTarget = (container: HTMLElement) =>\n  container === document.documentElement ? window : container\n\nexport const onScroll = (\n  callback: OnScrollCallback,\n  { container = document.documentElement }: OnScrollOptions = {}\n) => {\n  /**\n   * get the current handlers for the target\n   */\n  let containerHandlers = onScrollHandlers.get(container)\n\n  /**\n   * If there aren't any handlers then create a new set.\n   */\n  if (!containerHandlers) {\n    containerHandlers = new Set()\n    onScrollHandlers.set(container, containerHandlers)\n  }\n\n  /**\n   * Create a new ScrollHandler class and add it to the set.\n   */\n  const containerHandler = new ScrollHandler(callback, container)\n  containerHandlers.add(containerHandler)\n\n  /**\n   * If there are no scrollListeners then we need to make them\n   */\n  if (!scrollListeners.has(container)) {\n    /**\n     * Return true so RAFZ continues to run it\n     */\n    const listener = () => {\n      containerHandlers?.forEach(handler => handler.advance())\n      return true\n    }\n\n    scrollListeners.set(container, listener)\n\n    const target = getTarget(container)\n\n    /**\n     * Add resize handlers so we can correctly calculate the\n     * scroll position on changes\n     */\n    window.addEventListener('resize', listener, { passive: true })\n\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, onResize(listener, { container }))\n    }\n\n    /**\n     * Add the actual scroll listener\n     */\n    target.addEventListener('scroll', listener, { passive: true })\n  }\n\n  /**\n   * Start animation loop\n   */\n  const animateScroll = scrollListeners.get(container)!\n  raf(animateScroll)\n\n  return () => {\n    /**\n     * Clear it on cleanup\n     */\n    raf.cancel(animateScroll)\n\n    /**\n     * Check if we even have any handlers for this container.\n     */\n    const containerHandlers = onScrollHandlers.get(container)\n    if (!containerHandlers) return\n\n    containerHandlers.delete(containerHandler)\n\n    if (containerHandlers.size) return\n\n    /**\n     * If no more handlers, remove the scroll listener too.\n     */\n    const listener = scrollListeners.get(container)\n    scrollListeners.delete(container)\n\n    if (listener) {\n      getTarget(container).removeEventListener('scroll', listener)\n      window.removeEventListener('resize', listener)\n\n      resizeListeners.get(container)?.()\n    }\n  }\n}\n", "import type { OnResizeCallback } from '.'\n\nlet observer: ResizeObserver | undefined\n\nconst resizeHandlers = new WeakMap<Element, Set<OnResizeCallback>>()\n\nconst handleObservation = (entries: ResizeObserverEntry[]) =>\n  entries.forEach(({ target, contentRect }) => {\n    return resizeHandlers.get(target)?.forEach(handler => handler(contentRect))\n  })\n\nexport function resizeElement(handler: OnResizeCallback, target: HTMLElement) {\n  /**\n   * If there's a resize observer in the ENV then use that too.\n   */\n  if (!observer) {\n    if (typeof ResizeObserver !== 'undefined') {\n      observer = new ResizeObserver(handleObservation)\n    }\n  }\n\n  /**\n   * Fetch the handlers for the target\n   */\n  let elementHandlers = resizeHandlers.get(target)\n\n  /**\n   * If there are no handlers create a new set for the target\n   * and then add to the map\n   */\n  if (!elementHandlers) {\n    elementHandlers = new Set()\n    resizeHandlers.set(target, elementHandlers)\n  }\n\n  /**\n   * Add the handler to the target's set\n   * and observe the target if possible.\n   */\n  elementHandlers.add(handler)\n\n  if (observer) {\n    observer.observe(target)\n  }\n\n  /**\n   * Cleanup the event handlers and potential observers.\n   */\n  return () => {\n    const elementHandlers = resizeHandlers.get(target)\n\n    if (!elementHandlers) return\n\n    elementHandlers.delete(handler)\n\n    if (!elementHandlers.size && observer) {\n      observer.unobserve(target)\n    }\n  }\n}\n", "import type { OnResizeCallback } from '.'\n\nconst listeners = new Set<OnResizeCallback>()\n\nlet cleanupWindowResizeHandler: VoidFunction | undefined\n\nconst createResizeHandler = () => {\n  const handleResize = () => {\n    listeners.forEach(callback =>\n      callback({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      })\n    )\n  }\n\n  window.addEventListener('resize', handleResize)\n\n  return () => {\n    window.removeEventListener('resize', handleResize)\n  }\n}\n\nexport const resizeWindow = (callback: OnResizeCallback) => {\n  listeners.add(callback)\n\n  if (!cleanupWindowResizeHandler) {\n    cleanupWindowResizeHandler = createResizeHandler()\n  }\n\n  return () => {\n    listeners.delete(callback)\n\n    if (!listeners.size && cleanupWindowResizeHandler) {\n      cleanupWindowResizeHandler()\n      cleanupWindowResizeHandler = undefined\n    }\n  }\n}\n", "import { resizeElement } from './resizeElement'\nimport { resizeWindow } from './resizeWindow'\n\nexport interface OnResizeOptions {\n  container?: HTMLElement\n}\n\nexport type OnResizeCallback = (\n  rect: Pick<DOMRectReadOnly, 'width' | 'height'> &\n    Partial<Omit<DOMRectReadOnly, 'width' | 'height'>>\n) => void\n\nexport const onResize = (\n  callback: OnResizeCallback,\n  { container = document.documentElement }: OnResizeOptions = {}\n): (() => void) => {\n  if (container === document.documentElement) {\n    return resizeWindow(callback)\n  } else {\n    return resizeElement(callback, container)\n  }\n}\n", "export const progress = (min: number, max: number, value: number) =>\n  max - min === 0 ? 1 : (value - min) / (max - min)\n", "import { progress } from '../../progress'\n\nimport type { OnScrollCallback } from './index'\n\nconst SCROLL_KEYS = {\n  x: {\n    length: 'Width',\n    position: 'Left',\n  },\n  y: {\n    length: 'Height',\n    position: 'Top',\n  },\n} as const\n\n/**\n * Whilst user's may not need the scrollLength, it's easier to return\n * the whole state we're storing and let them pick what they want.\n */\nexport interface ScrollAxis {\n  current: number\n  progress: number\n  scrollLength: number\n}\n\nexport interface ScrollInfo {\n  time: number\n  x: ScrollAxis\n  y: ScrollAxis\n}\n\n/**\n * Why use a class? More extensible in the future.\n */\nexport class ScrollHandler {\n  protected callback: OnScrollCallback\n  protected container: HTMLElement\n  protected info: ScrollInfo\n\n  constructor(callback: OnScrollCallback, container: HTMLElement) {\n    this.callback = callback\n    this.container = container\n\n    this.info = {\n      time: 0,\n      x: this.createAxis(),\n      y: this.createAxis(),\n    }\n  }\n\n  private createAxis = (): ScrollAxis => ({\n    current: 0,\n    progress: 0,\n    scrollLength: 0,\n  })\n\n  private updateAxis = (axisName: keyof Pick<ScrollInfo, 'x' | 'y'>) => {\n    const axis = this.info[axisName]\n    const { length, position } = SCROLL_KEYS[axisName]\n\n    axis.current = this.container[`scroll${position}`]\n    axis.scrollLength =\n      this.container[`scroll${length}`] - this.container[`client${length}`]\n\n    axis.progress = progress(0, axis.scrollLength, axis.current)\n  }\n\n  private update = () => {\n    this.updateAxis('x')\n    this.updateAxis('y')\n  }\n\n  private sendEvent = () => {\n    this.callback(this.info)\n  }\n\n  advance = () => {\n    this.update()\n    this.sendEvent()\n  }\n}\n", "import { useRef } from 'react'\n\ntype Init<T> = () => T\n\n/**\n * Creates a constant value over the lifecycle of a component.\n */\nexport function useConstant<T>(init: Init<T>) {\n  const ref = useRef<T | null>(null)\n\n  if (ref.current === null) {\n    ref.current = init()\n  }\n\n  return ref.current\n}\n", "import { useState } from 'react'\nimport { useIsMounted } from './useIsMounted'\n\n/** Return a function that re-renders this component, if still mounted */\nexport function useForceUpdate() {\n  const update = useState<any>()[1]\n  const isMounted = useIsMounted()\n  return () => {\n    if (isMounted.current) {\n      update(Math.random())\n    }\n  }\n}\n", "import { useRef } from 'react'\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect'\n\nexport const useIsMounted = () => {\n  const isMounted = useRef(false)\n  useIsomorphicLayoutEffect(() => {\n    isMounted.current = true\n\n    return () => {\n      isMounted.current = false\n    }\n  }, [])\n\n  return isMounted\n}\n", "import { useEffect, useLayoutEffect } from 'react'\n\nimport { isSSR } from '../helpers'\n\n/**\n * Use this to read layout from the DOM and synchronously\n * re-render if the isSSR returns true. Updates scheduled\n * inside `useIsomorphicLayoutEffect` will be flushed\n * synchronously in the browser, before the browser has\n * a chance to paint.\n */\nexport const useIsomorphicLayoutEffect = isSSR() ? useEffect : useLayoutEffect\n", "import { useEffect, useRef, useState } from 'react'\n\ntype Cache<T> = {\n  inputs?: any[]\n  result?: T\n}\n\n// TODO: remove once merged (https://github.com/alexreardon/use-memo-one/pull/10)\nexport function useMemoOne<T>(getResult: () => T, inputs?: any[]): T {\n  const [initial] = useState(\n    (): Cache<T> => ({\n      inputs,\n      result: getResult(),\n    })\n  )\n\n  const committed = useRef<Cache<T>>()\n  const prevCache = committed.current\n\n  let cache = prevCache\n  if (cache) {\n    const useCache = Boolean(\n      inputs && cache.inputs && areInputsEqual(inputs, cache.inputs)\n    )\n    if (!useCache) {\n      cache = {\n        inputs,\n        result: getResult(),\n      }\n    }\n  } else {\n    cache = initial\n  }\n\n  useEffect(() => {\n    committed.current = cache\n    if (prevCache == initial) {\n      initial.inputs = initial.result = undefined\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [cache])\n\n  return cache.result!\n}\n\nfunction areInputsEqual(next: any[], prev: any[]) {\n  if (next.length !== prev.length) {\n    return false\n  }\n  for (let i = 0; i < next.length; i++) {\n    if (next[i] !== prev[i]) {\n      return false\n    }\n  }\n  return true\n}\n", "/* eslint-disable react-hooks/exhaustive-deps */\nimport { useEffect, EffectCallback } from 'react'\n\nexport const useOnce = (effect: EffectCallback) => useEffect(effect, emptyDeps)\n\nconst emptyDeps: any[] = []\n", "import { useEffect, useRef } from 'react'\n\n/** Use a value from the previous render */\nexport function usePrev<T>(value: T): T | undefined {\n  const prevRef = useRef<any>()\n  useEffect(() => {\n    prevRef.current = value\n  })\n  return prevRef.current\n}\n", "import { useState } from 'react'\n\nimport { assign } from '../globals'\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect'\n\n/**\n * Returns `boolean` or `null`, used to automatically\n * set skipAnimations to the value of the user's\n * `prefers-reduced-motion` query.\n *\n * The return value, post-effect, is the value of their prefered setting\n */\nexport const useReducedMotion = () => {\n  const [reducedMotion, setReducedMotion] = useState<boolean | null>(null)\n\n  useIsomorphicLayoutEffect(() => {\n    const mql = window.matchMedia('(prefers-reduced-motion)')\n\n    const handleMediaChange = (e: MediaQueryListEvent | MediaQueryList) => {\n      setReducedMotion(e.matches)\n\n      assign({\n        skipAnimation: e.matches,\n      })\n    }\n\n    handleMediaChange(mql)\n\n    if (mql.addEventListener) {\n      mql.addEventListener('change', handleMediaChange)\n    } else {\n      mql.addListener(handleMediaChange)\n    }\n\n    return () => {\n      if (mql.removeEventListener) {\n        mql.removeEventListener('change', handleMediaChange)\n      } else {\n        mql.removeListener(handleMediaChange)\n      }\n    }\n  }, [])\n\n  return reducedMotion\n}\n", "import * as Globals from './globals'\nexport { Globals }\n\nexport * from './FrameLoop'\nexport * from './clamp'\nexport * from './colors'\nexport * from './colorToRgba'\nexport * from './colorMatchers'\nexport * from './createInterpolator'\nexport * from './easings'\nexport * from './stringInterpolation'\nexport * from './deprecations'\nexport * from './helpers'\nexport * from './isAnimatedString'\n/**\n * Should these be moved to a DOM only\n * package to avoid native issues?\n */\nexport * from './dom-events/scroll'\nexport * from './dom-events/resize'\n\nexport * from './hooks/useConstant'\nexport * from './hooks/useForceUpdate'\nexport * from './hooks/useMemoOne'\nexport * from './hooks/useOnce'\nexport * from './hooks/usePrev'\nexport * from './hooks/useIsomorphicLayoutEffect'\nexport * from './hooks/useReducedMotion'\n\nexport * from './fluids'\n\nexport { raf } from '@react-spring/rafz'\nexport type { Timeout } from '@react-spring/rafz'\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAS,WAAiB;;;ACGnB,SAAS,OAAO;AAAC;AAEjB,IAAM,eAAe,CAAC,KAAU,KAAU,UAC/C,OAAO,eAAe,KAAK,KAAK,EAAE,OAAO,UAAU,MAAM,cAAc,KAAK,CAAC;AAOxE,IAAM,KAAK;AAAA,EAChB,KAAK,MAAM;AAAA,EACX,KAAK,CAAI,MACP,CAAC,CAAC,KAAK,EAAE,YAAY,SAAS;AAAA,EAChC,KAAM,CAAC,MAAe,OAAO,MAAM;AAAA,EACnC,KAAK,CAAC,MAA4B,OAAO,MAAM;AAAA,EAC/C,KAAK,CAAC,MAA4B,OAAO,MAAM;AAAA,EAC/C,KAAK,CAAC,MAA+B,MAAM;AAC7C;AAGO,SAAS,QAAQ,GAAQ,GAAQ;AACtC,MAAI,GAAG,IAAI,CAAC,GAAG;AACb,QAAI,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE;AAAQ,aAAO;AAChD,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAI,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG,eAAO;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACA,SAAO,MAAM;AACf;AAQO,IAAM,OAAO,CAClB,KACA,OACG,IAAI,QAAQ,EAAE;AAGZ,SAAS,SACd,KACA,IAKA,KACA;AACA,MAAI,GAAG,IAAI,GAAG,GAAG;AACf,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,SAAG,KAAK,KAAY,IAAI,CAAC,GAAU,GAAG,GAAG;AAAA,IAC3C;AACA;AAAA,EACF;AACA,aAAW,OAAO,KAAK;AACrB,QAAI,IAAI,eAAe,GAAG,GAAG;AAC3B,SAAG,KAAK,KAAY,IAAI,GAAG,GAAU,GAAG;AAAA,IAC1C;AAAA,EACF;AACF;AAEO,IAAM,UAAU,CAAI,MACzB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAK,IAAY,CAAC,CAAC;AAQvC,SAAS,MAAM,OAAY,UAAe;AAC/C,MAAI,MAAM,MAAM;AACd,UAAM,QAAQ,MAAM,KAAK,KAAK;AAC9B,UAAM,MAAM;AACZ,SAAK,OAAO,QAAQ;AAAA,EACtB;AACF;AAGO,IAAM,aAAa,CACxB,UACG,SACA,MAAM,OAAO,QAAM,GAAG,GAAG,IAAI,CAAC;AAK5B,IAAM,QAAQ,MACnB,OAAO,WAAW,eAClB,CAAC,OAAO,aACR,8BAA8B,KAAK,OAAO,UAAU,SAAS;;;ADnFxD,IAAI;AAQJ,IAAI;AAKJ,IAAI,SAAS;AAEb,IAAI,gBAAgB;AAEpB,IAAI,cAAoD;AA2BxD,IAAM,SAAS,CAAC,YAA6B;AAClD,MAAI,QAAQ;AAAI,SAAK,QAAQ;AAC7B,MAAI,QAAQ;AAAK,QAAI,MAAM,QAAQ;AACnC,MAAI,QAAQ,WAAW;AAAW,aAAS,QAAQ;AACnD,MAAI,QAAQ,iBAAiB;AAAM,oBAAgB,QAAQ;AAC3D,MAAI,QAAQ;AACV,+BAA2B,QAAQ;AACrC,MAAI,QAAQ;AAAuB,QAAI,IAAI,QAAQ,qBAAqB;AACxE,MAAI,QAAQ;AAAgB,QAAI,iBAAiB,QAAQ;AACzD,MAAI,QAAQ;AAAa,kBAAc,QAAQ;AAC/C,MAAI,QAAQ;AAAW,QAAI,YAAY,QAAQ;AACjD;;;AEtEA,SAAS,OAAAA,YAAW;AAUpB,IAAM,aAAa,oBAAI,IAAqB;AAI5C,IAAI,eAAkC,CAAC;AACvC,IAAI,YAA+B,CAAC;AAMpC,IAAI,WAAW;AAMR,IAAM,YAAY;AAAA,EACvB,IAAI,OAAO;AACT,WAAO,CAAC,WAAW,QAAQ,CAAC,aAAa;AAAA,EAC3C;AAAA;AAAA,EAGA,MAAM,WAA4B;AAGhC,QAAI,WAAW,UAAU,UAAU;AACjC,iBAAW,IAAI,SAAS;AACxB,MAAAC,KAAI,QAAQ,eAAe;AAAA,IAC7B,OAAO;AACL,kBAAY,SAAS;AACrB,MAAAA,KAAI,OAAO;AAAA,IACb;AAAA,EACF;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA,KAAK,WAA4B;AAC/B,QAAI,UAAU;AACZ,MAAAA,KAAI,QAAQ,MAAM,UAAU,KAAK,SAAS,CAAC;AAAA,IAC7C,OAAO;AACL,YAAM,YAAY,aAAa,QAAQ,SAAS;AAChD,UAAI,CAAC,WAAW;AACd,qBAAa,OAAO,WAAW,CAAC;AAChC,sBAAc,SAAS;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,mBAAe,CAAC;AAChB,eAAW,MAAM;AAAA,EACnB;AACF;AAEA,SAAS,kBAAkB;AACzB,aAAW,QAAQ,WAAW;AAC9B,aAAW,MAAM;AACjB,EAAAA,KAAI,OAAO;AACb;AAEA,SAAS,YAAY,WAA4B;AAC/C,MAAI,CAAC,aAAa,SAAS,SAAS;AAAG,kBAAc,SAAS;AAChE;AAEA,SAAS,cAAc,WAA4B;AACjD,eAAa;AAAA,IACX,UAAU,cAAc,WAAS,MAAM,WAAW,UAAU,QAAQ;AAAA,IACpE;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,QAAQ,IAAY;AAC3B,QAAM,YAAY;AAElB,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,UAAM,YAAY,aAAa,CAAC;AAChC,eAAW,UAAU;AAGrB,QAAI,CAAC,UAAU,MAAM;AACnB,MAAE,YAAY,SAAS;AACvB,gBAAU,QAAQ,EAAE;AACpB,UAAI,CAAC,UAAU,MAAM;AACnB,kBAAU,KAAK,SAAS;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,aAAW;AAGX,cAAY;AACZ,YAAU,SAAS;AAInB,iBAAe;AAEf,SAAO,aAAa,SAAS;AAC/B;AAGA,SAAS,UAAa,KAAU,MAA6B;AAC3D,QAAM,QAAQ,IAAI,UAAU,IAAI;AAChC,SAAO,QAAQ,IAAI,IAAI,SAAS;AAClC;;;AC3HO,IAAM,QAAQ,CAAC,KAAa,KAAa,MAC9C,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;;;ACEzB,IAAMC,UAAS;AAAA,EACpB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,eAAe;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACf;;;ACzJA,IAAM,SAAS;AACf,IAAM,aAAa,SAAS;AAE5B,SAAS,QAAQ,OAAiB;AAChC,SAAO,aAAa,MAAM,KAAK,aAAa,IAAI;AAClD;AAEO,IAAM,MAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,QAAQ,MAAM,CAAC;AAC3D,IAAM,OAAO,IAAI,OAAO,SAAS,KAAK,QAAQ,QAAQ,QAAQ,MAAM,CAAC;AACrE,IAAM,MAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,YAAY,UAAU,CAAC;AACnE,IAAM,OAAO,IAAI;AAAA,EACtB,SAAS,KAAK,QAAQ,YAAY,YAAY,MAAM;AACtD;AACO,IAAM,OAAO;AACb,IAAM,OACX;AACK,IAAM,OAAO;AACb,IAAM,OAAO;;;ACmBb,SAAS,eAAe,OAAwB;AACrD,MAAI;AAEJ,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,UAAU,MAAM,SAAS,SAAS,KAAK,SAAS,aACnD,QACA;AAAA,EACN;AAGA,MAAK,QAAiB,KAAK,KAAK,KAAK;AACnC,WAAO,SAAS,MAAM,CAAC,IAAI,MAAM,EAAE,MAAM;AAE3C,MAAM,UAAY,OAAO,KAAK,MAAM,QAAW;AAC7C,WAAS,OAAO,KAAK;AAAA,EACvB;AAEA,MAAK,QAAiB,IAAI,KAAK,KAAK,GAAI;AACtC,YACI,SAAS,MAAM,CAAC,CAAC,KAAK;AAAA,IACrB,SAAS,MAAM,CAAC,CAAC,KAAK;AAAA,IACtB,SAAS,MAAM,CAAC,CAAC,KAAK;AAAA,IACvB;AAAA,IACF;AAAA,EAEJ;AAEA,MAAK,QAAiB,KAAK,KAAK,KAAK,GAAI;AACvC,YACI,SAAS,MAAM,CAAC,CAAC,KAAK;AAAA,IACrB,SAAS,MAAM,CAAC,CAAC,KAAK;AAAA,IACtB,SAAS,MAAM,CAAC,CAAC,KAAK;AAAA,IACvB,OAAO,MAAM,CAAC,CAAC;AAAA,IACjB;AAAA,EAEJ;AAEA,MAAK,QAAiB,KAAK,KAAK,KAAK,GAAI;AACvC,WACE;AAAA,MACE,MAAM,CAAC,IACL,MAAM,CAAC;AAAA,MACP,MAAM,CAAC,IACP,MAAM,CAAC;AAAA,MACP,MAAM,CAAC,IACP,MAAM,CAAC;AAAA,MACP;AAAA;AAAA,MACF;AAAA,IACF,MAAM;AAAA,EAEV;AAGA,MAAK,QAAiB,KAAK,KAAK,KAAK;AAAI,WAAO,SAAS,MAAM,CAAC,GAAG,EAAE,MAAM;AAE3E,MAAK,QAAiB,KAAK,KAAK,KAAK,GAAI;AACvC,WACE;AAAA,MACE,MAAM,CAAC,IACL,MAAM,CAAC;AAAA,MACP,MAAM,CAAC,IACP,MAAM,CAAC;AAAA,MACP,MAAM,CAAC,IACP,MAAM,CAAC;AAAA,MACP,MAAM,CAAC,IACP,MAAM,CAAC;AAAA;AAAA,MACT;AAAA,IACF,MAAM;AAAA,EAEV;AAEA,MAAK,QAAiB,IAAI,KAAK,KAAK,GAAI;AACtC,YACG;AAAA,MACC,SAAS,MAAM,CAAC,CAAC;AAAA;AAAA,MACjB,gBAAgB,MAAM,CAAC,CAAC;AAAA;AAAA,MACxB,gBAAgB,MAAM,CAAC,CAAC;AAAA;AAAA,IAC1B,IACE;AAAA,IACF;AAAA,EAEJ;AAEA,MAAK,QAAiB,KAAK,KAAK,KAAK,GAAI;AACvC,YACG;AAAA,MACC,SAAS,MAAM,CAAC,CAAC;AAAA;AAAA,MACjB,gBAAgB,MAAM,CAAC,CAAC;AAAA;AAAA,MACxB,gBAAgB,MAAM,CAAC,CAAC;AAAA;AAAA,IAC1B,IACE,OAAO,MAAM,CAAC,CAAC;AAAA,IACjB;AAAA,EAEJ;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,GAAW,GAAW,GAAW;AAChD,MAAI,IAAI;AAAG,SAAK;AAChB,MAAI,IAAI;AAAG,SAAK;AAChB,MAAI,IAAI,IAAI;AAAG,WAAO,KAAK,IAAI,KAAK,IAAI;AACxC,MAAI,IAAI,IAAI;AAAG,WAAO;AACtB,MAAI,IAAI,IAAI;AAAG,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAClD,SAAO;AACT;AAEA,SAAS,SAAS,GAAW,GAAW,GAAW;AACjD,QAAM,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC9C,QAAM,IAAI,IAAI,IAAI;AAClB,QAAM,IAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AACjC,QAAM,IAAI,QAAQ,GAAG,GAAG,CAAC;AACzB,QAAM,IAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AACjC,SACG,KAAK,MAAM,IAAI,GAAG,KAAK,KACvB,KAAK,MAAM,IAAI,GAAG,KAAK,KACvB,KAAK,MAAM,IAAI,GAAG,KAAK;AAE5B;AAEA,SAAS,SAAS,KAAa;AAC7B,QAAM,MAAM,SAAS,KAAK,EAAE;AAC5B,MAAI,MAAM;AAAG,WAAO;AACpB,MAAI,MAAM;AAAK,WAAO;AACtB,SAAO;AACT;AAEA,SAAS,SAAS,KAAa;AAC7B,QAAM,MAAM,WAAW,GAAG;AAC1B,UAAU,MAAM,MAAO,OAAO,MAAO;AACvC;AAEA,SAAS,OAAO,KAAa;AAC3B,QAAM,MAAM,WAAW,GAAG;AAC1B,MAAI,MAAM;AAAG,WAAO;AACpB,MAAI,MAAM;AAAG,WAAO;AACpB,SAAO,KAAK,MAAM,MAAM,GAAG;AAC7B;AAEA,SAAS,gBAAgB,KAAa;AAEpC,QAAM,MAAM,WAAW,GAAG;AAC1B,MAAI,MAAM;AAAG,WAAO;AACpB,MAAI,MAAM;AAAK,WAAO;AACtB,SAAO,MAAM;AACf;;;ACnLO,SAAS,YAAY,OAAe;AACzC,MAAI,aAAa,eAAe,KAAK;AACrC,MAAI,eAAe;AAAM,WAAO;AAChC,eAAa,cAAc;AAC3B,QAAM,KAAK,aAAa,gBAAgB;AACxC,QAAM,KAAK,aAAa,cAAgB;AACxC,QAAM,KAAK,aAAa,WAAgB;AACxC,QAAM,KAAK,aAAa,OAAc;AACtC,SAAO,QAAQ,MAAM,MAAM,MAAM;AACnC;;;ACAO,IAAM,qBAA0C,CACrD,OACA,QACA,gBACG;AACH,MAAI,GAAG,IAAI,KAAK,GAAG;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,GAAG,IAAI,KAAK,GAAG;AACjB,WAAO,mBAAmB;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,GAAG,IAAI,MAAM,OAAO,CAAC,CAAC,GAAG;AAC3B,WAAS,yBAAyB,KAAY;AAAA,EAChD;AAEA,QAAM,SAAS;AACf,QAAM,cAAc,OAAO;AAC3B,QAAM,aAAa,OAAO,SAAS,CAAC,GAAG,CAAC;AAExC,QAAM,kBACJ,OAAO,mBAAmB,OAAO,eAAe;AAClD,QAAM,mBACJ,OAAO,oBAAoB,OAAO,eAAe;AACnD,QAAM,SAAS,OAAO,WAAW,OAAK;AAEtC,SAAO,CAAC,UAAkB;AACxB,UAAMC,SAAQ,UAAU,OAAO,UAAU;AACzC,WAAO;AAAA,MACL;AAAA,MACA,WAAWA,MAAK;AAAA,MAChB,WAAWA,SAAQ,CAAC;AAAA,MACpB,YAAYA,MAAK;AAAA,MACjB,YAAYA,SAAQ,CAAC;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,YACP,OACA,UACA,UACA,WACA,WACA,QACA,iBACA,kBACA,KACA;AACA,MAAI,SAAS,MAAM,IAAI,KAAK,IAAI;AAEhC,MAAI,SAAS,UAAU;AACrB,QAAI,oBAAoB;AAAY,aAAO;AAAA,aAClC,oBAAoB;AAAS,eAAS;AAAA,EACjD;AACA,MAAI,SAAS,UAAU;AACrB,QAAI,qBAAqB;AAAY,aAAO;AAAA,aACnC,qBAAqB;AAAS,eAAS;AAAA,EAClD;AACA,MAAI,cAAc;AAAW,WAAO;AACpC,MAAI,aAAa;AAAU,WAAO,SAAS,WAAW,YAAY;AAElE,MAAI,aAAa;AAAW,aAAS,CAAC;AAAA,WAC7B,aAAa;AAAU,aAAS,SAAS;AAAA;AAC7C,cAAU,SAAS,aAAa,WAAW;AAEhD,WAAS,OAAO,MAAM;AAEtB,MAAI,cAAc;AAAW,aAAS,CAAC;AAAA,WAC9B,cAAc;AAAU,aAAS,SAAS;AAAA;AAC9C,aAAS,UAAU,YAAY,aAAa;AACjD,SAAO;AACT;AAEA,SAAS,UAAU,OAAe,YAA+B;AAE/D,WAAS,IAAI,GAAG,IAAI,WAAW,SAAS,GAAG,EAAE;AAC3C,QAAI,WAAW,CAAC,KAAK;AAAO;AAC9B,SAAO,IAAI;AACb;;;AC3FA,IAAM,QACJ,CAACC,QAAe,YAAuB,UACvC,CAACC,cAAqB;AACpB,EAAAA,YACE,cAAc,QACV,KAAK,IAAIA,WAAU,KAAK,IACxB,KAAK,IAAIA,WAAU,IAAK;AAC9B,QAAM,WAAWA,YAAWD;AAC5B,QAAM,UACJ,cAAc,QAAQ,KAAK,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAEjE,SAAO,MAAM,GAAG,GAAG,UAAUA,MAAK;AACpC;AAyCF,IAAM,KAAK;AACX,IAAM,KAAK,KAAK;AAChB,IAAM,KAAK,KAAK;AAChB,IAAM,KAAM,IAAI,KAAK,KAAM;AAC3B,IAAM,KAAM,IAAI,KAAK,KAAM;AAE3B,IAAM,YAA4B,OAAK;AACrC,QAAM,KAAK;AACX,QAAM,KAAK;AAEX,MAAI,IAAI,IAAI,IAAI;AACd,WAAO,KAAK,IAAI;AAAA,EAClB,WAAW,IAAI,IAAI,IAAI;AACrB,WAAO,MAAM,KAAK,MAAM,MAAM,IAAI;AAAA,EACpC,WAAW,IAAI,MAAM,IAAI;AACvB,WAAO,MAAM,KAAK,OAAO,MAAM,IAAI;AAAA,EACrC,OAAO;AACL,WAAO,MAAM,KAAK,QAAQ,MAAM,IAAI;AAAA,EACtC;AACF;AAEO,IAAM,UAA4B;AAAA,EACvC,QAAQ,OAAK;AAAA,EACb,YAAY,OAAK,IAAI;AAAA,EACrB,aAAa,OAAK,KAAK,IAAI,MAAM,IAAI;AAAA,EACrC,eAAe,OAAM,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AAAA,EACzE,aAAa,OAAK,IAAI,IAAI;AAAA,EAC1B,cAAc,OAAK,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAAA,EACxC,gBAAgB,OACd,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AAAA,EAC1D,aAAa,OAAK,IAAI,IAAI,IAAI;AAAA,EAC9B,cAAc,OAAK,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAAA,EACxC,gBAAgB,OACd,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AAAA,EAC9D,aAAa,OAAK,IAAI,IAAI,IAAI,IAAI;AAAA,EAClC,cAAc,OAAK,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAAA,EACxC,gBAAgB,OACd,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AAAA,EACnE,YAAY,OAAK,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,CAAC;AAAA,EAC/C,aAAa,OAAK,KAAK,IAAK,IAAI,KAAK,KAAM,CAAC;AAAA,EAC5C,eAAe,OAAK,EAAE,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK;AAAA,EACnD,YAAY,OAAM,MAAM,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,EAAE;AAAA,EACvD,aAAa,OAAM,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC;AAAA,EACxD,eAAe,OACb,MAAM,IACF,IACA,MAAM,IACJ,IACA,IAAI,MACF,KAAK,IAAI,GAAG,KAAK,IAAI,EAAE,IAAI,KAC1B,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,EAAE,KAAK;AAAA,EAC5C,YAAY,OAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC;AAAA,EACjD,aAAa,OAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,EAClD,eAAe,OACb,IAAI,OACC,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,KAAK,KACzC,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,IAAI,KAAK;AAAA,EACrD,YAAY,OAAK,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,EAC3C,aAAa,OAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC;AAAA,EACtE,eAAe,OACb,IAAI,MACC,KAAK,IAAI,IAAI,GAAG,CAAC,MAAM,KAAK,KAAK,IAAI,IAAI,MAAO,KAChD,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,KAAK;AAAA,EACrE,eAAe,OACb,MAAM,IACF,IACA,MAAM,IACJ,IACA,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,EAAE,IAAI,KAAK,KAAK,IAAI,KAAK,SAAS,EAAE;AAAA,EAClE,gBAAgB,OACd,MAAM,IACF,IACA,MAAM,IACJ,IACA,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,QAAQ,EAAE,IAAI;AAAA,EAChE,kBAAkB,OAChB,MAAM,IACF,IACA,MAAM,IACJ,IACA,IAAI,MACF,EAAE,KAAK,IAAI,GAAG,KAAK,IAAI,EAAE,IAAI,KAAK,KAAK,KAAK,IAAI,UAAU,EAAE,KAAK,IAChE,KAAK,IAAI,GAAG,MAAM,IAAI,EAAE,IAAI,KAAK,KAAK,KAAK,IAAI,UAAU,EAAE,IAAK,IACjE;AAAA,EACV,cAAc,OAAK,IAAI,UAAU,IAAI,CAAC;AAAA,EACtC,eAAe;AAAA,EACf,iBAAiB,OACf,IAAI,OAAO,IAAI,UAAU,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,KAAK;AAAA,EAC1E;AACF;;;AChIA,IAAM,OAAO,OAAO,IAAI,gBAAgB;AACxC,IAAM,aAAa,OAAO,IAAI,sBAAsB;AAgBpD,IAAM,gBAAgB,CAAC,QAAgC,QAAQ,OAAO,IAAI,IAAI,CAAC;AAM/E,IAAM,gBAA+B,CAAC,QACpC,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAGnC,IAAM,oBAAuC,CAAC,WAC5C,OAAO,UAAU,KAAK;AAQxB,SAAS,kBAAkBE,WAAe,OAAmB;AAC3D,MAAIA,UAAS,eAAe;AAC1B,IAAAA,UAAS,cAAc,KAAK;AAAA,EAC9B,OAAO;AACL,IAAAA,UAAS,KAAK;AAAA,EAChB;AACF;AAUA,SAAS,mBAAmB,QAAa,OAAmB;AAC1D,QAAM,YAAgC,OAAO,UAAU;AACvD,MAAI,WAAW;AACb,cAAU,QAAQ,CAAAA,cAAY;AAC5B,wBAAkBA,WAAU,KAAK;AAAA,IACnC,CAAC;AAAA,EACH;AACF;AAuBA,IAAe,aAAf,MAAkE;AAAA,EAMhE,YAAY,KAAe;AACzB,QAAI,CAAC,OAAO,EAAE,MAAM,KAAK,MAAM;AAC7B,YAAM,MAAM,gBAAgB;AAAA,IAC9B;AACA,mBAAe,MAAM,GAAG;AAAA,EAC1B;AAQF;AAjBW,MAEA;AAiCX,IAAM,iBAAiB,CAAC,QAAgB,QACtC,UAAU,QAAQ,MAAM,GAAG;AAa7B,SAAS,iBAAiB,QAAaA,WAAyB;AAC9D,MAAI,OAAO,IAAI,GAAG;AAChB,QAAI,YAAgC,OAAO,UAAU;AACrD,QAAI,CAAC,WAAW;AACd,gBAAU,QAAQ,YAAa,YAAY,oBAAI,IAAI,CAAE;AAAA,IACvD;AACA,QAAI,CAAC,UAAU,IAAIA,SAAQ,GAAG;AAC5B,gBAAU,IAAIA,SAAQ;AACtB,UAAI,OAAO,eAAe;AACxB,eAAO,cAAc,UAAU,MAAMA,SAAQ;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;AAaA,SAAS,oBAAoB,QAAaA,WAAyB;AACjE,QAAM,YAAgC,OAAO,UAAU;AACvD,MAAI,aAAa,UAAU,IAAIA,SAAQ,GAAG;AACxC,UAAM,QAAQ,UAAU,OAAO;AAC/B,QAAI,OAAO;AACT,gBAAU,OAAOA,SAAQ;AAAA,IAC3B,OAAO;AACL,aAAO,UAAU,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,iBAAiB;AAC1B,aAAO,gBAAgB,OAAOA,SAAQ;AAAA,IACxC;AAAA,EACF;AACF;AAEA,IAAM,YAAY,CAAC,QAAa,KAAU,UACxC,OAAO,eAAe,QAAQ,KAAK;AAAA,EACjC;AAAA,EACA,UAAU;AAAA,EACV,cAAc;AAChB,CAAC;;;ACxMI,IAAM,cAAc;AAIpB,IAAM,aACX;AAGK,IAAM,YAAY,IAAI,OAAO,IAAI,YAAY,qBAAqB,GAAG;AAGrE,IAAM,YACX;AAUK,IAAM,mBACX;;;ACXK,IAAM,iBAAiB,CAAC,UAA0B;AACvD,QAAM,CAAC,OAAO,QAAQ,IAAI,iBAAiB,KAAK;AAEhD,MAAI,CAAC,SAAS,MAAM,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,OACX,iBAAiB,SAAS,eAAe,EACzC,iBAAiB,KAAK;AAEzB,MAAI,OAAO;AAKT,WAAO,MAAM,KAAK;AAAA,EACpB,WAAW,YAAY,SAAS,WAAW,IAAI,GAAG;AAKhD,UAAMC,SAAQ,OACX,iBAAiB,SAAS,eAAe,EACzC,iBAAiB,QAAQ;AAK5B,QAAIA,QAAO;AACT,aAAOA;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,WAAW,YAAY,iBAAiB,KAAK,QAAQ,GAAG;AAItD,WAAO,eAAe,QAAQ;AAAA,EAChC,WAAW,UAAU;AAInB,WAAO;AAAA,EACT;AAMA,SAAO;AACT;AAEA,IAAM,mBAAmB,CAAC,YAAoB;AAC5C,QAAM,QAAQ,iBAAiB,KAAK,OAAO;AAC3C,MAAI,CAAC;AAAO,WAAO,CAAC,CAAC;AAErB,QAAM,CAAC,EAAE,OAAO,QAAQ,IAAI;AAC5B,SAAO,CAAC,OAAO,QAAQ;AACzB;;;ACzDA,IAAI;AAIJ,IAAM,YAAY,CAAC,GAAQ,IAAY,IAAY,IAAY,OAC7D,QAAQ,KAAK,MAAM,EAAE,MAAM,KAAK,MAAM,EAAE,MAAM,KAAK,MAAM,EAAE,MAAM;AAY5D,IAAMC,4BAA2B,CACtC,WACG;AACH,MAAI,CAAC;AACH,sBAAoB;AAAA;AAAA,MAEhB,IAAI,OAAO,IAAI,OAAO,KAAO,MAAM,EAAE,KAAK,GAAG,aAAa,GAAG;AAAA;AAAA;AAAA,MAE7D;AAAA;AAGN,QAAM,SAAS,OAAO,OAAO,IAAI,WAAS;AACxC,WAAO,cAAc,KAAK,EACvB,QAAQ,kBAAkB,cAAc,EACxC,QAAQ,YAAY,WAAW,EAC/B,QAAQ,iBAAiB,WAAW;AAAA,EACzC,CAAC;AAGD,QAAM,YAAY,OAAO,IAAI,WAAS,MAAM,MAAM,WAAW,EAAG,IAAI,MAAM,CAAC;AAG3E,QAAM,eAAe,UAAU,CAAC,EAAE;AAAA,IAAI,CAAC,GAAG,MACxC,UAAU,IAAI,YAAU;AACtB,UAAI,EAAE,KAAK,SAAS;AAClB,cAAM,MAAM,gDAAgD;AAAA,MAC9D;AACA,aAAO,OAAO,CAAC;AAAA,IACjB,CAAC;AAAA,EACH;AAGA,QAAM,gBAAgB,aAAa;AAAA,IAAI,CAAAC,YACrC,mBAAmB,EAAE,GAAG,QAAQ,QAAAA,QAAO,CAAC;AAAA,EAC1C;AAGA,SAAO,CAAC,UAAkB;AAExB,UAAM,cACJ,CAAC,UAAU,KAAK,OAAO,CAAC,CAAC,KACzB,OAAO,KAAK,WAAS,UAAU,KAAK,KAAK,CAAC,GAAG,QAAQ,aAAa,EAAE;AAEtE,QAAI,IAAI;AACR,WAAO,OAAO,CAAC,EACZ;AAAA,MACC;AAAA,MACA,MAAM,GAAG,cAAc,GAAG,EAAE,KAAK,IAAI,eAAe;AAAA,IACtD,EACC,QAAQ,WAAW,SAAS;AAAA,EACjC;AACF;;;AClFO,IAAM,SAAS;AAEf,IAAM,OAAO,CAAsC,OAAc;AACtE,QAAM,OAAO;AACb,MAAI,SAAS;AAEb,MAAI,OAAO,QAAQ,YAAY;AAC7B,UAAM,IAAI,UAAU,GAAG,0CAA0C;AAAA,EACnE;AAEA,SAAO,IAAI,SAAc;AACvB,QAAI,CAAC,QAAQ;AACX,WAAK,GAAG,IAAI;AACZ,eAAS;AAAA,IACX;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB,KAAK,QAAQ,IAAI;AAClC,SAAS,uBAAuB;AACrC;AAAA,IACE,GAAG;AAAA,EACL;AACF;AAEA,IAAM,iBAAiB,KAAK,QAAQ,IAAI;AACjC,SAAS,sBAAsB;AACpC;AAAA,IACE,GAAG;AAAA,EACL;AACF;;;AC3BO,SAAS,iBAAiB,OAAiC;AAChE,SACE,GAAG,IAAI,KAAK,MACX,MAAM,CAAC,KAAK,OACX,KAAK,KAAK,KAAK;AAAA,EAEd,CAAC,MAAM,KAAK,iBAAiB,KAAK,KAAK,KACxC,UAAY,UAAU,CAAC;AAE7B;;;ACdA,SAAS,OAAAC,YAAW;;;ACEpB,IAAI;AAEJ,IAAM,iBAAiB,oBAAI,QAAwC;AAEnE,IAAM,oBAAoB,CAAC,YACzB,QAAQ,QAAQ,CAAC,EAAE,QAAQ,YAAY,MAAM;AAC3C,SAAO,eAAe,IAAI,MAAM,GAAG,QAAQ,aAAW,QAAQ,WAAW,CAAC;AAC5E,CAAC;AAEI,SAAS,cAAc,SAA2B,QAAqB;AAI5E,MAAI,CAAC,UAAU;AACb,QAAI,OAAO,mBAAmB,aAAa;AACzC,iBAAW,IAAI,eAAe,iBAAiB;AAAA,IACjD;AAAA,EACF;AAKA,MAAI,kBAAkB,eAAe,IAAI,MAAM;AAM/C,MAAI,CAAC,iBAAiB;AACpB,sBAAkB,oBAAI,IAAI;AAC1B,mBAAe,IAAI,QAAQ,eAAe;AAAA,EAC5C;AAMA,kBAAgB,IAAI,OAAO;AAE3B,MAAI,UAAU;AACZ,aAAS,QAAQ,MAAM;AAAA,EACzB;AAKA,SAAO,MAAM;AACX,UAAMC,mBAAkB,eAAe,IAAI,MAAM;AAEjD,QAAI,CAACA;AAAiB;AAEtB,IAAAA,iBAAgB,OAAO,OAAO;AAE9B,QAAI,CAACA,iBAAgB,QAAQ,UAAU;AACrC,eAAS,UAAU,MAAM;AAAA,IAC3B;AAAA,EACF;AACF;;;ACzDA,IAAM,YAAY,oBAAI,IAAsB;AAE5C,IAAI;AAEJ,IAAM,sBAAsB,MAAM;AAChC,QAAM,eAAe,MAAM;AACzB,cAAU;AAAA,MAAQ,cAChB,SAAS;AAAA,QACP,OAAO,OAAO;AAAA,QACd,QAAQ,OAAO;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,iBAAiB,UAAU,YAAY;AAE9C,SAAO,MAAM;AACX,WAAO,oBAAoB,UAAU,YAAY;AAAA,EACnD;AACF;AAEO,IAAM,eAAe,CAAC,aAA+B;AAC1D,YAAU,IAAI,QAAQ;AAEtB,MAAI,CAAC,4BAA4B;AAC/B,iCAA6B,oBAAoB;AAAA,EACnD;AAEA,SAAO,MAAM;AACX,cAAU,OAAO,QAAQ;AAEzB,QAAI,CAAC,UAAU,QAAQ,4BAA4B;AACjD,iCAA2B;AAC3B,mCAA6B;AAAA,IAC/B;AAAA,EACF;AACF;;;AC1BO,IAAM,WAAW,CACtB,UACA,EAAE,YAAY,SAAS,gBAAgB,IAAqB,CAAC,MAC5C;AACjB,MAAI,cAAc,SAAS,iBAAiB;AAC1C,WAAO,aAAa,QAAQ;AAAA,EAC9B,OAAO;AACL,WAAO,cAAc,UAAU,SAAS;AAAA,EAC1C;AACF;;;ACrBO,IAAM,WAAW,CAAC,KAAa,KAAa,UACjD,MAAM,QAAQ,IAAI,KAAK,QAAQ,QAAQ,MAAM;;;ACG/C,IAAM,cAAc;AAAA,EAClB,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AAAA,EACA,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AACF;AAqBO,IAAM,gBAAN,MAAoB;AAAA,EAKzB,YAAY,UAA4B,WAAwB;AAWhE,SAAQ,aAAa,OAAmB;AAAA,MACtC,SAAS;AAAA,MACT,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAEA,SAAQ,aAAa,CAAC,aAAgD;AACpE,YAAM,OAAO,KAAK,KAAK,QAAQ;AAC/B,YAAM,EAAE,QAAQ,SAAS,IAAI,YAAY,QAAQ;AAEjD,WAAK,UAAU,KAAK,UAAU,SAAS,UAAU;AACjD,WAAK,eACH,KAAK,UAAU,SAAS,QAAQ,IAAI,KAAK,UAAU,SAAS,QAAQ;AAEtE,WAAK,WAAW,SAAS,GAAG,KAAK,cAAc,KAAK,OAAO;AAAA,IAC7D;AAEA,SAAQ,SAAS,MAAM;AACrB,WAAK,WAAW,GAAG;AACnB,WAAK,WAAW,GAAG;AAAA,IACrB;AAEA,SAAQ,YAAY,MAAM;AACxB,WAAK,SAAS,KAAK,IAAI;AAAA,IACzB;AAEA,mBAAU,MAAM;AACd,WAAK,OAAO;AACZ,WAAK,UAAU;AAAA,IACjB;AAvCE,SAAK,WAAW;AAChB,SAAK,YAAY;AAEjB,SAAK,OAAO;AAAA,MACV,MAAM;AAAA,MACN,GAAG,KAAK,WAAW;AAAA,MACnB,GAAG,KAAK,WAAW;AAAA,IACrB;AAAA,EACF;AAgCF;;;ALlEA,IAAM,kBAAkB,oBAAI,QAAgC;AAC5D,IAAM,kBAAkB,oBAAI,QAA+B;AAC3D,IAAM,mBAAmB,oBAAI,QAAqC;AAElE,IAAM,YAAY,CAAC,cACjB,cAAc,SAAS,kBAAkB,SAAS;AAE7C,IAAM,WAAW,CACtB,UACA,EAAE,YAAY,SAAS,gBAAgB,IAAqB,CAAC,MAC1D;AAIH,MAAI,oBAAoB,iBAAiB,IAAI,SAAS;AAKtD,MAAI,CAAC,mBAAmB;AACtB,wBAAoB,oBAAI,IAAI;AAC5B,qBAAiB,IAAI,WAAW,iBAAiB;AAAA,EACnD;AAKA,QAAM,mBAAmB,IAAI,cAAc,UAAU,SAAS;AAC9D,oBAAkB,IAAI,gBAAgB;AAKtC,MAAI,CAAC,gBAAgB,IAAI,SAAS,GAAG;AAInC,UAAM,WAAW,MAAM;AACrB,yBAAmB,QAAQ,aAAW,QAAQ,QAAQ,CAAC;AACvD,aAAO;AAAA,IACT;AAEA,oBAAgB,IAAI,WAAW,QAAQ;AAEvC,UAAM,SAAS,UAAU,SAAS;AAMlC,WAAO,iBAAiB,UAAU,UAAU,EAAE,SAAS,KAAK,CAAC;AAE7D,QAAI,cAAc,SAAS,iBAAiB;AAC1C,sBAAgB,IAAI,WAAW,SAAS,UAAU,EAAE,UAAU,CAAC,CAAC;AAAA,IAClE;AAKA,WAAO,iBAAiB,UAAU,UAAU,EAAE,SAAS,KAAK,CAAC;AAAA,EAC/D;AAKA,QAAM,gBAAgB,gBAAgB,IAAI,SAAS;AACnD,EAAAC,KAAI,aAAa;AAEjB,SAAO,MAAM;AAIX,IAAAA,KAAI,OAAO,aAAa;AAKxB,UAAMC,qBAAoB,iBAAiB,IAAI,SAAS;AACxD,QAAI,CAACA;AAAmB;AAExB,IAAAA,mBAAkB,OAAO,gBAAgB;AAEzC,QAAIA,mBAAkB;AAAM;AAK5B,UAAM,WAAW,gBAAgB,IAAI,SAAS;AAC9C,oBAAgB,OAAO,SAAS;AAEhC,QAAI,UAAU;AACZ,gBAAU,SAAS,EAAE,oBAAoB,UAAU,QAAQ;AAC3D,aAAO,oBAAoB,UAAU,QAAQ;AAE7C,sBAAgB,IAAI,SAAS,IAAI;AAAA,IACnC;AAAA,EACF;AACF;;;AM/GA,SAAS,cAAc;AAOhB,SAAS,YAAe,MAAe;AAC5C,QAAM,MAAM,OAAiB,IAAI;AAEjC,MAAI,IAAI,YAAY,MAAM;AACxB,QAAI,UAAU,KAAK;AAAA,EACrB;AAEA,SAAO,IAAI;AACb;;;ACfA,SAAS,gBAAgB;;;ACAzB,SAAS,UAAAC,eAAc;;;ACAvB,SAAS,WAAW,uBAAuB;AAWpC,IAAM,4BAA4B,MAAM,IAAI,YAAY;;;ADRxD,IAAM,eAAe,MAAM;AAChC,QAAM,YAAYC,QAAO,KAAK;AAC9B,4BAA0B,MAAM;AAC9B,cAAU,UAAU;AAEpB,WAAO,MAAM;AACX,gBAAU,UAAU;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;;;ADVO,SAAS,iBAAiB;AAC/B,QAAM,SAAS,SAAc,EAAE,CAAC;AAChC,QAAM,YAAY,aAAa;AAC/B,SAAO,MAAM;AACX,QAAI,UAAU,SAAS;AACrB,aAAO,KAAK,OAAO,CAAC;AAAA,IACtB;AAAA,EACF;AACF;;;AGZA,SAAS,aAAAC,YAAW,UAAAC,SAAQ,YAAAC,iBAAgB;AAQrC,SAAS,WAAc,WAAoB,QAAmB;AACnE,QAAM,CAAC,OAAO,IAAIA;AAAA,IAChB,OAAiB;AAAA,MACf;AAAA,MACA,QAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AAEA,QAAM,YAAYD,QAAiB;AACnC,QAAM,YAAY,UAAU;AAE5B,MAAI,QAAQ;AACZ,MAAI,OAAO;AACT,UAAM,WAAW;AAAA,MACf,UAAU,MAAM,UAAU,eAAe,QAAQ,MAAM,MAAM;AAAA,IAC/D;AACA,QAAI,CAAC,UAAU;AACb,cAAQ;AAAA,QACN;AAAA,QACA,QAAQ,UAAU;AAAA,MACpB;AAAA,IACF;AAAA,EACF,OAAO;AACL,YAAQ;AAAA,EACV;AAEA,EAAAD,WAAU,MAAM;AACd,cAAU,UAAU;AACpB,QAAI,aAAa,SAAS;AACxB,cAAQ,SAAS,QAAQ,SAAS;AAAA,IACpC;AAAA,EAEF,GAAG,CAAC,KAAK,CAAC;AAEV,SAAO,MAAM;AACf;AAEA,SAAS,eAAe,MAAa,MAAa;AAChD,MAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACtDA,SAAS,aAAAG,kBAAiC;AAEnC,IAAM,UAAU,CAAC,WAA2BA,WAAU,QAAQ,SAAS;AAE9E,IAAM,YAAmB,CAAC;;;ACL1B,SAAS,aAAAC,YAAW,UAAAC,eAAc;AAG3B,SAAS,QAAW,OAAyB;AAClD,QAAM,UAAUA,QAAY;AAC5B,EAAAD,WAAU,MAAM;AACd,YAAQ,UAAU;AAAA,EACpB,CAAC;AACD,SAAO,QAAQ;AACjB;;;ACTA,SAAS,YAAAE,iBAAgB;AAYlB,IAAM,mBAAmB,MAAM;AACpC,QAAM,CAAC,eAAe,gBAAgB,IAAIC,UAAyB,IAAI;AAEvE,4BAA0B,MAAM;AAC9B,UAAM,MAAM,OAAO,WAAW,0BAA0B;AAExD,UAAM,oBAAoB,CAAC,MAA4C;AACrE,uBAAiB,EAAE,OAAO;AAE1B,aAAO;AAAA,QACL,eAAe,EAAE;AAAA,MACnB,CAAC;AAAA,IACH;AAEA,sBAAkB,GAAG;AAErB,QAAI,IAAI,kBAAkB;AACxB,UAAI,iBAAiB,UAAU,iBAAiB;AAAA,IAClD,OAAO;AACL,UAAI,YAAY,iBAAiB;AAAA,IACnC;AAEA,WAAO,MAAM;AACX,UAAI,IAAI,qBAAqB;AAC3B,YAAI,oBAAoB,UAAU,iBAAiB;AAAA,MACrD,OAAO;AACL,YAAI,eAAe,iBAAiB;AAAA,MACtC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;;;ACbA,SAAS,OAAAC,YAAW;", "names": ["raf", "raf", "colors", "range", "steps", "progress", "observer", "value", "createStringInterpolator", "output", "raf", "elementHandlers", "raf", "containerHandlers", "useRef", "useRef", "useEffect", "useRef", "useState", "useEffect", "useEffect", "useRef", "useState", "useState", "raf"]}