/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import React from 'react';
export declare const ButtonX: React.FC;
export declare const ButtonY: React.FC;
export declare const ButtonA: React.FC;
export declare const ButtonB: React.FC;
export declare const ThumbstickL: React.FC;
export declare const ThumbstickR: React.FC;
export declare const ThumbstickLUp: React.FC;
export declare const ThumbstickLDown: React.FC;
export declare const ThumbstickLLeft: React.FC;
export declare const ThumbstickLRight: React.FC;
export declare const ThumbstickRUp: React.FC;
export declare const ThumbstickRDown: React.FC;
export declare const ThumbstickRLeft: React.FC;
export declare const ThumbstickRRight: React.FC;
export declare const GripL: React.FC;
export declare const GripR: React.FC;
export declare const TriggerL: React.FC;
export declare const TriggerR: React.FC;
export declare const ThumbrestL: React.FC;
export declare const ThumbrestR: React.FC;
interface GamepadIconProps {
    buttonName: string;
    handedness: XRHandedness;
}
export declare const GamepadIcon: React.FC<GamepadIconProps>;
export declare const IWERIcon: React.FC;
export declare const MouseLeft: React.FC;
export declare const MouseRight: React.FC;
export {};
//# sourceMappingURL=icons.d.ts.map