/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { SpatialEntity, SpatialEntityType } from './internal/entity.js';
import { PRIVATE } from './index.js';
export function getComponent(entity, componentName) {
    return entity.components.find((component) => componentName in component);
}
export const reconstructEnvironment = (sem, objectMap, json) => {
    json.spatialEntities.forEach((spatialEntityJSON) => {
        const spatialEntity = SpatialEntity.fromJSON(spatialEntityJSON);
        switch (spatialEntity.entityType) {
            case SpatialEntityType.Box:
                sem[PRIVATE].boxes.add(spatialEntity);
                break;
            case SpatialEntityType.Plane:
                sem[PRIVATE].planes.add(spatialEntity);
                break;
            case SpatialEntityType.Mesh:
                sem[PRIVATE].meshes.add(spatialEntity);
                break;
        }
        const oldMesh = objectMap.get(spatialEntityJSON.uuid);
        if (oldMesh) {
            oldMesh.removeFromParent();
        }
        objectMap.set(spatialEntityJSON.uuid, spatialEntity);
    });
};
//# sourceMappingURL=reconstruct.js.map