{"version": 3, "file": "entity.js", "sourceRoot": "", "sources": ["../../../src/native/entity.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,OAAO,CAAC;AACjD,OAAO,EACN,UAAU,EACV,WAAW,EACX,gBAAgB,EAChB,gBAAgB,GAChB,MAAM,MAAM,CAAC;AACd,OAAO,EACN,aAAa,IAAI,eAAe,EAChC,iBAAiB,GACjB,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAEN,0BAA0B,GAC1B,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AAC/D,OAAO,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AAC/D,OAAO,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AAC/D,OAAO,EAAE,sBAAsB,EAAE,MAAM,+BAA+B,CAAC;AACvE,OAAO,EAAE,qBAAqB,EAAE,MAAM,8BAA8B,CAAC;AACrE,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAE3D,MAAM,CAAN,IAAY,iBAIX;AAJD,WAAY,iBAAiB;IAC5B,oCAAe,CAAA;IACf,gCAAW,CAAA;IACX,kCAAa,CAAA;AACd,CAAC,EAJW,iBAAiB,KAAjB,iBAAiB,QAI5B;AAED,MAAM,qBAAqB,GAAgD;IAC1E,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,OAAO;IACrD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,gBAAgB,CAAC,IAAI;IACpD,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,EAAE,gBAAgB,CAAC,MAAM;IAChE,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE,gBAAgB,CAAC,IAAI;IAC1D,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,gBAAgB,CAAC,IAAI;IACrD,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC,MAAM;IACzD,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC,OAAO;IACtD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACnD,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,gBAAgB,CAAC,GAAG;IAC7C,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,gBAAgB,CAAC,IAAI;IAC/C,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,gBAAgB,CAAC,MAAM;IACnD,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,gBAAgB,CAAC,UAAU;IAC5D,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACnD,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE,gBAAgB,CAAC,KAAK;IAC3D,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACnD,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC,KAAK;CACxD,CAAC;AAEF,MAAM,OAAO,aAAc,SAAQ,IAAI;IAUtC,YAAY,OAAe,YAAY,EAAE;QACxC,KAAK,CACJ,SAAS,EACT,IAAI,kBAAkB,CAAC;YACtB,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE;YAC/B,WAAW,EAAE,IAAI;SACjB,CAAC,CACF,CAAC;QAhBI,SAAI,GAAG,wBAAwB,CAAC;QAChC,oBAAe,GAAG,IAAI,CAAC;QAEtB,kBAAa,GAGjB,IAAI,GAAG,EAAE,CAAC;QAWb,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,YAAY,CAAC;IAC1B,CAAC;IAED,iBAAiB,CAAC,KAAe;QAChC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED,YAAY,CAAC,aAAyC,EAAE,QAAa;QACpE,IAAI,SAAS,GAAkC,IAAI,CAAC;QACpD,QAAQ,aAAa,EAAE,CAAC;YACvB,KAAK,0BAA0B,CAAC,SAAS;gBACxC,SAAS,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACnD,MAAM;YACP,KAAK,0BAA0B,CAAC,SAAS;gBACxC,SAAS,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACnD,MAAM;YACP,KAAK,0BAA0B,CAAC,SAAS;gBACxC,SAAS,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACnD,MAAM;YACP,KAAK,0BAA0B,CAAC,YAAY;gBAC3C,SAAS,GAAG,IAAI,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACtD,MAAM;YACP,KAAK,0BAA0B,CAAC,aAAa;gBAC5C,SAAS,GAAG,IAAI,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACvD,MAAM;QACR,CAAC;QACD,IAAI,SAAS,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAClD,CAAC;IACF,CAAC;IAED,YAAY,CAAC,aAAyC;QACrD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAED,SAAS;QACR,MAAM,KAAK,GAAG,IAAI,aAAa,EAAE,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;YAChD,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACd,CAAC;IAED,IAAI,YAAY;QACf,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAC5C;gBACC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClB,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClB,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClB,CAAC,EAAE,CAAC;aACJ,EACD;gBACC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACpB,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACpB,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACpB,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;aACpB,CACD,CAAC;YACF,MAAM,aAAa,GAClB,qBAAqB,CAEnB,IAAI,CAAC,YAAY,CAChB,0BAA0B,CAAC,aAAa,CAEzC,CAAC,aAAa,CACf,CAAC;YAEH,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBACjD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAC3C,0BAA0B,CAAC,SAAS,CACd,CAAC;gBACxB,MAAM,OAAO,GAAuB;oBACnC,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;oBAC3C,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;oBACtD,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;oBACjE,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;oBACtD,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;iBAC3C,CAAC;gBACF,IAAI,CAAC,aAAa,GAAG,IAAI,WAAW,CACnC,gBAAgB,EAChB,OAAO,EACP,aAAa,CACb,CAAC;YACH,CAAC;iBAAM,IACN,IAAI,CAAC,UAAU,KAAK,iBAAiB,CAAC,GAAG;gBACzC,IAAI,CAAC,UAAU,KAAK,iBAAiB,CAAC,IAAI,EACzC,CAAC;gBACF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC;qBACrD,KAAqB,CAAC;gBACxB,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAM,CAAC,KAAK,CAAC,CAAC;gBAC5D,IAAI,CAAC,aAAa,GAAG,IAAI,UAAU,CAClC,gBAAgB,EAChB,QAAQ,EACR,OAAO,EACP,aAAa,CACb,CAAC;YACH,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,IAAS;QAC1B,MAAM,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC7B,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACvD,aAAa,CAAC,YAAY,CACzB,0BAA0B,CAAC,SAAS,EACpC,QAAQ,CAAC,cAAc,CACvB,CAAC;YACF,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;gBACjC,aAAa,CAAC,YAAY,CACzB,0BAA0B,CAAC,aAAa,EACxC,QAAQ,CAAC,kBAAkB,CAC3B,CAAC;YACH,CAAC;YACD,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAC7B,aAAa,CAAC,YAAY,CACzB,0BAA0B,CAAC,SAAS,EACpC,QAAQ,CAAC,cAAc,CACvB,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;gBACpC,aAAa,CAAC,YAAY,CACzB,0BAA0B,CAAC,SAAS,EACpC,QAAQ,CAAC,cAAc,CACvB,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;gBACvC,aAAa,CAAC,YAAY,CACzB,0BAA0B,CAAC,YAAY,EACvC,QAAQ,CAAC,iBAAiB,CAC1B,CAAC;YACH,CAAC;YACD,OAAO,aAAa,CAAC;QACtB,CAAC;QACD,OAAO;IACR,CAAC;IAED,IAAI,UAAU;QACb,IAAI,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7D,OAAO,iBAAiB,CAAC,KAAK,CAAC;QAChC,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAAE,CAAC;YACpE,OAAO,iBAAiB,CAAC,GAAG,CAAC;QAC9B,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,YAAY,CAAC,EAAE,CAAC;YACvE,OAAO,iBAAiB,CAAC,IAAI,CAAC;QAC/B,CAAC;QACD,OAAO;IACR,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,MAAqB;;QACpC,MAAM,QAAQ,GAAoB;YACjC,IAAI,EAAE,MAAM,CAAC,WAAW;YACxB,cAAc,EAAE,MAAA,MAAM,CAAC,YAAY,CAAC,0BAA0B,CAAC,SAAS,CAAC,0CACtE,MAAM;YACT,eAAe,EAAE,SAAS;YAC1B,cAAc,EAAE,MAAA,MAAM,CAAC,YAAY,CAAC,0BAA0B,CAAC,SAAS,CAAC,0CACtE,MAAM;YACT,cAAc,EAAE,MAAA,MAAM,CAAC,YAAY,CAAC,0BAA0B,CAAC,SAAS,CAAC,0CACtE,MAAM;YACT,kBAAkB,EAAE,MAAA,MAAM,CAAC,YAAY,CACtC,0BAA0B,CAAC,aAAa,CACxC,0CAAE,MAAM;YACT,eAAe,EAAE,SAAS;YAC1B,mBAAmB,EAAE,SAAS;YAC9B,iBAAiB,EAAE,MAAA,MAAM,CAAC,YAAY,CACrC,0BAA0B,CAAC,YAAY,CACvC,0CAAE,MAAM;YACT,kBAAkB,EAAE,SAAS;SAC7B,CAAC;QACF,OAAO,QAAQ,CAAC;IACjB,CAAC;CACD"}