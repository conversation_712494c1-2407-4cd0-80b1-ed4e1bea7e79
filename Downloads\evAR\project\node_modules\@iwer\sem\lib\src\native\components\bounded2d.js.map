{"version": 3, "file": "bounded2d.js", "sourceRoot": "", "sources": ["../../../../src/native/components/bounded2d.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AAC/C,OAAO,EACN,sBAAsB,EACtB,0BAA0B,GAC1B,MAAM,gBAAgB,CAAC;AAKxB,MAAM,OAAO,kBAAmB,SAAQ,sBAAsB;IAK7D,YAAY,aAA4B,EAAE,QAAgB;QACzD,KAAK,CAAC,aAAa,CAAC,CAAC;QALd,YAAO,GAAY,IAAI,OAAO,EAAE,CAAC;QACjC,YAAO,GAAY,IAAI,OAAO,EAAE,CAAC;QACzC,SAAI,GAAG,0BAA0B,CAAC,SAAS,CAAC;QAI3C,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;QACpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAO,CAAC,CAAC,EAAE,MAAO,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAO,CAAC,KAAK,EAAE,MAAO,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,aAAa,EAAE,CAAC;IACtB,CAAC;IAED,aAAa;;QACZ,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACnE,QAAQ,CAAC,SAAS,CACjB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EACnC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EACnC,CAAC,CACD,CAAC;QACF,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAC9B,MAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,0CAAE,OAAO,EAAE,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzC,CAAC;IAED,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAED,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAED,IAAI,QAAQ;QACX,OAAO;YACN,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;SACnB,CAAC;IACH,CAAC;IAED,IAAI,MAAM;QACT,OAAO;YACN,MAAM,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;YAChD,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;SAC/C,CAAC;IACb,CAAC;CACD"}