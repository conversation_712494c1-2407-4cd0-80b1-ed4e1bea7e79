// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.0
//   protoc               v5.29.1
// source: protos/validate.proto
/* eslint-disable */
import { <PERSON>ary<PERSON><PERSON>er, BinaryWriter } from "@bufbuild/protobuf/wire";
import { fieldDescriptorProto_TypeFromJSON, fieldDescriptorProto_TypeToJSON, } from "../google/protobuf/descriptor";
import { Duration } from "../google/protobuf/duration";
import { Timestamp } from "../google/protobuf/timestamp";
export const protobufPackage = "buf.validate";
/**
 * Specifies how FieldConstraints.ignore behaves. See the documentation for
 * FieldConstraints.required for definitions of "populated" and "nullable".
 */
export var Ignore;
(function (Ignore) {
    /**
     * IGNORE_UNSPECIFIED - Validation is only skipped if it's an unpopulated nullable fields.
     *
     * ```proto
     * syntax="proto3";
     *
     * message Request {
     *   // The uri rule applies to any value, including the empty string.
     *   string foo = 1 [
     *     (buf.validate.field).string.uri = true
     *   ];
     *
     *   // The uri rule only applies if the field is set, including if it's
     *   // set to the empty string.
     *   optional string bar = 2 [
     *     (buf.validate.field).string.uri = true
     *   ];
     *
     *   // The min_items rule always applies, even if the list is empty.
     *   repeated string baz = 3 [
     *     (buf.validate.field).repeated.min_items = 3
     *   ];
     *
     *   // The custom CEL rule applies only if the field is set, including if
     *   // it's the "zero" value of that message.
     *   SomeMessage quux = 4 [
     *     (buf.validate.field).cel = {/* ... * /}
     *   ];
     * }
     * ```
     */
    Ignore[Ignore["IGNORE_UNSPECIFIED"] = 0] = "IGNORE_UNSPECIFIED";
    /**
     * IGNORE_IF_UNPOPULATED - Validation is skipped if the field is unpopulated. This rule is redundant
     * if the field is already nullable. This value is equivalent behavior to the
     * deprecated ignore_empty rule.
     *
     * ```proto
     * syntax="proto3
     *
     * message Request {
     *   // The uri rule applies only if the value is not the empty string.
     *   string foo = 1 [
     *     (buf.validate.field).string.uri = true,
     *     (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
     *   ];
     *
     *   // IGNORE_IF_UNPOPULATED is equivalent to IGNORE_UNSPECIFIED in this
     *   // case: the uri rule only applies if the field is set, including if
     *   // it's set to the empty string.
     *   optional string bar = 2 [
     *     (buf.validate.field).string.uri = true,
     *     (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
     *   ];
     *
     *   // The min_items rule only applies if the list has at least one item.
     *   repeated string baz = 3 [
     *     (buf.validate.field).repeated.min_items = 3,
     *     (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
     *   ];
     *
     *   // IGNORE_IF_UNPOPULATED is equivalent to IGNORE_UNSPECIFIED in this
     *   // case: the custom CEL rule applies only if the field is set, including
     *   // if it's the "zero" value of that message.
     *   SomeMessage quux = 4 [
     *     (buf.validate.field).cel = {/* ... * /},
     *     (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
     *   ];
     * }
     * ```
     */
    Ignore[Ignore["IGNORE_IF_UNPOPULATED"] = 1] = "IGNORE_IF_UNPOPULATED";
    /**
     * IGNORE_IF_DEFAULT_VALUE - Validation is skipped if the field is unpopulated or if it is a nullable
     * field populated with its default value. This is typically the zero or
     * empty value, but proto2 scalars support custom defaults. For messages, the
     * default is a non-null message with all its fields unpopulated.
     *
     * ```proto
     * syntax="proto3
     *
     * message Request {
     *   // IGNORE_IF_DEFAULT_VALUE is equivalent to IGNORE_IF_UNPOPULATED in
     *   // this case; the uri rule applies only if the value is not the empty
     *   // string.
     *   string foo = 1 [
     *     (buf.validate.field).string.uri = true,
     *     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE
     *   ];
     *
     *   // The uri rule only applies if the field is set to a value other than
     *   // the empty string.
     *   optional string bar = 2 [
     *     (buf.validate.field).string.uri = true,
     *     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE
     *   ];
     *
     *   // IGNORE_IF_DEFAULT_VALUE is equivalent to IGNORE_IF_UNPOPULATED in
     *   // this case; the min_items rule only applies if the list has at least
     *   // one item.
     *   repeated string baz = 3 [
     *     (buf.validate.field).repeated.min_items = 3,
     *     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE
     *   ];
     *
     *   // The custom CEL rule only applies if the field is set to a value other
     *   // than an empty message (i.e., fields are unpopulated).
     *   SomeMessage quux = 4 [
     *     (buf.validate.field).cel = {/* ... * /},
     *     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE
     *   ];
     * }
     * ```
     *
     * This rule is affected by proto2 custom default values:
     *
     * ```proto
     * syntax="proto2";
     *
     * message Request {
     *   // The gt rule only applies if the field is set and it's value is not
     *   the default (i.e., not -42). The rule even applies if the field is set
     *   to zero since the default value differs.
     *   optional int32 value = 1 [
     *     default = -42,
     *     (buf.validate.field).int32.gt = 0,
     *     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE
     *   ];
     * }
     */
    Ignore[Ignore["IGNORE_IF_DEFAULT_VALUE"] = 2] = "IGNORE_IF_DEFAULT_VALUE";
    /**
     * IGNORE_ALWAYS - The validation rules of this field will be skipped and not evaluated. This
     * is useful for situations that necessitate turning off the rules of a field
     * containing a message that may not make sense in the current context, or to
     * temporarily disable constraints during development.
     *
     * ```proto
     * message MyMessage {
     *   // The field's rules will always be ignored, including any validation's
     *   // on value's fields.
     *   MyOtherMessage value = 1 [
     *     (buf.validate.field).ignore = IGNORE_ALWAYS];
     * }
     * ```
     */
    Ignore[Ignore["IGNORE_ALWAYS"] = 3] = "IGNORE_ALWAYS";
    /**
     * IGNORE_EMPTY - Deprecated: Use IGNORE_IF_UNPOPULATED instead. TODO: Remove this value pre-v1.
     *
     * @deprecated
     */
    Ignore[Ignore["IGNORE_EMPTY"] = 1] = "IGNORE_EMPTY";
    /**
     * IGNORE_DEFAULT - Deprecated: Use IGNORE_IF_DEFAULT_VALUE. TODO: Remove this value pre-v1.
     *
     * @deprecated
     */
    Ignore[Ignore["IGNORE_DEFAULT"] = 2] = "IGNORE_DEFAULT";
    Ignore[Ignore["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(Ignore || (Ignore = {}));
export function ignoreFromJSON(object) {
    switch (object) {
        case 0:
        case "IGNORE_UNSPECIFIED":
            return Ignore.IGNORE_UNSPECIFIED;
        case 1:
        case "IGNORE_IF_UNPOPULATED":
            return Ignore.IGNORE_IF_UNPOPULATED;
        case 2:
        case "IGNORE_IF_DEFAULT_VALUE":
            return Ignore.IGNORE_IF_DEFAULT_VALUE;
        case 3:
        case "IGNORE_ALWAYS":
            return Ignore.IGNORE_ALWAYS;
        case 1:
        case "IGNORE_EMPTY":
            return Ignore.IGNORE_EMPTY;
        case 2:
        case "IGNORE_DEFAULT":
            return Ignore.IGNORE_DEFAULT;
        case -1:
        case "UNRECOGNIZED":
        default:
            return Ignore.UNRECOGNIZED;
    }
}
export function ignoreToJSON(object) {
    switch (object) {
        case Ignore.IGNORE_UNSPECIFIED:
            return "IGNORE_UNSPECIFIED";
        case Ignore.IGNORE_IF_UNPOPULATED:
            return "IGNORE_IF_UNPOPULATED";
        case Ignore.IGNORE_IF_DEFAULT_VALUE:
            return "IGNORE_IF_DEFAULT_VALUE";
        case Ignore.IGNORE_ALWAYS:
            return "IGNORE_ALWAYS";
        case Ignore.IGNORE_EMPTY:
            return "IGNORE_EMPTY";
        case Ignore.IGNORE_DEFAULT:
            return "IGNORE_DEFAULT";
        case Ignore.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
/** WellKnownRegex contain some well-known patterns. */
export var KnownRegex;
(function (KnownRegex) {
    KnownRegex[KnownRegex["KNOWN_REGEX_UNSPECIFIED"] = 0] = "KNOWN_REGEX_UNSPECIFIED";
    /** KNOWN_REGEX_HTTP_HEADER_NAME - HTTP header name as defined by [RFC 7230](https://tools.ietf.org/html/rfc7230#section-3.2). */
    KnownRegex[KnownRegex["KNOWN_REGEX_HTTP_HEADER_NAME"] = 1] = "KNOWN_REGEX_HTTP_HEADER_NAME";
    /** KNOWN_REGEX_HTTP_HEADER_VALUE - HTTP header value as defined by [RFC 7230](https://tools.ietf.org/html/rfc7230#section-3.2.4). */
    KnownRegex[KnownRegex["KNOWN_REGEX_HTTP_HEADER_VALUE"] = 2] = "KNOWN_REGEX_HTTP_HEADER_VALUE";
    KnownRegex[KnownRegex["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(KnownRegex || (KnownRegex = {}));
export function knownRegexFromJSON(object) {
    switch (object) {
        case 0:
        case "KNOWN_REGEX_UNSPECIFIED":
            return KnownRegex.KNOWN_REGEX_UNSPECIFIED;
        case 1:
        case "KNOWN_REGEX_HTTP_HEADER_NAME":
            return KnownRegex.KNOWN_REGEX_HTTP_HEADER_NAME;
        case 2:
        case "KNOWN_REGEX_HTTP_HEADER_VALUE":
            return KnownRegex.KNOWN_REGEX_HTTP_HEADER_VALUE;
        case -1:
        case "UNRECOGNIZED":
        default:
            return KnownRegex.UNRECOGNIZED;
    }
}
export function knownRegexToJSON(object) {
    switch (object) {
        case KnownRegex.KNOWN_REGEX_UNSPECIFIED:
            return "KNOWN_REGEX_UNSPECIFIED";
        case KnownRegex.KNOWN_REGEX_HTTP_HEADER_NAME:
            return "KNOWN_REGEX_HTTP_HEADER_NAME";
        case KnownRegex.KNOWN_REGEX_HTTP_HEADER_VALUE:
            return "KNOWN_REGEX_HTTP_HEADER_VALUE";
        case KnownRegex.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseConstraint() {
    return { id: "", message: "", expression: "" };
}
export const Constraint = {
    encode(message, writer = new BinaryWriter()) {
        if (message.id !== undefined && message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.message !== undefined && message.message !== "") {
            writer.uint32(18).string(message.message);
        }
        if (message.expression !== undefined && message.expression !== "") {
            writer.uint32(26).string(message.expression);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseConstraint();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.id = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.message = reader.string();
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.expression = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? globalThis.String(object.id) : "",
            message: isSet(object.message) ? globalThis.String(object.message) : "",
            expression: isSet(object.expression) ? globalThis.String(object.expression) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.id !== undefined && message.id !== "") {
            obj.id = message.id;
        }
        if (message.message !== undefined && message.message !== "") {
            obj.message = message.message;
        }
        if (message.expression !== undefined && message.expression !== "") {
            obj.expression = message.expression;
        }
        return obj;
    },
    create(base) {
        return Constraint.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseConstraint();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "";
        message.message = (_b = object.message) !== null && _b !== void 0 ? _b : "";
        message.expression = (_c = object.expression) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseMessageConstraints() {
    return { disabled: false, cel: [] };
}
export const MessageConstraints = {
    encode(message, writer = new BinaryWriter()) {
        if (message.disabled !== undefined && message.disabled !== false) {
            writer.uint32(8).bool(message.disabled);
        }
        for (const v of message.cel) {
            Constraint.encode(v, writer.uint32(26).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMessageConstraints();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.disabled = reader.bool();
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.cel.push(Constraint.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            disabled: isSet(object.disabled) ? globalThis.Boolean(object.disabled) : false,
            cel: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.cel) ? object.cel.map((e) => Constraint.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if (message.disabled !== undefined && message.disabled !== false) {
            obj.disabled = message.disabled;
        }
        if ((_a = message.cel) === null || _a === void 0 ? void 0 : _a.length) {
            obj.cel = message.cel.map((e) => Constraint.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return MessageConstraints.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseMessageConstraints();
        message.disabled = (_a = object.disabled) !== null && _a !== void 0 ? _a : false;
        message.cel = ((_b = object.cel) === null || _b === void 0 ? void 0 : _b.map((e) => Constraint.fromPartial(e))) || [];
        return message;
    },
};
function createBaseOneofConstraints() {
    return { required: false };
}
export const OneofConstraints = {
    encode(message, writer = new BinaryWriter()) {
        if (message.required !== undefined && message.required !== false) {
            writer.uint32(8).bool(message.required);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseOneofConstraints();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.required = reader.bool();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { required: isSet(object.required) ? globalThis.Boolean(object.required) : false };
    },
    toJSON(message) {
        const obj = {};
        if (message.required !== undefined && message.required !== false) {
            obj.required = message.required;
        }
        return obj;
    },
    create(base) {
        return OneofConstraints.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseOneofConstraints();
        message.required = (_a = object.required) !== null && _a !== void 0 ? _a : false;
        return message;
    },
};
function createBaseFieldConstraints() {
    return {
        cel: [],
        required: false,
        ignore: 0,
        float: undefined,
        double: undefined,
        int32: undefined,
        int64: undefined,
        uint32: undefined,
        uint64: undefined,
        sint32: undefined,
        sint64: undefined,
        fixed32: undefined,
        fixed64: undefined,
        sfixed32: undefined,
        sfixed64: undefined,
        bool: undefined,
        string: undefined,
        bytes: undefined,
        enum: undefined,
        repeated: undefined,
        map: undefined,
        any: undefined,
        duration: undefined,
        timestamp: undefined,
        skipped: false,
        ignoreEmpty: false,
    };
}
export const FieldConstraints = {
    encode(message, writer = new BinaryWriter()) {
        for (const v of message.cel) {
            Constraint.encode(v, writer.uint32(186).fork()).join();
        }
        if (message.required !== undefined && message.required !== false) {
            writer.uint32(200).bool(message.required);
        }
        if (message.ignore !== undefined && message.ignore !== 0) {
            writer.uint32(216).int32(message.ignore);
        }
        if (message.float !== undefined) {
            FloatRules.encode(message.float, writer.uint32(10).fork()).join();
        }
        if (message.double !== undefined) {
            DoubleRules.encode(message.double, writer.uint32(18).fork()).join();
        }
        if (message.int32 !== undefined) {
            Int32Rules.encode(message.int32, writer.uint32(26).fork()).join();
        }
        if (message.int64 !== undefined) {
            Int64Rules.encode(message.int64, writer.uint32(34).fork()).join();
        }
        if (message.uint32 !== undefined) {
            UInt32Rules.encode(message.uint32, writer.uint32(42).fork()).join();
        }
        if (message.uint64 !== undefined) {
            UInt64Rules.encode(message.uint64, writer.uint32(50).fork()).join();
        }
        if (message.sint32 !== undefined) {
            SInt32Rules.encode(message.sint32, writer.uint32(58).fork()).join();
        }
        if (message.sint64 !== undefined) {
            SInt64Rules.encode(message.sint64, writer.uint32(66).fork()).join();
        }
        if (message.fixed32 !== undefined) {
            Fixed32Rules.encode(message.fixed32, writer.uint32(74).fork()).join();
        }
        if (message.fixed64 !== undefined) {
            Fixed64Rules.encode(message.fixed64, writer.uint32(82).fork()).join();
        }
        if (message.sfixed32 !== undefined) {
            SFixed32Rules.encode(message.sfixed32, writer.uint32(90).fork()).join();
        }
        if (message.sfixed64 !== undefined) {
            SFixed64Rules.encode(message.sfixed64, writer.uint32(98).fork()).join();
        }
        if (message.bool !== undefined) {
            BoolRules.encode(message.bool, writer.uint32(106).fork()).join();
        }
        if (message.string !== undefined) {
            StringRules.encode(message.string, writer.uint32(114).fork()).join();
        }
        if (message.bytes !== undefined) {
            BytesRules.encode(message.bytes, writer.uint32(122).fork()).join();
        }
        if (message.enum !== undefined) {
            EnumRules.encode(message.enum, writer.uint32(130).fork()).join();
        }
        if (message.repeated !== undefined) {
            RepeatedRules.encode(message.repeated, writer.uint32(146).fork()).join();
        }
        if (message.map !== undefined) {
            MapRules.encode(message.map, writer.uint32(154).fork()).join();
        }
        if (message.any !== undefined) {
            AnyRules.encode(message.any, writer.uint32(162).fork()).join();
        }
        if (message.duration !== undefined) {
            DurationRules.encode(message.duration, writer.uint32(170).fork()).join();
        }
        if (message.timestamp !== undefined) {
            TimestampRules.encode(message.timestamp, writer.uint32(178).fork()).join();
        }
        if (message.skipped !== undefined && message.skipped !== false) {
            writer.uint32(192).bool(message.skipped);
        }
        if (message.ignoreEmpty !== undefined && message.ignoreEmpty !== false) {
            writer.uint32(208).bool(message.ignoreEmpty);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseFieldConstraints();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 23: {
                    if (tag !== 186) {
                        break;
                    }
                    message.cel.push(Constraint.decode(reader, reader.uint32()));
                    continue;
                }
                case 25: {
                    if (tag !== 200) {
                        break;
                    }
                    message.required = reader.bool();
                    continue;
                }
                case 27: {
                    if (tag !== 216) {
                        break;
                    }
                    message.ignore = reader.int32();
                    continue;
                }
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.float = FloatRules.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.double = DoubleRules.decode(reader, reader.uint32());
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.int32 = Int32Rules.decode(reader, reader.uint32());
                    continue;
                }
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.int64 = Int64Rules.decode(reader, reader.uint32());
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.uint32 = UInt32Rules.decode(reader, reader.uint32());
                    continue;
                }
                case 6: {
                    if (tag !== 50) {
                        break;
                    }
                    message.uint64 = UInt64Rules.decode(reader, reader.uint32());
                    continue;
                }
                case 7: {
                    if (tag !== 58) {
                        break;
                    }
                    message.sint32 = SInt32Rules.decode(reader, reader.uint32());
                    continue;
                }
                case 8: {
                    if (tag !== 66) {
                        break;
                    }
                    message.sint64 = SInt64Rules.decode(reader, reader.uint32());
                    continue;
                }
                case 9: {
                    if (tag !== 74) {
                        break;
                    }
                    message.fixed32 = Fixed32Rules.decode(reader, reader.uint32());
                    continue;
                }
                case 10: {
                    if (tag !== 82) {
                        break;
                    }
                    message.fixed64 = Fixed64Rules.decode(reader, reader.uint32());
                    continue;
                }
                case 11: {
                    if (tag !== 90) {
                        break;
                    }
                    message.sfixed32 = SFixed32Rules.decode(reader, reader.uint32());
                    continue;
                }
                case 12: {
                    if (tag !== 98) {
                        break;
                    }
                    message.sfixed64 = SFixed64Rules.decode(reader, reader.uint32());
                    continue;
                }
                case 13: {
                    if (tag !== 106) {
                        break;
                    }
                    message.bool = BoolRules.decode(reader, reader.uint32());
                    continue;
                }
                case 14: {
                    if (tag !== 114) {
                        break;
                    }
                    message.string = StringRules.decode(reader, reader.uint32());
                    continue;
                }
                case 15: {
                    if (tag !== 122) {
                        break;
                    }
                    message.bytes = BytesRules.decode(reader, reader.uint32());
                    continue;
                }
                case 16: {
                    if (tag !== 130) {
                        break;
                    }
                    message.enum = EnumRules.decode(reader, reader.uint32());
                    continue;
                }
                case 18: {
                    if (tag !== 146) {
                        break;
                    }
                    message.repeated = RepeatedRules.decode(reader, reader.uint32());
                    continue;
                }
                case 19: {
                    if (tag !== 154) {
                        break;
                    }
                    message.map = MapRules.decode(reader, reader.uint32());
                    continue;
                }
                case 20: {
                    if (tag !== 162) {
                        break;
                    }
                    message.any = AnyRules.decode(reader, reader.uint32());
                    continue;
                }
                case 21: {
                    if (tag !== 170) {
                        break;
                    }
                    message.duration = DurationRules.decode(reader, reader.uint32());
                    continue;
                }
                case 22: {
                    if (tag !== 178) {
                        break;
                    }
                    message.timestamp = TimestampRules.decode(reader, reader.uint32());
                    continue;
                }
                case 24: {
                    if (tag !== 192) {
                        break;
                    }
                    message.skipped = reader.bool();
                    continue;
                }
                case 26: {
                    if (tag !== 208) {
                        break;
                    }
                    message.ignoreEmpty = reader.bool();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            cel: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.cel) ? object.cel.map((e) => Constraint.fromJSON(e)) : [],
            required: isSet(object.required) ? globalThis.Boolean(object.required) : false,
            ignore: isSet(object.ignore) ? ignoreFromJSON(object.ignore) : 0,
            float: isSet(object.float) ? FloatRules.fromJSON(object.float) : undefined,
            double: isSet(object.double) ? DoubleRules.fromJSON(object.double) : undefined,
            int32: isSet(object.int32) ? Int32Rules.fromJSON(object.int32) : undefined,
            int64: isSet(object.int64) ? Int64Rules.fromJSON(object.int64) : undefined,
            uint32: isSet(object.uint32) ? UInt32Rules.fromJSON(object.uint32) : undefined,
            uint64: isSet(object.uint64) ? UInt64Rules.fromJSON(object.uint64) : undefined,
            sint32: isSet(object.sint32) ? SInt32Rules.fromJSON(object.sint32) : undefined,
            sint64: isSet(object.sint64) ? SInt64Rules.fromJSON(object.sint64) : undefined,
            fixed32: isSet(object.fixed32) ? Fixed32Rules.fromJSON(object.fixed32) : undefined,
            fixed64: isSet(object.fixed64) ? Fixed64Rules.fromJSON(object.fixed64) : undefined,
            sfixed32: isSet(object.sfixed32) ? SFixed32Rules.fromJSON(object.sfixed32) : undefined,
            sfixed64: isSet(object.sfixed64) ? SFixed64Rules.fromJSON(object.sfixed64) : undefined,
            bool: isSet(object.bool) ? BoolRules.fromJSON(object.bool) : undefined,
            string: isSet(object.string) ? StringRules.fromJSON(object.string) : undefined,
            bytes: isSet(object.bytes) ? BytesRules.fromJSON(object.bytes) : undefined,
            enum: isSet(object.enum) ? EnumRules.fromJSON(object.enum) : undefined,
            repeated: isSet(object.repeated) ? RepeatedRules.fromJSON(object.repeated) : undefined,
            map: isSet(object.map) ? MapRules.fromJSON(object.map) : undefined,
            any: isSet(object.any) ? AnyRules.fromJSON(object.any) : undefined,
            duration: isSet(object.duration) ? DurationRules.fromJSON(object.duration) : undefined,
            timestamp: isSet(object.timestamp) ? TimestampRules.fromJSON(object.timestamp) : undefined,
            skipped: isSet(object.skipped) ? globalThis.Boolean(object.skipped) : false,
            ignoreEmpty: isSet(object.ignoreEmpty) ? globalThis.Boolean(object.ignoreEmpty) : false,
        };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if ((_a = message.cel) === null || _a === void 0 ? void 0 : _a.length) {
            obj.cel = message.cel.map((e) => Constraint.toJSON(e));
        }
        if (message.required !== undefined && message.required !== false) {
            obj.required = message.required;
        }
        if (message.ignore !== undefined && message.ignore !== 0) {
            obj.ignore = ignoreToJSON(message.ignore);
        }
        if (message.float !== undefined) {
            obj.float = FloatRules.toJSON(message.float);
        }
        if (message.double !== undefined) {
            obj.double = DoubleRules.toJSON(message.double);
        }
        if (message.int32 !== undefined) {
            obj.int32 = Int32Rules.toJSON(message.int32);
        }
        if (message.int64 !== undefined) {
            obj.int64 = Int64Rules.toJSON(message.int64);
        }
        if (message.uint32 !== undefined) {
            obj.uint32 = UInt32Rules.toJSON(message.uint32);
        }
        if (message.uint64 !== undefined) {
            obj.uint64 = UInt64Rules.toJSON(message.uint64);
        }
        if (message.sint32 !== undefined) {
            obj.sint32 = SInt32Rules.toJSON(message.sint32);
        }
        if (message.sint64 !== undefined) {
            obj.sint64 = SInt64Rules.toJSON(message.sint64);
        }
        if (message.fixed32 !== undefined) {
            obj.fixed32 = Fixed32Rules.toJSON(message.fixed32);
        }
        if (message.fixed64 !== undefined) {
            obj.fixed64 = Fixed64Rules.toJSON(message.fixed64);
        }
        if (message.sfixed32 !== undefined) {
            obj.sfixed32 = SFixed32Rules.toJSON(message.sfixed32);
        }
        if (message.sfixed64 !== undefined) {
            obj.sfixed64 = SFixed64Rules.toJSON(message.sfixed64);
        }
        if (message.bool !== undefined) {
            obj.bool = BoolRules.toJSON(message.bool);
        }
        if (message.string !== undefined) {
            obj.string = StringRules.toJSON(message.string);
        }
        if (message.bytes !== undefined) {
            obj.bytes = BytesRules.toJSON(message.bytes);
        }
        if (message.enum !== undefined) {
            obj.enum = EnumRules.toJSON(message.enum);
        }
        if (message.repeated !== undefined) {
            obj.repeated = RepeatedRules.toJSON(message.repeated);
        }
        if (message.map !== undefined) {
            obj.map = MapRules.toJSON(message.map);
        }
        if (message.any !== undefined) {
            obj.any = AnyRules.toJSON(message.any);
        }
        if (message.duration !== undefined) {
            obj.duration = DurationRules.toJSON(message.duration);
        }
        if (message.timestamp !== undefined) {
            obj.timestamp = TimestampRules.toJSON(message.timestamp);
        }
        if (message.skipped !== undefined && message.skipped !== false) {
            obj.skipped = message.skipped;
        }
        if (message.ignoreEmpty !== undefined && message.ignoreEmpty !== false) {
            obj.ignoreEmpty = message.ignoreEmpty;
        }
        return obj;
    },
    create(base) {
        return FieldConstraints.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBaseFieldConstraints();
        message.cel = ((_a = object.cel) === null || _a === void 0 ? void 0 : _a.map((e) => Constraint.fromPartial(e))) || [];
        message.required = (_b = object.required) !== null && _b !== void 0 ? _b : false;
        message.ignore = (_c = object.ignore) !== null && _c !== void 0 ? _c : 0;
        message.float = (object.float !== undefined && object.float !== null)
            ? FloatRules.fromPartial(object.float)
            : undefined;
        message.double = (object.double !== undefined && object.double !== null)
            ? DoubleRules.fromPartial(object.double)
            : undefined;
        message.int32 = (object.int32 !== undefined && object.int32 !== null)
            ? Int32Rules.fromPartial(object.int32)
            : undefined;
        message.int64 = (object.int64 !== undefined && object.int64 !== null)
            ? Int64Rules.fromPartial(object.int64)
            : undefined;
        message.uint32 = (object.uint32 !== undefined && object.uint32 !== null)
            ? UInt32Rules.fromPartial(object.uint32)
            : undefined;
        message.uint64 = (object.uint64 !== undefined && object.uint64 !== null)
            ? UInt64Rules.fromPartial(object.uint64)
            : undefined;
        message.sint32 = (object.sint32 !== undefined && object.sint32 !== null)
            ? SInt32Rules.fromPartial(object.sint32)
            : undefined;
        message.sint64 = (object.sint64 !== undefined && object.sint64 !== null)
            ? SInt64Rules.fromPartial(object.sint64)
            : undefined;
        message.fixed32 = (object.fixed32 !== undefined && object.fixed32 !== null)
            ? Fixed32Rules.fromPartial(object.fixed32)
            : undefined;
        message.fixed64 = (object.fixed64 !== undefined && object.fixed64 !== null)
            ? Fixed64Rules.fromPartial(object.fixed64)
            : undefined;
        message.sfixed32 = (object.sfixed32 !== undefined && object.sfixed32 !== null)
            ? SFixed32Rules.fromPartial(object.sfixed32)
            : undefined;
        message.sfixed64 = (object.sfixed64 !== undefined && object.sfixed64 !== null)
            ? SFixed64Rules.fromPartial(object.sfixed64)
            : undefined;
        message.bool = (object.bool !== undefined && object.bool !== null) ? BoolRules.fromPartial(object.bool) : undefined;
        message.string = (object.string !== undefined && object.string !== null)
            ? StringRules.fromPartial(object.string)
            : undefined;
        message.bytes = (object.bytes !== undefined && object.bytes !== null)
            ? BytesRules.fromPartial(object.bytes)
            : undefined;
        message.enum = (object.enum !== undefined && object.enum !== null) ? EnumRules.fromPartial(object.enum) : undefined;
        message.repeated = (object.repeated !== undefined && object.repeated !== null)
            ? RepeatedRules.fromPartial(object.repeated)
            : undefined;
        message.map = (object.map !== undefined && object.map !== null) ? MapRules.fromPartial(object.map) : undefined;
        message.any = (object.any !== undefined && object.any !== null) ? AnyRules.fromPartial(object.any) : undefined;
        message.duration = (object.duration !== undefined && object.duration !== null)
            ? DurationRules.fromPartial(object.duration)
            : undefined;
        message.timestamp = (object.timestamp !== undefined && object.timestamp !== null)
            ? TimestampRules.fromPartial(object.timestamp)
            : undefined;
        message.skipped = (_d = object.skipped) !== null && _d !== void 0 ? _d : false;
        message.ignoreEmpty = (_e = object.ignoreEmpty) !== null && _e !== void 0 ? _e : false;
        return message;
    },
};
function createBasePredefinedConstraints() {
    return { cel: [] };
}
export const PredefinedConstraints = {
    encode(message, writer = new BinaryWriter()) {
        for (const v of message.cel) {
            Constraint.encode(v, writer.uint32(10).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePredefinedConstraints();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.cel.push(Constraint.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { cel: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.cel) ? object.cel.map((e) => Constraint.fromJSON(e)) : [] };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if ((_a = message.cel) === null || _a === void 0 ? void 0 : _a.length) {
            obj.cel = message.cel.map((e) => Constraint.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return PredefinedConstraints.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBasePredefinedConstraints();
        message.cel = ((_a = object.cel) === null || _a === void 0 ? void 0 : _a.map((e) => Constraint.fromPartial(e))) || [];
        return message;
    },
};
function createBaseFloatRules() {
    return {
        const: 0,
        lt: undefined,
        lte: undefined,
        gt: undefined,
        gte: undefined,
        in: [],
        notIn: [],
        finite: false,
        example: [],
    };
}
export const FloatRules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(13).float(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(21).float(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(29).float(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(37).float(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(45).float(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.float(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.float(v);
        }
        writer.join();
        if (message.finite !== undefined && message.finite !== false) {
            writer.uint32(64).bool(message.finite);
        }
        writer.uint32(74).fork();
        for (const v of message.example) {
            writer.float(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseFloatRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 13) {
                        break;
                    }
                    message.const = reader.float();
                    continue;
                }
                case 2: {
                    if (tag !== 21) {
                        break;
                    }
                    message.lt = reader.float();
                    continue;
                }
                case 3: {
                    if (tag !== 29) {
                        break;
                    }
                    message.lte = reader.float();
                    continue;
                }
                case 4: {
                    if (tag !== 37) {
                        break;
                    }
                    message.gt = reader.float();
                    continue;
                }
                case 5: {
                    if (tag !== 45) {
                        break;
                    }
                    message.gte = reader.float();
                    continue;
                }
                case 6: {
                    if (tag === 53) {
                        message.in.push(reader.float());
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(reader.float());
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 61) {
                        message.notIn.push(reader.float());
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(reader.float());
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag !== 64) {
                        break;
                    }
                    message.finite = reader.bool();
                    continue;
                }
                case 9: {
                    if (tag === 77) {
                        message.example.push(reader.float());
                        continue;
                    }
                    if (tag === 74) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(reader.float());
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            finite: isSet(object.finite) ? globalThis.Boolean(object.finite) : false,
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = message.const;
        }
        if (message.lt !== undefined) {
            obj.lt = message.lt;
        }
        if (message.lte !== undefined) {
            obj.lte = message.lte;
        }
        if (message.gt !== undefined) {
            obj.gt = message.gt;
        }
        if (message.gte !== undefined) {
            obj.gte = message.gte;
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in;
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn;
        }
        if (message.finite !== undefined && message.finite !== false) {
            obj.finite = message.finite;
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example;
        }
        return obj;
    },
    create(base) {
        return FloatRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const message = createBaseFloatRules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.finite = (_h = object.finite) !== null && _h !== void 0 ? _h : false;
        message.example = ((_j = object.example) === null || _j === void 0 ? void 0 : _j.map((e) => e)) || [];
        return message;
    },
};
function createBaseDoubleRules() {
    return {
        const: 0,
        lt: undefined,
        lte: undefined,
        gt: undefined,
        gte: undefined,
        in: [],
        notIn: [],
        finite: false,
        example: [],
    };
}
export const DoubleRules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(9).double(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(17).double(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(25).double(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(33).double(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(41).double(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.double(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.double(v);
        }
        writer.join();
        if (message.finite !== undefined && message.finite !== false) {
            writer.uint32(64).bool(message.finite);
        }
        writer.uint32(74).fork();
        for (const v of message.example) {
            writer.double(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDoubleRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 9) {
                        break;
                    }
                    message.const = reader.double();
                    continue;
                }
                case 2: {
                    if (tag !== 17) {
                        break;
                    }
                    message.lt = reader.double();
                    continue;
                }
                case 3: {
                    if (tag !== 25) {
                        break;
                    }
                    message.lte = reader.double();
                    continue;
                }
                case 4: {
                    if (tag !== 33) {
                        break;
                    }
                    message.gt = reader.double();
                    continue;
                }
                case 5: {
                    if (tag !== 41) {
                        break;
                    }
                    message.gte = reader.double();
                    continue;
                }
                case 6: {
                    if (tag === 49) {
                        message.in.push(reader.double());
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(reader.double());
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 57) {
                        message.notIn.push(reader.double());
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(reader.double());
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag !== 64) {
                        break;
                    }
                    message.finite = reader.bool();
                    continue;
                }
                case 9: {
                    if (tag === 73) {
                        message.example.push(reader.double());
                        continue;
                    }
                    if (tag === 74) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(reader.double());
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            finite: isSet(object.finite) ? globalThis.Boolean(object.finite) : false,
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = message.const;
        }
        if (message.lt !== undefined) {
            obj.lt = message.lt;
        }
        if (message.lte !== undefined) {
            obj.lte = message.lte;
        }
        if (message.gt !== undefined) {
            obj.gt = message.gt;
        }
        if (message.gte !== undefined) {
            obj.gte = message.gte;
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in;
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn;
        }
        if (message.finite !== undefined && message.finite !== false) {
            obj.finite = message.finite;
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example;
        }
        return obj;
    },
    create(base) {
        return DoubleRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const message = createBaseDoubleRules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.finite = (_h = object.finite) !== null && _h !== void 0 ? _h : false;
        message.example = ((_j = object.example) === null || _j === void 0 ? void 0 : _j.map((e) => e)) || [];
        return message;
    },
};
function createBaseInt32Rules() {
    return { const: 0, lt: undefined, lte: undefined, gt: undefined, gte: undefined, in: [], notIn: [], example: [] };
}
export const Int32Rules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(8).int32(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(16).int32(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(24).int32(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(32).int32(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(40).int32(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.int32(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.int32(v);
        }
        writer.join();
        writer.uint32(66).fork();
        for (const v of message.example) {
            writer.int32(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseInt32Rules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.const = reader.int32();
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.lt = reader.int32();
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.lte = reader.int32();
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.gt = reader.int32();
                    continue;
                }
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.gte = reader.int32();
                    continue;
                }
                case 6: {
                    if (tag === 48) {
                        message.in.push(reader.int32());
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(reader.int32());
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 56) {
                        message.notIn.push(reader.int32());
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(reader.int32());
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag === 64) {
                        message.example.push(reader.int32());
                        continue;
                    }
                    if (tag === 66) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(reader.int32());
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Math.round(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Math.round(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Math.round(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Math.round(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return Int32Rules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseInt32Rules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseInt64Rules() {
    return { const: 0, lt: undefined, lte: undefined, gt: undefined, gte: undefined, in: [], notIn: [], example: [] };
}
export const Int64Rules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(8).int64(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(16).int64(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(24).int64(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(32).int64(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(40).int64(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.int64(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.int64(v);
        }
        writer.join();
        writer.uint32(74).fork();
        for (const v of message.example) {
            writer.int64(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseInt64Rules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.const = longToNumber(reader.int64());
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.lt = longToNumber(reader.int64());
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.lte = longToNumber(reader.int64());
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.gt = longToNumber(reader.int64());
                    continue;
                }
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.gte = longToNumber(reader.int64());
                    continue;
                }
                case 6: {
                    if (tag === 48) {
                        message.in.push(longToNumber(reader.int64()));
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(longToNumber(reader.int64()));
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 56) {
                        message.notIn.push(longToNumber(reader.int64()));
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(longToNumber(reader.int64()));
                        }
                        continue;
                    }
                    break;
                }
                case 9: {
                    if (tag === 72) {
                        message.example.push(longToNumber(reader.int64()));
                        continue;
                    }
                    if (tag === 74) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(longToNumber(reader.int64()));
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Math.round(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Math.round(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Math.round(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Math.round(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return Int64Rules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseInt64Rules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseUInt32Rules() {
    return { const: 0, lt: undefined, lte: undefined, gt: undefined, gte: undefined, in: [], notIn: [], example: [] };
}
export const UInt32Rules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(8).uint32(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(16).uint32(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(24).uint32(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(32).uint32(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(40).uint32(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.uint32(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.uint32(v);
        }
        writer.join();
        writer.uint32(66).fork();
        for (const v of message.example) {
            writer.uint32(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseUInt32Rules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.const = reader.uint32();
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.lt = reader.uint32();
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.lte = reader.uint32();
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.gt = reader.uint32();
                    continue;
                }
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.gte = reader.uint32();
                    continue;
                }
                case 6: {
                    if (tag === 48) {
                        message.in.push(reader.uint32());
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(reader.uint32());
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 56) {
                        message.notIn.push(reader.uint32());
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(reader.uint32());
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag === 64) {
                        message.example.push(reader.uint32());
                        continue;
                    }
                    if (tag === 66) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(reader.uint32());
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Math.round(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Math.round(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Math.round(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Math.round(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return UInt32Rules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseUInt32Rules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseUInt64Rules() {
    return { const: 0, lt: undefined, lte: undefined, gt: undefined, gte: undefined, in: [], notIn: [], example: [] };
}
export const UInt64Rules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(8).uint64(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(16).uint64(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(24).uint64(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(32).uint64(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(40).uint64(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.uint64(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.uint64(v);
        }
        writer.join();
        writer.uint32(66).fork();
        for (const v of message.example) {
            writer.uint64(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseUInt64Rules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.const = longToNumber(reader.uint64());
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.lt = longToNumber(reader.uint64());
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.lte = longToNumber(reader.uint64());
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.gt = longToNumber(reader.uint64());
                    continue;
                }
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.gte = longToNumber(reader.uint64());
                    continue;
                }
                case 6: {
                    if (tag === 48) {
                        message.in.push(longToNumber(reader.uint64()));
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(longToNumber(reader.uint64()));
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 56) {
                        message.notIn.push(longToNumber(reader.uint64()));
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(longToNumber(reader.uint64()));
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag === 64) {
                        message.example.push(longToNumber(reader.uint64()));
                        continue;
                    }
                    if (tag === 66) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(longToNumber(reader.uint64()));
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Math.round(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Math.round(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Math.round(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Math.round(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return UInt64Rules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseUInt64Rules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseSInt32Rules() {
    return { const: 0, lt: undefined, lte: undefined, gt: undefined, gte: undefined, in: [], notIn: [], example: [] };
}
export const SInt32Rules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(8).sint32(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(16).sint32(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(24).sint32(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(32).sint32(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(40).sint32(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.sint32(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.sint32(v);
        }
        writer.join();
        writer.uint32(66).fork();
        for (const v of message.example) {
            writer.sint32(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSInt32Rules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.const = reader.sint32();
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.lt = reader.sint32();
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.lte = reader.sint32();
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.gt = reader.sint32();
                    continue;
                }
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.gte = reader.sint32();
                    continue;
                }
                case 6: {
                    if (tag === 48) {
                        message.in.push(reader.sint32());
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(reader.sint32());
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 56) {
                        message.notIn.push(reader.sint32());
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(reader.sint32());
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag === 64) {
                        message.example.push(reader.sint32());
                        continue;
                    }
                    if (tag === 66) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(reader.sint32());
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Math.round(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Math.round(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Math.round(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Math.round(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return SInt32Rules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseSInt32Rules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseSInt64Rules() {
    return { const: 0, lt: undefined, lte: undefined, gt: undefined, gte: undefined, in: [], notIn: [], example: [] };
}
export const SInt64Rules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(8).sint64(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(16).sint64(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(24).sint64(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(32).sint64(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(40).sint64(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.sint64(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.sint64(v);
        }
        writer.join();
        writer.uint32(66).fork();
        for (const v of message.example) {
            writer.sint64(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSInt64Rules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.const = longToNumber(reader.sint64());
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.lt = longToNumber(reader.sint64());
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.lte = longToNumber(reader.sint64());
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.gt = longToNumber(reader.sint64());
                    continue;
                }
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.gte = longToNumber(reader.sint64());
                    continue;
                }
                case 6: {
                    if (tag === 48) {
                        message.in.push(longToNumber(reader.sint64()));
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(longToNumber(reader.sint64()));
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 56) {
                        message.notIn.push(longToNumber(reader.sint64()));
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(longToNumber(reader.sint64()));
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag === 64) {
                        message.example.push(longToNumber(reader.sint64()));
                        continue;
                    }
                    if (tag === 66) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(longToNumber(reader.sint64()));
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Math.round(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Math.round(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Math.round(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Math.round(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return SInt64Rules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseSInt64Rules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseFixed32Rules() {
    return { const: 0, lt: undefined, lte: undefined, gt: undefined, gte: undefined, in: [], notIn: [], example: [] };
}
export const Fixed32Rules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(13).fixed32(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(21).fixed32(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(29).fixed32(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(37).fixed32(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(45).fixed32(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.fixed32(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.fixed32(v);
        }
        writer.join();
        writer.uint32(66).fork();
        for (const v of message.example) {
            writer.fixed32(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseFixed32Rules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 13) {
                        break;
                    }
                    message.const = reader.fixed32();
                    continue;
                }
                case 2: {
                    if (tag !== 21) {
                        break;
                    }
                    message.lt = reader.fixed32();
                    continue;
                }
                case 3: {
                    if (tag !== 29) {
                        break;
                    }
                    message.lte = reader.fixed32();
                    continue;
                }
                case 4: {
                    if (tag !== 37) {
                        break;
                    }
                    message.gt = reader.fixed32();
                    continue;
                }
                case 5: {
                    if (tag !== 45) {
                        break;
                    }
                    message.gte = reader.fixed32();
                    continue;
                }
                case 6: {
                    if (tag === 53) {
                        message.in.push(reader.fixed32());
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(reader.fixed32());
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 61) {
                        message.notIn.push(reader.fixed32());
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(reader.fixed32());
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag === 69) {
                        message.example.push(reader.fixed32());
                        continue;
                    }
                    if (tag === 66) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(reader.fixed32());
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Math.round(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Math.round(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Math.round(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Math.round(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return Fixed32Rules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseFixed32Rules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseFixed64Rules() {
    return { const: 0, lt: undefined, lte: undefined, gt: undefined, gte: undefined, in: [], notIn: [], example: [] };
}
export const Fixed64Rules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(9).fixed64(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(17).fixed64(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(25).fixed64(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(33).fixed64(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(41).fixed64(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.fixed64(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.fixed64(v);
        }
        writer.join();
        writer.uint32(66).fork();
        for (const v of message.example) {
            writer.fixed64(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseFixed64Rules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 9) {
                        break;
                    }
                    message.const = longToNumber(reader.fixed64());
                    continue;
                }
                case 2: {
                    if (tag !== 17) {
                        break;
                    }
                    message.lt = longToNumber(reader.fixed64());
                    continue;
                }
                case 3: {
                    if (tag !== 25) {
                        break;
                    }
                    message.lte = longToNumber(reader.fixed64());
                    continue;
                }
                case 4: {
                    if (tag !== 33) {
                        break;
                    }
                    message.gt = longToNumber(reader.fixed64());
                    continue;
                }
                case 5: {
                    if (tag !== 41) {
                        break;
                    }
                    message.gte = longToNumber(reader.fixed64());
                    continue;
                }
                case 6: {
                    if (tag === 49) {
                        message.in.push(longToNumber(reader.fixed64()));
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(longToNumber(reader.fixed64()));
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 57) {
                        message.notIn.push(longToNumber(reader.fixed64()));
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(longToNumber(reader.fixed64()));
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag === 65) {
                        message.example.push(longToNumber(reader.fixed64()));
                        continue;
                    }
                    if (tag === 66) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(longToNumber(reader.fixed64()));
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Math.round(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Math.round(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Math.round(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Math.round(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return Fixed64Rules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseFixed64Rules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseSFixed32Rules() {
    return { const: 0, lt: undefined, lte: undefined, gt: undefined, gte: undefined, in: [], notIn: [], example: [] };
}
export const SFixed32Rules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(13).sfixed32(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(21).sfixed32(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(29).sfixed32(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(37).sfixed32(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(45).sfixed32(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.sfixed32(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.sfixed32(v);
        }
        writer.join();
        writer.uint32(66).fork();
        for (const v of message.example) {
            writer.sfixed32(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSFixed32Rules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 13) {
                        break;
                    }
                    message.const = reader.sfixed32();
                    continue;
                }
                case 2: {
                    if (tag !== 21) {
                        break;
                    }
                    message.lt = reader.sfixed32();
                    continue;
                }
                case 3: {
                    if (tag !== 29) {
                        break;
                    }
                    message.lte = reader.sfixed32();
                    continue;
                }
                case 4: {
                    if (tag !== 37) {
                        break;
                    }
                    message.gt = reader.sfixed32();
                    continue;
                }
                case 5: {
                    if (tag !== 45) {
                        break;
                    }
                    message.gte = reader.sfixed32();
                    continue;
                }
                case 6: {
                    if (tag === 53) {
                        message.in.push(reader.sfixed32());
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(reader.sfixed32());
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 61) {
                        message.notIn.push(reader.sfixed32());
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(reader.sfixed32());
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag === 69) {
                        message.example.push(reader.sfixed32());
                        continue;
                    }
                    if (tag === 66) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(reader.sfixed32());
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Math.round(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Math.round(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Math.round(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Math.round(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return SFixed32Rules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseSFixed32Rules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseSFixed64Rules() {
    return { const: 0, lt: undefined, lte: undefined, gt: undefined, gte: undefined, in: [], notIn: [], example: [] };
}
export const SFixed64Rules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(9).sfixed64(message.const);
        }
        if (message.lt !== undefined) {
            writer.uint32(17).sfixed64(message.lt);
        }
        if (message.lte !== undefined) {
            writer.uint32(25).sfixed64(message.lte);
        }
        if (message.gt !== undefined) {
            writer.uint32(33).sfixed64(message.gt);
        }
        if (message.gte !== undefined) {
            writer.uint32(41).sfixed64(message.gte);
        }
        writer.uint32(50).fork();
        for (const v of message.in) {
            writer.sfixed64(v);
        }
        writer.join();
        writer.uint32(58).fork();
        for (const v of message.notIn) {
            writer.sfixed64(v);
        }
        writer.join();
        writer.uint32(66).fork();
        for (const v of message.example) {
            writer.sfixed64(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSFixed64Rules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 9) {
                        break;
                    }
                    message.const = longToNumber(reader.sfixed64());
                    continue;
                }
                case 2: {
                    if (tag !== 17) {
                        break;
                    }
                    message.lt = longToNumber(reader.sfixed64());
                    continue;
                }
                case 3: {
                    if (tag !== 25) {
                        break;
                    }
                    message.lte = longToNumber(reader.sfixed64());
                    continue;
                }
                case 4: {
                    if (tag !== 33) {
                        break;
                    }
                    message.gt = longToNumber(reader.sfixed64());
                    continue;
                }
                case 5: {
                    if (tag !== 41) {
                        break;
                    }
                    message.gte = longToNumber(reader.sfixed64());
                    continue;
                }
                case 6: {
                    if (tag === 49) {
                        message.in.push(longToNumber(reader.sfixed64()));
                        continue;
                    }
                    if (tag === 50) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(longToNumber(reader.sfixed64()));
                        }
                        continue;
                    }
                    break;
                }
                case 7: {
                    if (tag === 57) {
                        message.notIn.push(longToNumber(reader.sfixed64()));
                        continue;
                    }
                    if (tag === 58) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(longToNumber(reader.sfixed64()));
                        }
                        continue;
                    }
                    break;
                }
                case 8: {
                    if (tag === 65) {
                        message.example.push(longToNumber(reader.sfixed64()));
                        continue;
                    }
                    if (tag === 66) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(longToNumber(reader.sfixed64()));
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            lt: isSet(object.lt) ? globalThis.Number(object.lt) : undefined,
            lte: isSet(object.lte) ? globalThis.Number(object.lte) : undefined,
            gt: isSet(object.gt) ? globalThis.Number(object.gt) : undefined,
            gte: isSet(object.gte) ? globalThis.Number(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Math.round(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Math.round(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Math.round(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Math.round(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return SFixed64Rules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseSFixed64Rules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.gt = (_d = object.gt) !== null && _d !== void 0 ? _d : undefined;
        message.gte = (_e = object.gte) !== null && _e !== void 0 ? _e : undefined;
        message.in = ((_f = object.in) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.notIn = ((_g = object.notIn) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseBoolRules() {
    return { const: false, example: [] };
}
export const BoolRules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== false) {
            writer.uint32(8).bool(message.const);
        }
        writer.uint32(18).fork();
        for (const v of message.example) {
            writer.bool(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBoolRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.const = reader.bool();
                    continue;
                }
                case 2: {
                    if (tag === 16) {
                        message.example.push(reader.bool());
                        continue;
                    }
                    if (tag === 18) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(reader.bool());
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Boolean(object.const) : false,
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Boolean(e)) : [],
        };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if (message.const !== undefined && message.const !== false) {
            obj.const = message.const;
        }
        if ((_a = message.example) === null || _a === void 0 ? void 0 : _a.length) {
            obj.example = message.example;
        }
        return obj;
    },
    create(base) {
        return BoolRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseBoolRules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : false;
        message.example = ((_b = object.example) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        return message;
    },
};
function createBaseStringRules() {
    return {
        const: "",
        len: 0,
        minLen: 0,
        maxLen: 0,
        lenBytes: 0,
        minBytes: 0,
        maxBytes: 0,
        pattern: "",
        prefix: "",
        suffix: "",
        contains: "",
        notContains: "",
        in: [],
        notIn: [],
        email: undefined,
        hostname: undefined,
        ip: undefined,
        ipv4: undefined,
        ipv6: undefined,
        uri: undefined,
        uriRef: undefined,
        address: undefined,
        uuid: undefined,
        tuuid: undefined,
        ipWithPrefixlen: undefined,
        ipv4WithPrefixlen: undefined,
        ipv6WithPrefixlen: undefined,
        ipPrefix: undefined,
        ipv4Prefix: undefined,
        ipv6Prefix: undefined,
        hostAndPort: undefined,
        wellKnownRegex: undefined,
        strict: false,
        example: [],
    };
}
export const StringRules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== "") {
            writer.uint32(10).string(message.const);
        }
        if (message.len !== undefined && message.len !== 0) {
            writer.uint32(152).uint64(message.len);
        }
        if (message.minLen !== undefined && message.minLen !== 0) {
            writer.uint32(16).uint64(message.minLen);
        }
        if (message.maxLen !== undefined && message.maxLen !== 0) {
            writer.uint32(24).uint64(message.maxLen);
        }
        if (message.lenBytes !== undefined && message.lenBytes !== 0) {
            writer.uint32(160).uint64(message.lenBytes);
        }
        if (message.minBytes !== undefined && message.minBytes !== 0) {
            writer.uint32(32).uint64(message.minBytes);
        }
        if (message.maxBytes !== undefined && message.maxBytes !== 0) {
            writer.uint32(40).uint64(message.maxBytes);
        }
        if (message.pattern !== undefined && message.pattern !== "") {
            writer.uint32(50).string(message.pattern);
        }
        if (message.prefix !== undefined && message.prefix !== "") {
            writer.uint32(58).string(message.prefix);
        }
        if (message.suffix !== undefined && message.suffix !== "") {
            writer.uint32(66).string(message.suffix);
        }
        if (message.contains !== undefined && message.contains !== "") {
            writer.uint32(74).string(message.contains);
        }
        if (message.notContains !== undefined && message.notContains !== "") {
            writer.uint32(186).string(message.notContains);
        }
        for (const v of message.in) {
            writer.uint32(82).string(v);
        }
        for (const v of message.notIn) {
            writer.uint32(90).string(v);
        }
        if (message.email !== undefined) {
            writer.uint32(96).bool(message.email);
        }
        if (message.hostname !== undefined) {
            writer.uint32(104).bool(message.hostname);
        }
        if (message.ip !== undefined) {
            writer.uint32(112).bool(message.ip);
        }
        if (message.ipv4 !== undefined) {
            writer.uint32(120).bool(message.ipv4);
        }
        if (message.ipv6 !== undefined) {
            writer.uint32(128).bool(message.ipv6);
        }
        if (message.uri !== undefined) {
            writer.uint32(136).bool(message.uri);
        }
        if (message.uriRef !== undefined) {
            writer.uint32(144).bool(message.uriRef);
        }
        if (message.address !== undefined) {
            writer.uint32(168).bool(message.address);
        }
        if (message.uuid !== undefined) {
            writer.uint32(176).bool(message.uuid);
        }
        if (message.tuuid !== undefined) {
            writer.uint32(264).bool(message.tuuid);
        }
        if (message.ipWithPrefixlen !== undefined) {
            writer.uint32(208).bool(message.ipWithPrefixlen);
        }
        if (message.ipv4WithPrefixlen !== undefined) {
            writer.uint32(216).bool(message.ipv4WithPrefixlen);
        }
        if (message.ipv6WithPrefixlen !== undefined) {
            writer.uint32(224).bool(message.ipv6WithPrefixlen);
        }
        if (message.ipPrefix !== undefined) {
            writer.uint32(232).bool(message.ipPrefix);
        }
        if (message.ipv4Prefix !== undefined) {
            writer.uint32(240).bool(message.ipv4Prefix);
        }
        if (message.ipv6Prefix !== undefined) {
            writer.uint32(248).bool(message.ipv6Prefix);
        }
        if (message.hostAndPort !== undefined) {
            writer.uint32(256).bool(message.hostAndPort);
        }
        if (message.wellKnownRegex !== undefined) {
            writer.uint32(192).int32(message.wellKnownRegex);
        }
        if (message.strict !== undefined && message.strict !== false) {
            writer.uint32(200).bool(message.strict);
        }
        for (const v of message.example) {
            writer.uint32(274).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseStringRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.const = reader.string();
                    continue;
                }
                case 19: {
                    if (tag !== 152) {
                        break;
                    }
                    message.len = longToNumber(reader.uint64());
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.minLen = longToNumber(reader.uint64());
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.maxLen = longToNumber(reader.uint64());
                    continue;
                }
                case 20: {
                    if (tag !== 160) {
                        break;
                    }
                    message.lenBytes = longToNumber(reader.uint64());
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.minBytes = longToNumber(reader.uint64());
                    continue;
                }
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.maxBytes = longToNumber(reader.uint64());
                    continue;
                }
                case 6: {
                    if (tag !== 50) {
                        break;
                    }
                    message.pattern = reader.string();
                    continue;
                }
                case 7: {
                    if (tag !== 58) {
                        break;
                    }
                    message.prefix = reader.string();
                    continue;
                }
                case 8: {
                    if (tag !== 66) {
                        break;
                    }
                    message.suffix = reader.string();
                    continue;
                }
                case 9: {
                    if (tag !== 74) {
                        break;
                    }
                    message.contains = reader.string();
                    continue;
                }
                case 23: {
                    if (tag !== 186) {
                        break;
                    }
                    message.notContains = reader.string();
                    continue;
                }
                case 10: {
                    if (tag !== 82) {
                        break;
                    }
                    message.in.push(reader.string());
                    continue;
                }
                case 11: {
                    if (tag !== 90) {
                        break;
                    }
                    message.notIn.push(reader.string());
                    continue;
                }
                case 12: {
                    if (tag !== 96) {
                        break;
                    }
                    message.email = reader.bool();
                    continue;
                }
                case 13: {
                    if (tag !== 104) {
                        break;
                    }
                    message.hostname = reader.bool();
                    continue;
                }
                case 14: {
                    if (tag !== 112) {
                        break;
                    }
                    message.ip = reader.bool();
                    continue;
                }
                case 15: {
                    if (tag !== 120) {
                        break;
                    }
                    message.ipv4 = reader.bool();
                    continue;
                }
                case 16: {
                    if (tag !== 128) {
                        break;
                    }
                    message.ipv6 = reader.bool();
                    continue;
                }
                case 17: {
                    if (tag !== 136) {
                        break;
                    }
                    message.uri = reader.bool();
                    continue;
                }
                case 18: {
                    if (tag !== 144) {
                        break;
                    }
                    message.uriRef = reader.bool();
                    continue;
                }
                case 21: {
                    if (tag !== 168) {
                        break;
                    }
                    message.address = reader.bool();
                    continue;
                }
                case 22: {
                    if (tag !== 176) {
                        break;
                    }
                    message.uuid = reader.bool();
                    continue;
                }
                case 33: {
                    if (tag !== 264) {
                        break;
                    }
                    message.tuuid = reader.bool();
                    continue;
                }
                case 26: {
                    if (tag !== 208) {
                        break;
                    }
                    message.ipWithPrefixlen = reader.bool();
                    continue;
                }
                case 27: {
                    if (tag !== 216) {
                        break;
                    }
                    message.ipv4WithPrefixlen = reader.bool();
                    continue;
                }
                case 28: {
                    if (tag !== 224) {
                        break;
                    }
                    message.ipv6WithPrefixlen = reader.bool();
                    continue;
                }
                case 29: {
                    if (tag !== 232) {
                        break;
                    }
                    message.ipPrefix = reader.bool();
                    continue;
                }
                case 30: {
                    if (tag !== 240) {
                        break;
                    }
                    message.ipv4Prefix = reader.bool();
                    continue;
                }
                case 31: {
                    if (tag !== 248) {
                        break;
                    }
                    message.ipv6Prefix = reader.bool();
                    continue;
                }
                case 32: {
                    if (tag !== 256) {
                        break;
                    }
                    message.hostAndPort = reader.bool();
                    continue;
                }
                case 24: {
                    if (tag !== 192) {
                        break;
                    }
                    message.wellKnownRegex = reader.int32();
                    continue;
                }
                case 25: {
                    if (tag !== 200) {
                        break;
                    }
                    message.strict = reader.bool();
                    continue;
                }
                case 34: {
                    if (tag !== 274) {
                        break;
                    }
                    message.example.push(reader.string());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.String(object.const) : "",
            len: isSet(object.len) ? globalThis.Number(object.len) : 0,
            minLen: isSet(object.minLen) ? globalThis.Number(object.minLen) : 0,
            maxLen: isSet(object.maxLen) ? globalThis.Number(object.maxLen) : 0,
            lenBytes: isSet(object.lenBytes) ? globalThis.Number(object.lenBytes) : 0,
            minBytes: isSet(object.minBytes) ? globalThis.Number(object.minBytes) : 0,
            maxBytes: isSet(object.maxBytes) ? globalThis.Number(object.maxBytes) : 0,
            pattern: isSet(object.pattern) ? globalThis.String(object.pattern) : "",
            prefix: isSet(object.prefix) ? globalThis.String(object.prefix) : "",
            suffix: isSet(object.suffix) ? globalThis.String(object.suffix) : "",
            contains: isSet(object.contains) ? globalThis.String(object.contains) : "",
            notContains: isSet(object.notContains) ? globalThis.String(object.notContains) : "",
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.String(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.String(e)) : [],
            email: isSet(object.email) ? globalThis.Boolean(object.email) : undefined,
            hostname: isSet(object.hostname) ? globalThis.Boolean(object.hostname) : undefined,
            ip: isSet(object.ip) ? globalThis.Boolean(object.ip) : undefined,
            ipv4: isSet(object.ipv4) ? globalThis.Boolean(object.ipv4) : undefined,
            ipv6: isSet(object.ipv6) ? globalThis.Boolean(object.ipv6) : undefined,
            uri: isSet(object.uri) ? globalThis.Boolean(object.uri) : undefined,
            uriRef: isSet(object.uriRef) ? globalThis.Boolean(object.uriRef) : undefined,
            address: isSet(object.address) ? globalThis.Boolean(object.address) : undefined,
            uuid: isSet(object.uuid) ? globalThis.Boolean(object.uuid) : undefined,
            tuuid: isSet(object.tuuid) ? globalThis.Boolean(object.tuuid) : undefined,
            ipWithPrefixlen: isSet(object.ipWithPrefixlen) ? globalThis.Boolean(object.ipWithPrefixlen) : undefined,
            ipv4WithPrefixlen: isSet(object.ipv4WithPrefixlen) ? globalThis.Boolean(object.ipv4WithPrefixlen) : undefined,
            ipv6WithPrefixlen: isSet(object.ipv6WithPrefixlen) ? globalThis.Boolean(object.ipv6WithPrefixlen) : undefined,
            ipPrefix: isSet(object.ipPrefix) ? globalThis.Boolean(object.ipPrefix) : undefined,
            ipv4Prefix: isSet(object.ipv4Prefix) ? globalThis.Boolean(object.ipv4Prefix) : undefined,
            ipv6Prefix: isSet(object.ipv6Prefix) ? globalThis.Boolean(object.ipv6Prefix) : undefined,
            hostAndPort: isSet(object.hostAndPort) ? globalThis.Boolean(object.hostAndPort) : undefined,
            wellKnownRegex: isSet(object.wellKnownRegex) ? knownRegexFromJSON(object.wellKnownRegex) : undefined,
            strict: isSet(object.strict) ? globalThis.Boolean(object.strict) : false,
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.String(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== "") {
            obj.const = message.const;
        }
        if (message.len !== undefined && message.len !== 0) {
            obj.len = Math.round(message.len);
        }
        if (message.minLen !== undefined && message.minLen !== 0) {
            obj.minLen = Math.round(message.minLen);
        }
        if (message.maxLen !== undefined && message.maxLen !== 0) {
            obj.maxLen = Math.round(message.maxLen);
        }
        if (message.lenBytes !== undefined && message.lenBytes !== 0) {
            obj.lenBytes = Math.round(message.lenBytes);
        }
        if (message.minBytes !== undefined && message.minBytes !== 0) {
            obj.minBytes = Math.round(message.minBytes);
        }
        if (message.maxBytes !== undefined && message.maxBytes !== 0) {
            obj.maxBytes = Math.round(message.maxBytes);
        }
        if (message.pattern !== undefined && message.pattern !== "") {
            obj.pattern = message.pattern;
        }
        if (message.prefix !== undefined && message.prefix !== "") {
            obj.prefix = message.prefix;
        }
        if (message.suffix !== undefined && message.suffix !== "") {
            obj.suffix = message.suffix;
        }
        if (message.contains !== undefined && message.contains !== "") {
            obj.contains = message.contains;
        }
        if (message.notContains !== undefined && message.notContains !== "") {
            obj.notContains = message.notContains;
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in;
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn;
        }
        if (message.email !== undefined) {
            obj.email = message.email;
        }
        if (message.hostname !== undefined) {
            obj.hostname = message.hostname;
        }
        if (message.ip !== undefined) {
            obj.ip = message.ip;
        }
        if (message.ipv4 !== undefined) {
            obj.ipv4 = message.ipv4;
        }
        if (message.ipv6 !== undefined) {
            obj.ipv6 = message.ipv6;
        }
        if (message.uri !== undefined) {
            obj.uri = message.uri;
        }
        if (message.uriRef !== undefined) {
            obj.uriRef = message.uriRef;
        }
        if (message.address !== undefined) {
            obj.address = message.address;
        }
        if (message.uuid !== undefined) {
            obj.uuid = message.uuid;
        }
        if (message.tuuid !== undefined) {
            obj.tuuid = message.tuuid;
        }
        if (message.ipWithPrefixlen !== undefined) {
            obj.ipWithPrefixlen = message.ipWithPrefixlen;
        }
        if (message.ipv4WithPrefixlen !== undefined) {
            obj.ipv4WithPrefixlen = message.ipv4WithPrefixlen;
        }
        if (message.ipv6WithPrefixlen !== undefined) {
            obj.ipv6WithPrefixlen = message.ipv6WithPrefixlen;
        }
        if (message.ipPrefix !== undefined) {
            obj.ipPrefix = message.ipPrefix;
        }
        if (message.ipv4Prefix !== undefined) {
            obj.ipv4Prefix = message.ipv4Prefix;
        }
        if (message.ipv6Prefix !== undefined) {
            obj.ipv6Prefix = message.ipv6Prefix;
        }
        if (message.hostAndPort !== undefined) {
            obj.hostAndPort = message.hostAndPort;
        }
        if (message.wellKnownRegex !== undefined) {
            obj.wellKnownRegex = knownRegexToJSON(message.wellKnownRegex);
        }
        if (message.strict !== undefined && message.strict !== false) {
            obj.strict = message.strict;
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example;
        }
        return obj;
    },
    create(base) {
        return StringRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9;
        const message = createBaseStringRules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : "";
        message.len = (_b = object.len) !== null && _b !== void 0 ? _b : 0;
        message.minLen = (_c = object.minLen) !== null && _c !== void 0 ? _c : 0;
        message.maxLen = (_d = object.maxLen) !== null && _d !== void 0 ? _d : 0;
        message.lenBytes = (_e = object.lenBytes) !== null && _e !== void 0 ? _e : 0;
        message.minBytes = (_f = object.minBytes) !== null && _f !== void 0 ? _f : 0;
        message.maxBytes = (_g = object.maxBytes) !== null && _g !== void 0 ? _g : 0;
        message.pattern = (_h = object.pattern) !== null && _h !== void 0 ? _h : "";
        message.prefix = (_j = object.prefix) !== null && _j !== void 0 ? _j : "";
        message.suffix = (_k = object.suffix) !== null && _k !== void 0 ? _k : "";
        message.contains = (_l = object.contains) !== null && _l !== void 0 ? _l : "";
        message.notContains = (_m = object.notContains) !== null && _m !== void 0 ? _m : "";
        message.in = ((_o = object.in) === null || _o === void 0 ? void 0 : _o.map((e) => e)) || [];
        message.notIn = ((_p = object.notIn) === null || _p === void 0 ? void 0 : _p.map((e) => e)) || [];
        message.email = (_q = object.email) !== null && _q !== void 0 ? _q : undefined;
        message.hostname = (_r = object.hostname) !== null && _r !== void 0 ? _r : undefined;
        message.ip = (_s = object.ip) !== null && _s !== void 0 ? _s : undefined;
        message.ipv4 = (_t = object.ipv4) !== null && _t !== void 0 ? _t : undefined;
        message.ipv6 = (_u = object.ipv6) !== null && _u !== void 0 ? _u : undefined;
        message.uri = (_v = object.uri) !== null && _v !== void 0 ? _v : undefined;
        message.uriRef = (_w = object.uriRef) !== null && _w !== void 0 ? _w : undefined;
        message.address = (_x = object.address) !== null && _x !== void 0 ? _x : undefined;
        message.uuid = (_y = object.uuid) !== null && _y !== void 0 ? _y : undefined;
        message.tuuid = (_z = object.tuuid) !== null && _z !== void 0 ? _z : undefined;
        message.ipWithPrefixlen = (_0 = object.ipWithPrefixlen) !== null && _0 !== void 0 ? _0 : undefined;
        message.ipv4WithPrefixlen = (_1 = object.ipv4WithPrefixlen) !== null && _1 !== void 0 ? _1 : undefined;
        message.ipv6WithPrefixlen = (_2 = object.ipv6WithPrefixlen) !== null && _2 !== void 0 ? _2 : undefined;
        message.ipPrefix = (_3 = object.ipPrefix) !== null && _3 !== void 0 ? _3 : undefined;
        message.ipv4Prefix = (_4 = object.ipv4Prefix) !== null && _4 !== void 0 ? _4 : undefined;
        message.ipv6Prefix = (_5 = object.ipv6Prefix) !== null && _5 !== void 0 ? _5 : undefined;
        message.hostAndPort = (_6 = object.hostAndPort) !== null && _6 !== void 0 ? _6 : undefined;
        message.wellKnownRegex = (_7 = object.wellKnownRegex) !== null && _7 !== void 0 ? _7 : undefined;
        message.strict = (_8 = object.strict) !== null && _8 !== void 0 ? _8 : false;
        message.example = ((_9 = object.example) === null || _9 === void 0 ? void 0 : _9.map((e) => e)) || [];
        return message;
    },
};
function createBaseBytesRules() {
    return {
        const: new Uint8Array(0),
        len: 0,
        minLen: 0,
        maxLen: 0,
        pattern: "",
        prefix: new Uint8Array(0),
        suffix: new Uint8Array(0),
        contains: new Uint8Array(0),
        in: [],
        notIn: [],
        ip: undefined,
        ipv4: undefined,
        ipv6: undefined,
        example: [],
    };
}
export const BytesRules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const.length !== 0) {
            writer.uint32(10).bytes(message.const);
        }
        if (message.len !== undefined && message.len !== 0) {
            writer.uint32(104).uint64(message.len);
        }
        if (message.minLen !== undefined && message.minLen !== 0) {
            writer.uint32(16).uint64(message.minLen);
        }
        if (message.maxLen !== undefined && message.maxLen !== 0) {
            writer.uint32(24).uint64(message.maxLen);
        }
        if (message.pattern !== undefined && message.pattern !== "") {
            writer.uint32(34).string(message.pattern);
        }
        if (message.prefix !== undefined && message.prefix.length !== 0) {
            writer.uint32(42).bytes(message.prefix);
        }
        if (message.suffix !== undefined && message.suffix.length !== 0) {
            writer.uint32(50).bytes(message.suffix);
        }
        if (message.contains !== undefined && message.contains.length !== 0) {
            writer.uint32(58).bytes(message.contains);
        }
        for (const v of message.in) {
            writer.uint32(66).bytes(v);
        }
        for (const v of message.notIn) {
            writer.uint32(74).bytes(v);
        }
        if (message.ip !== undefined) {
            writer.uint32(80).bool(message.ip);
        }
        if (message.ipv4 !== undefined) {
            writer.uint32(88).bool(message.ipv4);
        }
        if (message.ipv6 !== undefined) {
            writer.uint32(96).bool(message.ipv6);
        }
        for (const v of message.example) {
            writer.uint32(114).bytes(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBytesRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.const = reader.bytes();
                    continue;
                }
                case 13: {
                    if (tag !== 104) {
                        break;
                    }
                    message.len = longToNumber(reader.uint64());
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.minLen = longToNumber(reader.uint64());
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.maxLen = longToNumber(reader.uint64());
                    continue;
                }
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.pattern = reader.string();
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.prefix = reader.bytes();
                    continue;
                }
                case 6: {
                    if (tag !== 50) {
                        break;
                    }
                    message.suffix = reader.bytes();
                    continue;
                }
                case 7: {
                    if (tag !== 58) {
                        break;
                    }
                    message.contains = reader.bytes();
                    continue;
                }
                case 8: {
                    if (tag !== 66) {
                        break;
                    }
                    message.in.push(reader.bytes());
                    continue;
                }
                case 9: {
                    if (tag !== 74) {
                        break;
                    }
                    message.notIn.push(reader.bytes());
                    continue;
                }
                case 10: {
                    if (tag !== 80) {
                        break;
                    }
                    message.ip = reader.bool();
                    continue;
                }
                case 11: {
                    if (tag !== 88) {
                        break;
                    }
                    message.ipv4 = reader.bool();
                    continue;
                }
                case 12: {
                    if (tag !== 96) {
                        break;
                    }
                    message.ipv6 = reader.bool();
                    continue;
                }
                case 14: {
                    if (tag !== 114) {
                        break;
                    }
                    message.example.push(reader.bytes());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? bytesFromBase64(object.const) : new Uint8Array(0),
            len: isSet(object.len) ? globalThis.Number(object.len) : 0,
            minLen: isSet(object.minLen) ? globalThis.Number(object.minLen) : 0,
            maxLen: isSet(object.maxLen) ? globalThis.Number(object.maxLen) : 0,
            pattern: isSet(object.pattern) ? globalThis.String(object.pattern) : "",
            prefix: isSet(object.prefix) ? bytesFromBase64(object.prefix) : new Uint8Array(0),
            suffix: isSet(object.suffix) ? bytesFromBase64(object.suffix) : new Uint8Array(0),
            contains: isSet(object.contains) ? bytesFromBase64(object.contains) : new Uint8Array(0),
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => bytesFromBase64(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => bytesFromBase64(e)) : [],
            ip: isSet(object.ip) ? globalThis.Boolean(object.ip) : undefined,
            ipv4: isSet(object.ipv4) ? globalThis.Boolean(object.ipv4) : undefined,
            ipv6: isSet(object.ipv6) ? globalThis.Boolean(object.ipv6) : undefined,
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => bytesFromBase64(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const.length !== 0) {
            obj.const = base64FromBytes(message.const);
        }
        if (message.len !== undefined && message.len !== 0) {
            obj.len = Math.round(message.len);
        }
        if (message.minLen !== undefined && message.minLen !== 0) {
            obj.minLen = Math.round(message.minLen);
        }
        if (message.maxLen !== undefined && message.maxLen !== 0) {
            obj.maxLen = Math.round(message.maxLen);
        }
        if (message.pattern !== undefined && message.pattern !== "") {
            obj.pattern = message.pattern;
        }
        if (message.prefix !== undefined && message.prefix.length !== 0) {
            obj.prefix = base64FromBytes(message.prefix);
        }
        if (message.suffix !== undefined && message.suffix.length !== 0) {
            obj.suffix = base64FromBytes(message.suffix);
        }
        if (message.contains !== undefined && message.contains.length !== 0) {
            obj.contains = base64FromBytes(message.contains);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => base64FromBytes(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => base64FromBytes(e));
        }
        if (message.ip !== undefined) {
            obj.ip = message.ip;
        }
        if (message.ipv4 !== undefined) {
            obj.ipv4 = message.ipv4;
        }
        if (message.ipv6 !== undefined) {
            obj.ipv6 = message.ipv6;
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => base64FromBytes(e));
        }
        return obj;
    },
    create(base) {
        return BytesRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;
        const message = createBaseBytesRules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : new Uint8Array(0);
        message.len = (_b = object.len) !== null && _b !== void 0 ? _b : 0;
        message.minLen = (_c = object.minLen) !== null && _c !== void 0 ? _c : 0;
        message.maxLen = (_d = object.maxLen) !== null && _d !== void 0 ? _d : 0;
        message.pattern = (_e = object.pattern) !== null && _e !== void 0 ? _e : "";
        message.prefix = (_f = object.prefix) !== null && _f !== void 0 ? _f : new Uint8Array(0);
        message.suffix = (_g = object.suffix) !== null && _g !== void 0 ? _g : new Uint8Array(0);
        message.contains = (_h = object.contains) !== null && _h !== void 0 ? _h : new Uint8Array(0);
        message.in = ((_j = object.in) === null || _j === void 0 ? void 0 : _j.map((e) => e)) || [];
        message.notIn = ((_k = object.notIn) === null || _k === void 0 ? void 0 : _k.map((e) => e)) || [];
        message.ip = (_l = object.ip) !== null && _l !== void 0 ? _l : undefined;
        message.ipv4 = (_m = object.ipv4) !== null && _m !== void 0 ? _m : undefined;
        message.ipv6 = (_o = object.ipv6) !== null && _o !== void 0 ? _o : undefined;
        message.example = ((_p = object.example) === null || _p === void 0 ? void 0 : _p.map((e) => e)) || [];
        return message;
    },
};
function createBaseEnumRules() {
    return { const: 0, definedOnly: false, in: [], notIn: [], example: [] };
}
export const EnumRules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined && message.const !== 0) {
            writer.uint32(8).int32(message.const);
        }
        if (message.definedOnly !== undefined && message.definedOnly !== false) {
            writer.uint32(16).bool(message.definedOnly);
        }
        writer.uint32(26).fork();
        for (const v of message.in) {
            writer.int32(v);
        }
        writer.join();
        writer.uint32(34).fork();
        for (const v of message.notIn) {
            writer.int32(v);
        }
        writer.join();
        writer.uint32(42).fork();
        for (const v of message.example) {
            writer.int32(v);
        }
        writer.join();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEnumRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.const = reader.int32();
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.definedOnly = reader.bool();
                    continue;
                }
                case 3: {
                    if (tag === 24) {
                        message.in.push(reader.int32());
                        continue;
                    }
                    if (tag === 26) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.in.push(reader.int32());
                        }
                        continue;
                    }
                    break;
                }
                case 4: {
                    if (tag === 32) {
                        message.notIn.push(reader.int32());
                        continue;
                    }
                    if (tag === 34) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.notIn.push(reader.int32());
                        }
                        continue;
                    }
                    break;
                }
                case 5: {
                    if (tag === 40) {
                        message.example.push(reader.int32());
                        continue;
                    }
                    if (tag === 42) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.example.push(reader.int32());
                        }
                        continue;
                    }
                    break;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? globalThis.Number(object.const) : 0,
            definedOnly: isSet(object.definedOnly) ? globalThis.Boolean(object.definedOnly) : false,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.Number(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.Number(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => globalThis.Number(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined && message.const !== 0) {
            obj.const = Math.round(message.const);
        }
        if (message.definedOnly !== undefined && message.definedOnly !== false) {
            obj.definedOnly = message.definedOnly;
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Math.round(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Math.round(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Math.round(e));
        }
        return obj;
    },
    create(base) {
        return EnumRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBaseEnumRules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : 0;
        message.definedOnly = (_b = object.definedOnly) !== null && _b !== void 0 ? _b : false;
        message.in = ((_c = object.in) === null || _c === void 0 ? void 0 : _c.map((e) => e)) || [];
        message.notIn = ((_d = object.notIn) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
        message.example = ((_e = object.example) === null || _e === void 0 ? void 0 : _e.map((e) => e)) || [];
        return message;
    },
};
function createBaseRepeatedRules() {
    return { minItems: 0, maxItems: 0, unique: false, items: undefined };
}
export const RepeatedRules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.minItems !== undefined && message.minItems !== 0) {
            writer.uint32(8).uint64(message.minItems);
        }
        if (message.maxItems !== undefined && message.maxItems !== 0) {
            writer.uint32(16).uint64(message.maxItems);
        }
        if (message.unique !== undefined && message.unique !== false) {
            writer.uint32(24).bool(message.unique);
        }
        if (message.items !== undefined) {
            FieldConstraints.encode(message.items, writer.uint32(34).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRepeatedRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.minItems = longToNumber(reader.uint64());
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.maxItems = longToNumber(reader.uint64());
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.unique = reader.bool();
                    continue;
                }
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.items = FieldConstraints.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            minItems: isSet(object.minItems) ? globalThis.Number(object.minItems) : 0,
            maxItems: isSet(object.maxItems) ? globalThis.Number(object.maxItems) : 0,
            unique: isSet(object.unique) ? globalThis.Boolean(object.unique) : false,
            items: isSet(object.items) ? FieldConstraints.fromJSON(object.items) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.minItems !== undefined && message.minItems !== 0) {
            obj.minItems = Math.round(message.minItems);
        }
        if (message.maxItems !== undefined && message.maxItems !== 0) {
            obj.maxItems = Math.round(message.maxItems);
        }
        if (message.unique !== undefined && message.unique !== false) {
            obj.unique = message.unique;
        }
        if (message.items !== undefined) {
            obj.items = FieldConstraints.toJSON(message.items);
        }
        return obj;
    },
    create(base) {
        return RepeatedRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseRepeatedRules();
        message.minItems = (_a = object.minItems) !== null && _a !== void 0 ? _a : 0;
        message.maxItems = (_b = object.maxItems) !== null && _b !== void 0 ? _b : 0;
        message.unique = (_c = object.unique) !== null && _c !== void 0 ? _c : false;
        message.items = (object.items !== undefined && object.items !== null)
            ? FieldConstraints.fromPartial(object.items)
            : undefined;
        return message;
    },
};
function createBaseMapRules() {
    return { minPairs: 0, maxPairs: 0, keys: undefined, values: undefined };
}
export const MapRules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.minPairs !== undefined && message.minPairs !== 0) {
            writer.uint32(8).uint64(message.minPairs);
        }
        if (message.maxPairs !== undefined && message.maxPairs !== 0) {
            writer.uint32(16).uint64(message.maxPairs);
        }
        if (message.keys !== undefined) {
            FieldConstraints.encode(message.keys, writer.uint32(34).fork()).join();
        }
        if (message.values !== undefined) {
            FieldConstraints.encode(message.values, writer.uint32(42).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMapRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.minPairs = longToNumber(reader.uint64());
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.maxPairs = longToNumber(reader.uint64());
                    continue;
                }
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.keys = FieldConstraints.decode(reader, reader.uint32());
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.values = FieldConstraints.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            minPairs: isSet(object.minPairs) ? globalThis.Number(object.minPairs) : 0,
            maxPairs: isSet(object.maxPairs) ? globalThis.Number(object.maxPairs) : 0,
            keys: isSet(object.keys) ? FieldConstraints.fromJSON(object.keys) : undefined,
            values: isSet(object.values) ? FieldConstraints.fromJSON(object.values) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.minPairs !== undefined && message.minPairs !== 0) {
            obj.minPairs = Math.round(message.minPairs);
        }
        if (message.maxPairs !== undefined && message.maxPairs !== 0) {
            obj.maxPairs = Math.round(message.maxPairs);
        }
        if (message.keys !== undefined) {
            obj.keys = FieldConstraints.toJSON(message.keys);
        }
        if (message.values !== undefined) {
            obj.values = FieldConstraints.toJSON(message.values);
        }
        return obj;
    },
    create(base) {
        return MapRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseMapRules();
        message.minPairs = (_a = object.minPairs) !== null && _a !== void 0 ? _a : 0;
        message.maxPairs = (_b = object.maxPairs) !== null && _b !== void 0 ? _b : 0;
        message.keys = (object.keys !== undefined && object.keys !== null)
            ? FieldConstraints.fromPartial(object.keys)
            : undefined;
        message.values = (object.values !== undefined && object.values !== null)
            ? FieldConstraints.fromPartial(object.values)
            : undefined;
        return message;
    },
};
function createBaseAnyRules() {
    return { in: [], notIn: [] };
}
export const AnyRules = {
    encode(message, writer = new BinaryWriter()) {
        for (const v of message.in) {
            writer.uint32(18).string(v);
        }
        for (const v of message.notIn) {
            writer.uint32(26).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAnyRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.in.push(reader.string());
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.notIn.push(reader.string());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => globalThis.String(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => globalThis.String(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b;
        const obj = {};
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in;
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn;
        }
        return obj;
    },
    create(base) {
        return AnyRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseAnyRules();
        message.in = ((_a = object.in) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
        message.notIn = ((_b = object.notIn) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        return message;
    },
};
function createBaseDurationRules() {
    return {
        const: undefined,
        lt: undefined,
        lte: undefined,
        gt: undefined,
        gte: undefined,
        in: [],
        notIn: [],
        example: [],
    };
}
export const DurationRules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined) {
            Duration.encode(message.const, writer.uint32(18).fork()).join();
        }
        if (message.lt !== undefined) {
            Duration.encode(message.lt, writer.uint32(26).fork()).join();
        }
        if (message.lte !== undefined) {
            Duration.encode(message.lte, writer.uint32(34).fork()).join();
        }
        if (message.gt !== undefined) {
            Duration.encode(message.gt, writer.uint32(42).fork()).join();
        }
        if (message.gte !== undefined) {
            Duration.encode(message.gte, writer.uint32(50).fork()).join();
        }
        for (const v of message.in) {
            Duration.encode(v, writer.uint32(58).fork()).join();
        }
        for (const v of message.notIn) {
            Duration.encode(v, writer.uint32(66).fork()).join();
        }
        for (const v of message.example) {
            Duration.encode(v, writer.uint32(74).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDurationRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.const = Duration.decode(reader, reader.uint32());
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.lt = Duration.decode(reader, reader.uint32());
                    continue;
                }
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.lte = Duration.decode(reader, reader.uint32());
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.gt = Duration.decode(reader, reader.uint32());
                    continue;
                }
                case 6: {
                    if (tag !== 50) {
                        break;
                    }
                    message.gte = Duration.decode(reader, reader.uint32());
                    continue;
                }
                case 7: {
                    if (tag !== 58) {
                        break;
                    }
                    message.in.push(Duration.decode(reader, reader.uint32()));
                    continue;
                }
                case 8: {
                    if (tag !== 66) {
                        break;
                    }
                    message.notIn.push(Duration.decode(reader, reader.uint32()));
                    continue;
                }
                case 9: {
                    if (tag !== 74) {
                        break;
                    }
                    message.example.push(Duration.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? Duration.fromJSON(object.const) : undefined,
            lt: isSet(object.lt) ? Duration.fromJSON(object.lt) : undefined,
            lte: isSet(object.lte) ? Duration.fromJSON(object.lte) : undefined,
            gt: isSet(object.gt) ? Duration.fromJSON(object.gt) : undefined,
            gte: isSet(object.gte) ? Duration.fromJSON(object.gte) : undefined,
            in: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.in) ? object.in.map((e) => Duration.fromJSON(e)) : [],
            notIn: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.notIn) ? object.notIn.map((e) => Duration.fromJSON(e)) : [],
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => Duration.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        var _a, _b, _c;
        const obj = {};
        if (message.const !== undefined) {
            obj.const = Duration.toJSON(message.const);
        }
        if (message.lt !== undefined) {
            obj.lt = Duration.toJSON(message.lt);
        }
        if (message.lte !== undefined) {
            obj.lte = Duration.toJSON(message.lte);
        }
        if (message.gt !== undefined) {
            obj.gt = Duration.toJSON(message.gt);
        }
        if (message.gte !== undefined) {
            obj.gte = Duration.toJSON(message.gte);
        }
        if ((_a = message.in) === null || _a === void 0 ? void 0 : _a.length) {
            obj.in = message.in.map((e) => Duration.toJSON(e));
        }
        if ((_b = message.notIn) === null || _b === void 0 ? void 0 : _b.length) {
            obj.notIn = message.notIn.map((e) => Duration.toJSON(e));
        }
        if ((_c = message.example) === null || _c === void 0 ? void 0 : _c.length) {
            obj.example = message.example.map((e) => Duration.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return DurationRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseDurationRules();
        message.const = (object.const !== undefined && object.const !== null)
            ? Duration.fromPartial(object.const)
            : undefined;
        message.lt = (object.lt !== undefined && object.lt !== null) ? Duration.fromPartial(object.lt) : undefined;
        message.lte = (object.lte !== undefined && object.lte !== null) ? Duration.fromPartial(object.lte) : undefined;
        message.gt = (object.gt !== undefined && object.gt !== null) ? Duration.fromPartial(object.gt) : undefined;
        message.gte = (object.gte !== undefined && object.gte !== null) ? Duration.fromPartial(object.gte) : undefined;
        message.in = ((_a = object.in) === null || _a === void 0 ? void 0 : _a.map((e) => Duration.fromPartial(e))) || [];
        message.notIn = ((_b = object.notIn) === null || _b === void 0 ? void 0 : _b.map((e) => Duration.fromPartial(e))) || [];
        message.example = ((_c = object.example) === null || _c === void 0 ? void 0 : _c.map((e) => Duration.fromPartial(e))) || [];
        return message;
    },
};
function createBaseTimestampRules() {
    return {
        const: undefined,
        lt: undefined,
        lte: undefined,
        ltNow: undefined,
        gt: undefined,
        gte: undefined,
        gtNow: undefined,
        within: undefined,
        example: [],
    };
}
export const TimestampRules = {
    encode(message, writer = new BinaryWriter()) {
        if (message.const !== undefined) {
            Timestamp.encode(toTimestamp(message.const), writer.uint32(18).fork()).join();
        }
        if (message.lt !== undefined) {
            Timestamp.encode(toTimestamp(message.lt), writer.uint32(26).fork()).join();
        }
        if (message.lte !== undefined) {
            Timestamp.encode(toTimestamp(message.lte), writer.uint32(34).fork()).join();
        }
        if (message.ltNow !== undefined) {
            writer.uint32(56).bool(message.ltNow);
        }
        if (message.gt !== undefined) {
            Timestamp.encode(toTimestamp(message.gt), writer.uint32(42).fork()).join();
        }
        if (message.gte !== undefined) {
            Timestamp.encode(toTimestamp(message.gte), writer.uint32(50).fork()).join();
        }
        if (message.gtNow !== undefined) {
            writer.uint32(64).bool(message.gtNow);
        }
        if (message.within !== undefined) {
            Duration.encode(message.within, writer.uint32(74).fork()).join();
        }
        for (const v of message.example) {
            Timestamp.encode(toTimestamp(v), writer.uint32(82).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTimestampRules();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.const = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.lt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
                    continue;
                }
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.lte = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
                    continue;
                }
                case 7: {
                    if (tag !== 56) {
                        break;
                    }
                    message.ltNow = reader.bool();
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.gt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
                    continue;
                }
                case 6: {
                    if (tag !== 50) {
                        break;
                    }
                    message.gte = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
                    continue;
                }
                case 8: {
                    if (tag !== 64) {
                        break;
                    }
                    message.gtNow = reader.bool();
                    continue;
                }
                case 9: {
                    if (tag !== 74) {
                        break;
                    }
                    message.within = Duration.decode(reader, reader.uint32());
                    continue;
                }
                case 10: {
                    if (tag !== 82) {
                        break;
                    }
                    message.example.push(fromTimestamp(Timestamp.decode(reader, reader.uint32())));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            const: isSet(object.const) ? fromJsonTimestamp(object.const) : undefined,
            lt: isSet(object.lt) ? fromJsonTimestamp(object.lt) : undefined,
            lte: isSet(object.lte) ? fromJsonTimestamp(object.lte) : undefined,
            ltNow: isSet(object.ltNow) ? globalThis.Boolean(object.ltNow) : undefined,
            gt: isSet(object.gt) ? fromJsonTimestamp(object.gt) : undefined,
            gte: isSet(object.gte) ? fromJsonTimestamp(object.gte) : undefined,
            gtNow: isSet(object.gtNow) ? globalThis.Boolean(object.gtNow) : undefined,
            within: isSet(object.within) ? Duration.fromJSON(object.within) : undefined,
            example: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.example) ? object.example.map((e) => fromJsonTimestamp(e)) : [],
        };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if (message.const !== undefined) {
            obj.const = message.const.toISOString();
        }
        if (message.lt !== undefined) {
            obj.lt = message.lt.toISOString();
        }
        if (message.lte !== undefined) {
            obj.lte = message.lte.toISOString();
        }
        if (message.ltNow !== undefined) {
            obj.ltNow = message.ltNow;
        }
        if (message.gt !== undefined) {
            obj.gt = message.gt.toISOString();
        }
        if (message.gte !== undefined) {
            obj.gte = message.gte.toISOString();
        }
        if (message.gtNow !== undefined) {
            obj.gtNow = message.gtNow;
        }
        if (message.within !== undefined) {
            obj.within = Duration.toJSON(message.within);
        }
        if ((_a = message.example) === null || _a === void 0 ? void 0 : _a.length) {
            obj.example = message.example.map((e) => e.toISOString());
        }
        return obj;
    },
    create(base) {
        return TimestampRules.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseTimestampRules();
        message.const = (_a = object.const) !== null && _a !== void 0 ? _a : undefined;
        message.lt = (_b = object.lt) !== null && _b !== void 0 ? _b : undefined;
        message.lte = (_c = object.lte) !== null && _c !== void 0 ? _c : undefined;
        message.ltNow = (_d = object.ltNow) !== null && _d !== void 0 ? _d : undefined;
        message.gt = (_e = object.gt) !== null && _e !== void 0 ? _e : undefined;
        message.gte = (_f = object.gte) !== null && _f !== void 0 ? _f : undefined;
        message.gtNow = (_g = object.gtNow) !== null && _g !== void 0 ? _g : undefined;
        message.within = (object.within !== undefined && object.within !== null)
            ? Duration.fromPartial(object.within)
            : undefined;
        message.example = ((_h = object.example) === null || _h === void 0 ? void 0 : _h.map((e) => e)) || [];
        return message;
    },
};
function createBaseViolations() {
    return { violations: [] };
}
export const Violations = {
    encode(message, writer = new BinaryWriter()) {
        for (const v of message.violations) {
            Violation.encode(v, writer.uint32(10).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseViolations();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.violations.push(Violation.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            violations: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.violations)
                ? object.violations.map((e) => Violation.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if ((_a = message.violations) === null || _a === void 0 ? void 0 : _a.length) {
            obj.violations = message.violations.map((e) => Violation.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return Violations.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseViolations();
        message.violations = ((_a = object.violations) === null || _a === void 0 ? void 0 : _a.map((e) => Violation.fromPartial(e))) || [];
        return message;
    },
};
function createBaseViolation() {
    return { field: undefined, rule: undefined, fieldPath: "", constraintId: "", message: "", forKey: false };
}
export const Violation = {
    encode(message, writer = new BinaryWriter()) {
        if (message.field !== undefined) {
            FieldPath.encode(message.field, writer.uint32(42).fork()).join();
        }
        if (message.rule !== undefined) {
            FieldPath.encode(message.rule, writer.uint32(50).fork()).join();
        }
        if (message.fieldPath !== undefined && message.fieldPath !== "") {
            writer.uint32(10).string(message.fieldPath);
        }
        if (message.constraintId !== undefined && message.constraintId !== "") {
            writer.uint32(18).string(message.constraintId);
        }
        if (message.message !== undefined && message.message !== "") {
            writer.uint32(26).string(message.message);
        }
        if (message.forKey !== undefined && message.forKey !== false) {
            writer.uint32(32).bool(message.forKey);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseViolation();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.field = FieldPath.decode(reader, reader.uint32());
                    continue;
                }
                case 6: {
                    if (tag !== 50) {
                        break;
                    }
                    message.rule = FieldPath.decode(reader, reader.uint32());
                    continue;
                }
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.fieldPath = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.constraintId = reader.string();
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.message = reader.string();
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.forKey = reader.bool();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            field: isSet(object.field) ? FieldPath.fromJSON(object.field) : undefined,
            rule: isSet(object.rule) ? FieldPath.fromJSON(object.rule) : undefined,
            fieldPath: isSet(object.fieldPath) ? globalThis.String(object.fieldPath) : "",
            constraintId: isSet(object.constraintId) ? globalThis.String(object.constraintId) : "",
            message: isSet(object.message) ? globalThis.String(object.message) : "",
            forKey: isSet(object.forKey) ? globalThis.Boolean(object.forKey) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.field !== undefined) {
            obj.field = FieldPath.toJSON(message.field);
        }
        if (message.rule !== undefined) {
            obj.rule = FieldPath.toJSON(message.rule);
        }
        if (message.fieldPath !== undefined && message.fieldPath !== "") {
            obj.fieldPath = message.fieldPath;
        }
        if (message.constraintId !== undefined && message.constraintId !== "") {
            obj.constraintId = message.constraintId;
        }
        if (message.message !== undefined && message.message !== "") {
            obj.message = message.message;
        }
        if (message.forKey !== undefined && message.forKey !== false) {
            obj.forKey = message.forKey;
        }
        return obj;
    },
    create(base) {
        return Violation.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseViolation();
        message.field = (object.field !== undefined && object.field !== null)
            ? FieldPath.fromPartial(object.field)
            : undefined;
        message.rule = (object.rule !== undefined && object.rule !== null) ? FieldPath.fromPartial(object.rule) : undefined;
        message.fieldPath = (_a = object.fieldPath) !== null && _a !== void 0 ? _a : "";
        message.constraintId = (_b = object.constraintId) !== null && _b !== void 0 ? _b : "";
        message.message = (_c = object.message) !== null && _c !== void 0 ? _c : "";
        message.forKey = (_d = object.forKey) !== null && _d !== void 0 ? _d : false;
        return message;
    },
};
function createBaseFieldPath() {
    return { elements: [] };
}
export const FieldPath = {
    encode(message, writer = new BinaryWriter()) {
        for (const v of message.elements) {
            FieldPathElement.encode(v, writer.uint32(10).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseFieldPath();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.elements.push(FieldPathElement.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            elements: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.elements)
                ? object.elements.map((e) => FieldPathElement.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if ((_a = message.elements) === null || _a === void 0 ? void 0 : _a.length) {
            obj.elements = message.elements.map((e) => FieldPathElement.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return FieldPath.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseFieldPath();
        message.elements = ((_a = object.elements) === null || _a === void 0 ? void 0 : _a.map((e) => FieldPathElement.fromPartial(e))) || [];
        return message;
    },
};
function createBaseFieldPathElement() {
    return {
        fieldNumber: 0,
        fieldName: "",
        fieldType: 1,
        keyType: 1,
        valueType: 1,
        index: undefined,
        boolKey: undefined,
        intKey: undefined,
        uintKey: undefined,
        stringKey: undefined,
    };
}
export const FieldPathElement = {
    encode(message, writer = new BinaryWriter()) {
        if (message.fieldNumber !== undefined && message.fieldNumber !== 0) {
            writer.uint32(8).int32(message.fieldNumber);
        }
        if (message.fieldName !== undefined && message.fieldName !== "") {
            writer.uint32(18).string(message.fieldName);
        }
        if (message.fieldType !== undefined && message.fieldType !== 1) {
            writer.uint32(24).int32(message.fieldType);
        }
        if (message.keyType !== undefined && message.keyType !== 1) {
            writer.uint32(32).int32(message.keyType);
        }
        if (message.valueType !== undefined && message.valueType !== 1) {
            writer.uint32(40).int32(message.valueType);
        }
        if (message.index !== undefined) {
            writer.uint32(48).uint64(message.index);
        }
        if (message.boolKey !== undefined) {
            writer.uint32(56).bool(message.boolKey);
        }
        if (message.intKey !== undefined) {
            writer.uint32(64).int64(message.intKey);
        }
        if (message.uintKey !== undefined) {
            writer.uint32(72).uint64(message.uintKey);
        }
        if (message.stringKey !== undefined) {
            writer.uint32(82).string(message.stringKey);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseFieldPathElement();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.fieldNumber = reader.int32();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.fieldName = reader.string();
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.fieldType = reader.int32();
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.keyType = reader.int32();
                    continue;
                }
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.valueType = reader.int32();
                    continue;
                }
                case 6: {
                    if (tag !== 48) {
                        break;
                    }
                    message.index = longToNumber(reader.uint64());
                    continue;
                }
                case 7: {
                    if (tag !== 56) {
                        break;
                    }
                    message.boolKey = reader.bool();
                    continue;
                }
                case 8: {
                    if (tag !== 64) {
                        break;
                    }
                    message.intKey = longToNumber(reader.int64());
                    continue;
                }
                case 9: {
                    if (tag !== 72) {
                        break;
                    }
                    message.uintKey = longToNumber(reader.uint64());
                    continue;
                }
                case 10: {
                    if (tag !== 82) {
                        break;
                    }
                    message.stringKey = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            fieldNumber: isSet(object.fieldNumber) ? globalThis.Number(object.fieldNumber) : 0,
            fieldName: isSet(object.fieldName) ? globalThis.String(object.fieldName) : "",
            fieldType: isSet(object.fieldType) ? fieldDescriptorProto_TypeFromJSON(object.fieldType) : 1,
            keyType: isSet(object.keyType) ? fieldDescriptorProto_TypeFromJSON(object.keyType) : 1,
            valueType: isSet(object.valueType) ? fieldDescriptorProto_TypeFromJSON(object.valueType) : 1,
            index: isSet(object.index) ? globalThis.Number(object.index) : undefined,
            boolKey: isSet(object.boolKey) ? globalThis.Boolean(object.boolKey) : undefined,
            intKey: isSet(object.intKey) ? globalThis.Number(object.intKey) : undefined,
            uintKey: isSet(object.uintKey) ? globalThis.Number(object.uintKey) : undefined,
            stringKey: isSet(object.stringKey) ? globalThis.String(object.stringKey) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.fieldNumber !== undefined && message.fieldNumber !== 0) {
            obj.fieldNumber = Math.round(message.fieldNumber);
        }
        if (message.fieldName !== undefined && message.fieldName !== "") {
            obj.fieldName = message.fieldName;
        }
        if (message.fieldType !== undefined && message.fieldType !== 1) {
            obj.fieldType = fieldDescriptorProto_TypeToJSON(message.fieldType);
        }
        if (message.keyType !== undefined && message.keyType !== 1) {
            obj.keyType = fieldDescriptorProto_TypeToJSON(message.keyType);
        }
        if (message.valueType !== undefined && message.valueType !== 1) {
            obj.valueType = fieldDescriptorProto_TypeToJSON(message.valueType);
        }
        if (message.index !== undefined) {
            obj.index = Math.round(message.index);
        }
        if (message.boolKey !== undefined) {
            obj.boolKey = message.boolKey;
        }
        if (message.intKey !== undefined) {
            obj.intKey = Math.round(message.intKey);
        }
        if (message.uintKey !== undefined) {
            obj.uintKey = Math.round(message.uintKey);
        }
        if (message.stringKey !== undefined) {
            obj.stringKey = message.stringKey;
        }
        return obj;
    },
    create(base) {
        return FieldPathElement.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        const message = createBaseFieldPathElement();
        message.fieldNumber = (_a = object.fieldNumber) !== null && _a !== void 0 ? _a : 0;
        message.fieldName = (_b = object.fieldName) !== null && _b !== void 0 ? _b : "";
        message.fieldType = (_c = object.fieldType) !== null && _c !== void 0 ? _c : 1;
        message.keyType = (_d = object.keyType) !== null && _d !== void 0 ? _d : 1;
        message.valueType = (_e = object.valueType) !== null && _e !== void 0 ? _e : 1;
        message.index = (_f = object.index) !== null && _f !== void 0 ? _f : undefined;
        message.boolKey = (_g = object.boolKey) !== null && _g !== void 0 ? _g : undefined;
        message.intKey = (_h = object.intKey) !== null && _h !== void 0 ? _h : undefined;
        message.uintKey = (_j = object.uintKey) !== null && _j !== void 0 ? _j : undefined;
        message.stringKey = (_k = object.stringKey) !== null && _k !== void 0 ? _k : undefined;
        return message;
    },
};
function bytesFromBase64(b64) {
    if (globalThis.Buffer) {
        return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
    }
    else {
        const bin = globalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (globalThis.Buffer) {
        return globalThis.Buffer.from(arr).toString("base64");
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(globalThis.String.fromCharCode(byte));
        });
        return globalThis.btoa(bin.join(""));
    }
}
function toTimestamp(date) {
    const seconds = Math.trunc(date.getTime() / 1000);
    const nanos = (date.getTime() % 1000) * 1000000;
    return { seconds, nanos };
}
function fromTimestamp(t) {
    let millis = (t.seconds || 0) * 1000;
    millis += (t.nanos || 0) / 1000000;
    return new globalThis.Date(millis);
}
function fromJsonTimestamp(o) {
    if (o instanceof globalThis.Date) {
        return o;
    }
    else if (typeof o === "string") {
        return new globalThis.Date(o);
    }
    else {
        return fromTimestamp(Timestamp.fromJSON(o));
    }
}
function longToNumber(int64) {
    const num = globalThis.Number(int64.toString());
    if (num > globalThis.Number.MAX_SAFE_INTEGER) {
        throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
    }
    if (num < globalThis.Number.MIN_SAFE_INTEGER) {
        throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
    }
    return num;
}
function isSet(value) {
    return value !== null && value !== undefined;
}
//# sourceMappingURL=validate.js.map