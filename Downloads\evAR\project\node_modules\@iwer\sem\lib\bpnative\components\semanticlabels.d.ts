/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { SemanticLabelJSON, SemanticLabelsComponentJSON } from '../description.js';
import { SpatialEntityComponent, SpatialEntityComponentType } from './component.js';
import { Mesh } from 'three';
export declare class SemanticLabelsComponent extends SpatialEntityComponent {
    private _semanticLabel;
    type: SpatialEntityComponentType;
    constructor(spatialEntity: Mesh, initData: SemanticLabelJSON[]);
    get semanticLabel(): SemanticLabelJSON;
    set semanticLabel(value: string);
    get initData(): SemanticLabelJSON[];
    toJSON(): SemanticLabelsComponentJSON;
}
//# sourceMappingURL=semanticlabels.d.ts.map