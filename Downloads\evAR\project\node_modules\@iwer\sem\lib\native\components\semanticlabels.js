import { SemanticLabelMETA, semanticLabelMETAToJSON, } from '../../generated/protos/openxr_scene.js';
import { SpatialEntityComponent, SpatialEntityComponentType, } from './component.js';
function convertToReadableString(str) {
    return str.toLowerCase().replace(/_/g, ' ').trim();
}
export class SemanticLabelsComponent extends SpatialEntityComponent {
    constructor(spatialEntity, _semanticLabel) {
        super(spatialEntity);
        this._semanticLabel = _semanticLabel;
        this.type = SpatialEntityComponentType.SemanticLabels;
        this._spatialEntity.name = convertToReadableString(semanticLabelMETAToJSON(_semanticLabel));
    }
    get semanticLabel() {
        return this._semanticLabel;
    }
    set semanticLabel(value) {
        if (Object.values(SemanticLabelMETA).includes(value)) {
            this._semanticLabel = value;
        }
        else {
            this._semanticLabel = SemanticLabelMETA.UNRECOGNIZED;
        }
        this._spatialEntity.name = convertToReadableString(semanticLabelMETAToJSON(this._semanticLabel));
    }
    get initData() {
        return this._semanticLabel;
    }
    get pbData() {
        return this._semanticLabel;
    }
}
//# sourceMappingURL=semanticlabels.js.map