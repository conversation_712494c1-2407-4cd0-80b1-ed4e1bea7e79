// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.0
//   protoc               v5.29.1
// source: protos/openxr_scene.proto
/* eslint-disable */
import { BinaryReader, BinaryWriter } from '@bufbuild/protobuf/wire';
import { Pose, Rect2D, Rect3D } from './openxr_core.js';
export const protobufPackage = 'openxr.scene';
export var DynamicObjectTypeMETA;
(function (DynamicObjectTypeMETA) {
    DynamicObjectTypeMETA[DynamicObjectTypeMETA["Keyboard"] = 0] = "Keyboard";
    DynamicObjectTypeMETA[DynamicObjectTypeMETA["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(DynamicObjectTypeMETA || (DynamicObjectTypeMETA = {}));
export function dynamicObjectTypeMETAFromJSON(object) {
    switch (object) {
        case 0:
        case 'Keyboard':
            return DynamicObjectTypeMETA.Keyboard;
        case -1:
        case 'UNRECOGNIZED':
        default:
            return DynamicObjectTypeMETA.UNRECOGNIZED;
    }
}
export function dynamicObjectTypeMETAToJSON(object) {
    switch (object) {
        case DynamicObjectTypeMETA.Keyboard:
            return 'Keyboard';
        case DynamicObjectTypeMETA.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED';
    }
}
export var SemanticLabelMETA;
(function (SemanticLabelMETA) {
    SemanticLabelMETA[SemanticLabelMETA["UNKNOWN"] = 0] = "UNKNOWN";
    SemanticLabelMETA[SemanticLabelMETA["FLOOR"] = 1] = "FLOOR";
    SemanticLabelMETA[SemanticLabelMETA["CEILING"] = 2] = "CEILING";
    SemanticLabelMETA[SemanticLabelMETA["WALL_FACE"] = 3] = "WALL_FACE";
    SemanticLabelMETA[SemanticLabelMETA["TABLE"] = 4] = "TABLE";
    SemanticLabelMETA[SemanticLabelMETA["COUCH"] = 5] = "COUCH";
    SemanticLabelMETA[SemanticLabelMETA["DOOR_FRAME"] = 6] = "DOOR_FRAME";
    SemanticLabelMETA[SemanticLabelMETA["WINDOW_FRAME"] = 7] = "WINDOW_FRAME";
    SemanticLabelMETA[SemanticLabelMETA["OTHER"] = 8] = "OTHER";
    SemanticLabelMETA[SemanticLabelMETA["STORAGE"] = 9] = "STORAGE";
    SemanticLabelMETA[SemanticLabelMETA["BED"] = 10] = "BED";
    SemanticLabelMETA[SemanticLabelMETA["SCREEN"] = 11] = "SCREEN";
    SemanticLabelMETA[SemanticLabelMETA["LAMP"] = 12] = "LAMP";
    SemanticLabelMETA[SemanticLabelMETA["PLANT"] = 13] = "PLANT";
    SemanticLabelMETA[SemanticLabelMETA["WALL_ART"] = 14] = "WALL_ART";
    SemanticLabelMETA[SemanticLabelMETA["GLOBAL_MESH"] = 15] = "GLOBAL_MESH";
    SemanticLabelMETA[SemanticLabelMETA["INVISIBLE_WALL_FACE"] = 16] = "INVISIBLE_WALL_FACE";
    SemanticLabelMETA[SemanticLabelMETA["CHAIR"] = 17] = "CHAIR";
    SemanticLabelMETA[SemanticLabelMETA["INNER_WALL_FACE"] = 18] = "INNER_WALL_FACE";
    SemanticLabelMETA[SemanticLabelMETA["OTHER_ROOM_FACE"] = 19] = "OTHER_ROOM_FACE";
    SemanticLabelMETA[SemanticLabelMETA["OPENING"] = 20] = "OPENING";
    SemanticLabelMETA[SemanticLabelMETA["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(SemanticLabelMETA || (SemanticLabelMETA = {}));
export function semanticLabelMETAFromJSON(object) {
    switch (object) {
        case 0:
        case 'UNKNOWN':
            return SemanticLabelMETA.UNKNOWN;
        case 1:
        case 'FLOOR':
            return SemanticLabelMETA.FLOOR;
        case 2:
        case 'CEILING':
            return SemanticLabelMETA.CEILING;
        case 3:
        case 'WALL_FACE':
            return SemanticLabelMETA.WALL_FACE;
        case 4:
        case 'TABLE':
            return SemanticLabelMETA.TABLE;
        case 5:
        case 'COUCH':
            return SemanticLabelMETA.COUCH;
        case 6:
        case 'DOOR_FRAME':
            return SemanticLabelMETA.DOOR_FRAME;
        case 7:
        case 'WINDOW_FRAME':
            return SemanticLabelMETA.WINDOW_FRAME;
        case 8:
        case 'OTHER':
            return SemanticLabelMETA.OTHER;
        case 9:
        case 'STORAGE':
            return SemanticLabelMETA.STORAGE;
        case 10:
        case 'BED':
            return SemanticLabelMETA.BED;
        case 11:
        case 'SCREEN':
            return SemanticLabelMETA.SCREEN;
        case 12:
        case 'LAMP':
            return SemanticLabelMETA.LAMP;
        case 13:
        case 'PLANT':
            return SemanticLabelMETA.PLANT;
        case 14:
        case 'WALL_ART':
            return SemanticLabelMETA.WALL_ART;
        case 15:
        case 'GLOBAL_MESH':
            return SemanticLabelMETA.GLOBAL_MESH;
        case 16:
        case 'INVISIBLE_WALL_FACE':
            return SemanticLabelMETA.INVISIBLE_WALL_FACE;
        case 17:
        case 'CHAIR':
            return SemanticLabelMETA.CHAIR;
        case 18:
        case 'INNER_WALL_FACE':
            return SemanticLabelMETA.INNER_WALL_FACE;
        case 19:
        case 'OTHER_ROOM_FACE':
            return SemanticLabelMETA.OTHER_ROOM_FACE;
        case 20:
        case 'OPENING':
            return SemanticLabelMETA.OPENING;
        case -1:
        case 'UNRECOGNIZED':
        default:
            return SemanticLabelMETA.UNRECOGNIZED;
    }
}
export function semanticLabelMETAToJSON(object) {
    switch (object) {
        case SemanticLabelMETA.UNKNOWN:
            return 'UNKNOWN';
        case SemanticLabelMETA.FLOOR:
            return 'FLOOR';
        case SemanticLabelMETA.CEILING:
            return 'CEILING';
        case SemanticLabelMETA.WALL_FACE:
            return 'WALL_FACE';
        case SemanticLabelMETA.TABLE:
            return 'TABLE';
        case SemanticLabelMETA.COUCH:
            return 'COUCH';
        case SemanticLabelMETA.DOOR_FRAME:
            return 'DOOR_FRAME';
        case SemanticLabelMETA.WINDOW_FRAME:
            return 'WINDOW_FRAME';
        case SemanticLabelMETA.OTHER:
            return 'OTHER';
        case SemanticLabelMETA.STORAGE:
            return 'STORAGE';
        case SemanticLabelMETA.BED:
            return 'BED';
        case SemanticLabelMETA.SCREEN:
            return 'SCREEN';
        case SemanticLabelMETA.LAMP:
            return 'LAMP';
        case SemanticLabelMETA.PLANT:
            return 'PLANT';
        case SemanticLabelMETA.WALL_ART:
            return 'WALL_ART';
        case SemanticLabelMETA.GLOBAL_MESH:
            return 'GLOBAL_MESH';
        case SemanticLabelMETA.INVISIBLE_WALL_FACE:
            return 'INVISIBLE_WALL_FACE';
        case SemanticLabelMETA.CHAIR:
            return 'CHAIR';
        case SemanticLabelMETA.INNER_WALL_FACE:
            return 'INNER_WALL_FACE';
        case SemanticLabelMETA.OTHER_ROOM_FACE:
            return 'OTHER_ROOM_FACE';
        case SemanticLabelMETA.OPENING:
            return 'OPENING';
        case SemanticLabelMETA.UNRECOGNIZED:
        default:
            return 'UNRECOGNIZED';
    }
}
function createBaseRoomLayoutMETA() {
    return { floorUuid: '', ceilingUuid: '', wallUuids: [] };
}
export const RoomLayoutMETA = {
    encode(message, writer = new BinaryWriter()) {
        if (message.floorUuid !== '') {
            writer.uint32(10).string(message.floorUuid);
        }
        if (message.ceilingUuid !== '') {
            writer.uint32(18).string(message.ceilingUuid);
        }
        for (const v of message.wallUuids) {
            writer.uint32(26).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRoomLayoutMETA();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.floorUuid = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.ceilingUuid = reader.string();
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.wallUuids.push(reader.string());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            floorUuid: isSet(object.floorUuid)
                ? globalThis.String(object.floorUuid)
                : '',
            ceilingUuid: isSet(object.ceilingUuid)
                ? globalThis.String(object.ceilingUuid)
                : '',
            wallUuids: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.wallUuids)
                ? object.wallUuids.map((e) => globalThis.String(e))
                : [],
        };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if (message.floorUuid !== '') {
            obj.floorUuid = message.floorUuid;
        }
        if (message.ceilingUuid !== '') {
            obj.ceilingUuid = message.ceilingUuid;
        }
        if ((_a = message.wallUuids) === null || _a === void 0 ? void 0 : _a.length) {
            obj.wallUuids = message.wallUuids;
        }
        return obj;
    },
    create(base) {
        return RoomLayoutMETA.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseRoomLayoutMETA();
        message.floorUuid = (_a = object.floorUuid) !== null && _a !== void 0 ? _a : '';
        message.ceilingUuid = (_b = object.ceilingUuid) !== null && _b !== void 0 ? _b : '';
        message.wallUuids = ((_c = object.wallUuids) === null || _c === void 0 ? void 0 : _c.map((e) => e)) || [];
        return message;
    },
};
function createBaseSpaceContainerMETA() {
    return { uuids: [] };
}
export const SpaceContainerMETA = {
    encode(message, writer = new BinaryWriter()) {
        for (const v of message.uuids) {
            writer.uint32(10).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpaceContainerMETA();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.uuids.push(reader.string());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            uuids: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.uuids)
                ? object.uuids.map((e) => globalThis.String(e))
                : [],
        };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if ((_a = message.uuids) === null || _a === void 0 ? void 0 : _a.length) {
            obj.uuids = message.uuids;
        }
        return obj;
    },
    create(base) {
        return SpaceContainerMETA.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseSpaceContainerMETA();
        message.uuids = ((_a = object.uuids) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
        return message;
    },
};
function createBaseBoundary2DMETA() {
    return { vertices: new Uint8Array(0) };
}
export const Boundary2DMETA = {
    encode(message, writer = new BinaryWriter()) {
        if (message.vertices.length !== 0) {
            writer.uint32(10).bytes(message.vertices);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBoundary2DMETA();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.vertices = reader.bytes();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            vertices: isSet(object.vertices)
                ? bytesFromBase64(object.vertices)
                : new Uint8Array(0),
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.vertices.length !== 0) {
            obj.vertices = base64FromBytes(message.vertices);
        }
        return obj;
    },
    create(base) {
        return Boundary2DMETA.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseBoundary2DMETA();
        message.vertices = (_a = object.vertices) !== null && _a !== void 0 ? _a : new Uint8Array(0);
        return message;
    },
};
function createBaseTriangleMeshMETA() {
    return { vertices: new Uint8Array(0), indices: new Uint8Array(0) };
}
export const TriangleMeshMETA = {
    encode(message, writer = new BinaryWriter()) {
        if (message.vertices.length !== 0) {
            writer.uint32(10).bytes(message.vertices);
        }
        if (message.indices.length !== 0) {
            writer.uint32(18).bytes(message.indices);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTriangleMeshMETA();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.vertices = reader.bytes();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.indices = reader.bytes();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            vertices: isSet(object.vertices)
                ? bytesFromBase64(object.vertices)
                : new Uint8Array(0),
            indices: isSet(object.indices)
                ? bytesFromBase64(object.indices)
                : new Uint8Array(0),
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.vertices.length !== 0) {
            obj.vertices = base64FromBytes(message.vertices);
        }
        if (message.indices.length !== 0) {
            obj.indices = base64FromBytes(message.indices);
        }
        return obj;
    },
    create(base) {
        return TriangleMeshMETA.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseTriangleMeshMETA();
        message.vertices = (_a = object.vertices) !== null && _a !== void 0 ? _a : new Uint8Array(0);
        message.indices = (_b = object.indices) !== null && _b !== void 0 ? _b : new Uint8Array(0);
        return message;
    },
};
function createBaseDynamicObjectMETA() {
    return { classType: 0 };
}
export const DynamicObjectMETA = {
    encode(message, writer = new BinaryWriter()) {
        if (message.classType !== 0) {
            writer.uint32(8).int32(message.classType);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDynamicObjectMETA();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.classType = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            classType: isSet(object.classType)
                ? dynamicObjectTypeMETAFromJSON(object.classType)
                : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.classType !== 0) {
            obj.classType = dynamicObjectTypeMETAToJSON(message.classType);
        }
        return obj;
    },
    create(base) {
        return DynamicObjectMETA.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseDynamicObjectMETA();
        message.classType = (_a = object.classType) !== null && _a !== void 0 ? _a : 0;
        return message;
    },
};
function createBaseRoomMeshMETA() {
    return { mesh: undefined, faces: [] };
}
export const RoomMeshMETA = {
    encode(message, writer = new BinaryWriter()) {
        if (message.mesh !== undefined) {
            TriangleMeshMETA.encode(message.mesh, writer.uint32(10).fork()).join();
        }
        for (const v of message.faces) {
            RoomMeshMETA_RoomFace.encode(v, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRoomMeshMETA();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.mesh = TriangleMeshMETA.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.faces.push(RoomMeshMETA_RoomFace.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            mesh: isSet(object.mesh)
                ? TriangleMeshMETA.fromJSON(object.mesh)
                : undefined,
            faces: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.faces)
                ? object.faces.map((e) => RoomMeshMETA_RoomFace.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if (message.mesh !== undefined) {
            obj.mesh = TriangleMeshMETA.toJSON(message.mesh);
        }
        if ((_a = message.faces) === null || _a === void 0 ? void 0 : _a.length) {
            obj.faces = message.faces.map((e) => RoomMeshMETA_RoomFace.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return RoomMeshMETA.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseRoomMeshMETA();
        message.mesh =
            object.mesh !== undefined && object.mesh !== null
                ? TriangleMeshMETA.fromPartial(object.mesh)
                : undefined;
        message.faces =
            ((_a = object.faces) === null || _a === void 0 ? void 0 : _a.map((e) => RoomMeshMETA_RoomFace.fromPartial(e))) || [];
        return message;
    },
};
function createBaseRoomMeshMETA_RoomFace() {
    return { uuid: '', parentUuid: '', semanticLabel: 0 };
}
export const RoomMeshMETA_RoomFace = {
    encode(message, writer = new BinaryWriter()) {
        if (message.uuid !== '') {
            writer.uint32(10).string(message.uuid);
        }
        if (message.parentUuid !== '') {
            writer.uint32(18).string(message.parentUuid);
        }
        if (message.semanticLabel !== 0) {
            writer.uint32(24).int32(message.semanticLabel);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRoomMeshMETA_RoomFace();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.uuid = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.parentUuid = reader.string();
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.semanticLabel = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : '',
            parentUuid: isSet(object.parentUuid)
                ? globalThis.String(object.parentUuid)
                : '',
            semanticLabel: isSet(object.semanticLabel)
                ? semanticLabelMETAFromJSON(object.semanticLabel)
                : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.uuid !== '') {
            obj.uuid = message.uuid;
        }
        if (message.parentUuid !== '') {
            obj.parentUuid = message.parentUuid;
        }
        if (message.semanticLabel !== 0) {
            obj.semanticLabel = semanticLabelMETAToJSON(message.semanticLabel);
        }
        return obj;
    },
    create(base) {
        return RoomMeshMETA_RoomFace.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseRoomMeshMETA_RoomFace();
        message.uuid = (_a = object.uuid) !== null && _a !== void 0 ? _a : '';
        message.parentUuid = (_b = object.parentUuid) !== null && _b !== void 0 ? _b : '';
        message.semanticLabel = (_c = object.semanticLabel) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseSpatialEntity() {
    return {
        uuid: '',
        semanticLabel_META: undefined,
        locatable_META: undefined,
        boundary2D_META: undefined,
        bounded2D_META: undefined,
        bounded3D_META: undefined,
        roomLayout_META: undefined,
        spaceContainer_META: undefined,
        triangleMesh_META: undefined,
        dynamicObject_META: undefined,
    };
}
export const SpatialEntity = {
    encode(message, writer = new BinaryWriter()) {
        if (message.uuid !== '') {
            writer.uint32(10).string(message.uuid);
        }
        if (message.semanticLabel_META !== undefined) {
            writer.uint32(16).int32(message.semanticLabel_META);
        }
        if (message.locatable_META !== undefined) {
            Pose.encode(message.locatable_META, writer.uint32(26).fork()).join();
        }
        if (message.boundary2D_META !== undefined) {
            Boundary2DMETA.encode(message.boundary2D_META, writer.uint32(34).fork()).join();
        }
        if (message.bounded2D_META !== undefined) {
            Rect2D.encode(message.bounded2D_META, writer.uint32(42).fork()).join();
        }
        if (message.bounded3D_META !== undefined) {
            Rect3D.encode(message.bounded3D_META, writer.uint32(50).fork()).join();
        }
        if (message.roomLayout_META !== undefined) {
            RoomLayoutMETA.encode(message.roomLayout_META, writer.uint32(58).fork()).join();
        }
        if (message.spaceContainer_META !== undefined) {
            SpaceContainerMETA.encode(message.spaceContainer_META, writer.uint32(66).fork()).join();
        }
        if (message.triangleMesh_META !== undefined) {
            TriangleMeshMETA.encode(message.triangleMesh_META, writer.uint32(74).fork()).join();
        }
        if (message.dynamicObject_META !== undefined) {
            DynamicObjectMETA.encode(message.dynamicObject_META, writer.uint32(82).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpatialEntity();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.uuid = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.semanticLabel_META = reader.int32();
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.locatable_META = Pose.decode(reader, reader.uint32());
                    continue;
                }
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.boundary2D_META = Boundary2DMETA.decode(reader, reader.uint32());
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.bounded2D_META = Rect2D.decode(reader, reader.uint32());
                    continue;
                }
                case 6: {
                    if (tag !== 50) {
                        break;
                    }
                    message.bounded3D_META = Rect3D.decode(reader, reader.uint32());
                    continue;
                }
                case 7: {
                    if (tag !== 58) {
                        break;
                    }
                    message.roomLayout_META = RoomLayoutMETA.decode(reader, reader.uint32());
                    continue;
                }
                case 8: {
                    if (tag !== 66) {
                        break;
                    }
                    message.spaceContainer_META = SpaceContainerMETA.decode(reader, reader.uint32());
                    continue;
                }
                case 9: {
                    if (tag !== 74) {
                        break;
                    }
                    message.triangleMesh_META = TriangleMeshMETA.decode(reader, reader.uint32());
                    continue;
                }
                case 10: {
                    if (tag !== 82) {
                        break;
                    }
                    message.dynamicObject_META = DynamicObjectMETA.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : '',
            semanticLabel_META: isSet(object.semanticLabel_META)
                ? semanticLabelMETAFromJSON(object.semanticLabel_META)
                : undefined,
            locatable_META: isSet(object.locatable_META)
                ? Pose.fromJSON(object.locatable_META)
                : undefined,
            boundary2D_META: isSet(object.boundary2D_META)
                ? Boundary2DMETA.fromJSON(object.boundary2D_META)
                : undefined,
            bounded2D_META: isSet(object.bounded2D_META)
                ? Rect2D.fromJSON(object.bounded2D_META)
                : undefined,
            bounded3D_META: isSet(object.bounded3D_META)
                ? Rect3D.fromJSON(object.bounded3D_META)
                : undefined,
            roomLayout_META: isSet(object.roomLayout_META)
                ? RoomLayoutMETA.fromJSON(object.roomLayout_META)
                : undefined,
            spaceContainer_META: isSet(object.spaceContainer_META)
                ? SpaceContainerMETA.fromJSON(object.spaceContainer_META)
                : undefined,
            triangleMesh_META: isSet(object.triangleMesh_META)
                ? TriangleMeshMETA.fromJSON(object.triangleMesh_META)
                : undefined,
            dynamicObject_META: isSet(object.dynamicObject_META)
                ? DynamicObjectMETA.fromJSON(object.dynamicObject_META)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.uuid !== '') {
            obj.uuid = message.uuid;
        }
        if (message.semanticLabel_META !== undefined) {
            obj.semanticLabel_META = semanticLabelMETAToJSON(message.semanticLabel_META);
        }
        if (message.locatable_META !== undefined) {
            obj.locatable_META = Pose.toJSON(message.locatable_META);
        }
        if (message.boundary2D_META !== undefined) {
            obj.boundary2D_META = Boundary2DMETA.toJSON(message.boundary2D_META);
        }
        if (message.bounded2D_META !== undefined) {
            obj.bounded2D_META = Rect2D.toJSON(message.bounded2D_META);
        }
        if (message.bounded3D_META !== undefined) {
            obj.bounded3D_META = Rect3D.toJSON(message.bounded3D_META);
        }
        if (message.roomLayout_META !== undefined) {
            obj.roomLayout_META = RoomLayoutMETA.toJSON(message.roomLayout_META);
        }
        if (message.spaceContainer_META !== undefined) {
            obj.spaceContainer_META = SpaceContainerMETA.toJSON(message.spaceContainer_META);
        }
        if (message.triangleMesh_META !== undefined) {
            obj.triangleMesh_META = TriangleMeshMETA.toJSON(message.triangleMesh_META);
        }
        if (message.dynamicObject_META !== undefined) {
            obj.dynamicObject_META = DynamicObjectMETA.toJSON(message.dynamicObject_META);
        }
        return obj;
    },
    create(base) {
        return SpatialEntity.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseSpatialEntity();
        message.uuid = (_a = object.uuid) !== null && _a !== void 0 ? _a : '';
        message.semanticLabel_META = (_b = object.semanticLabel_META) !== null && _b !== void 0 ? _b : undefined;
        message.locatable_META =
            object.locatable_META !== undefined && object.locatable_META !== null
                ? Pose.fromPartial(object.locatable_META)
                : undefined;
        message.boundary2D_META =
            object.boundary2D_META !== undefined && object.boundary2D_META !== null
                ? Boundary2DMETA.fromPartial(object.boundary2D_META)
                : undefined;
        message.bounded2D_META =
            object.bounded2D_META !== undefined && object.bounded2D_META !== null
                ? Rect2D.fromPartial(object.bounded2D_META)
                : undefined;
        message.bounded3D_META =
            object.bounded3D_META !== undefined && object.bounded3D_META !== null
                ? Rect3D.fromPartial(object.bounded3D_META)
                : undefined;
        message.roomLayout_META =
            object.roomLayout_META !== undefined && object.roomLayout_META !== null
                ? RoomLayoutMETA.fromPartial(object.roomLayout_META)
                : undefined;
        message.spaceContainer_META =
            object.spaceContainer_META !== undefined &&
                object.spaceContainer_META !== null
                ? SpaceContainerMETA.fromPartial(object.spaceContainer_META)
                : undefined;
        message.triangleMesh_META =
            object.triangleMesh_META !== undefined &&
                object.triangleMesh_META !== null
                ? TriangleMeshMETA.fromPartial(object.triangleMesh_META)
                : undefined;
        message.dynamicObject_META =
            object.dynamicObject_META !== undefined &&
                object.dynamicObject_META !== null
                ? DynamicObjectMETA.fromPartial(object.dynamicObject_META)
                : undefined;
        return message;
    },
};
function createBaseScene() {
    return { type: '', version: 0, spatialEntities: [] };
}
export const Scene = {
    encode(message, writer = new BinaryWriter()) {
        if (message.type !== '') {
            writer.uint32(10).string(message.type);
        }
        if (message.version !== 0) {
            writer.uint32(16).uint32(message.version);
        }
        for (const v of message.spatialEntities) {
            SpatialEntity.encode(v, writer.uint32(26).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseScene();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.type = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.version = reader.uint32();
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.spatialEntities.push(SpatialEntity.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            type: isSet(object.type) ? globalThis.String(object.type) : '',
            version: isSet(object.version) ? globalThis.Number(object.version) : 0,
            spatialEntities: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.spatialEntities)
                ? object.spatialEntities.map((e) => SpatialEntity.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        var _a;
        const obj = {};
        if (message.type !== '') {
            obj.type = message.type;
        }
        if (message.version !== 0) {
            obj.version = Math.round(message.version);
        }
        if ((_a = message.spatialEntities) === null || _a === void 0 ? void 0 : _a.length) {
            obj.spatialEntities = message.spatialEntities.map((e) => SpatialEntity.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return Scene.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseScene();
        message.type = (_a = object.type) !== null && _a !== void 0 ? _a : '';
        message.version = (_b = object.version) !== null && _b !== void 0 ? _b : 0;
        message.spatialEntities =
            ((_c = object.spatialEntities) === null || _c === void 0 ? void 0 : _c.map((e) => SpatialEntity.fromPartial(e))) || [];
        return message;
    },
};
function bytesFromBase64(b64) {
    if (globalThis.Buffer) {
        return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
    }
    else {
        const bin = globalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (globalThis.Buffer) {
        return globalThis.Buffer.from(arr).toString('base64');
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(globalThis.String.fromCharCode(byte));
        });
        return globalThis.btoa(bin.join(''));
    }
}
function isSet(value) {
    return value !== null && value !== undefined;
}
//# sourceMappingURL=openxr_scene.js.map