/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { BufferAttribute, BufferGeometry } from 'three';
import { SpatialEntityComponent, SpatialEntityComponentType, } from './component.js';
function vec3ArrayToFloat64Array(arr) {
    const result = new Float32Array(arr.length * 3);
    let index = 0;
    for (const vec of arr) {
        result[index++] = vec.x;
        result[index++] = vec.y;
        result[index++] = vec.z;
    }
    return result;
}
export class TriangleMeshComponent extends SpatialEntityComponent {
    constructor(spatialEntity, initData) {
        super(spatialEntity);
        this._polygonCount = 0;
        this._vertexCount = 0;
        this._dimensions = { x: 0, y: 0, z: 0 };
        this.type = SpatialEntityComponentType.TriangleMesh;
        const { vertices, indices } = initData;
        this._vertices = vertices;
        this._indices = indices;
        this.buildGeometry();
    }
    buildGeometry() {
        var _a;
        const geometry = new BufferGeometry();
        const vertices = vec3ArrayToFloat64Array(this._vertices);
        geometry.setAttribute('position', new BufferAttribute(vertices, 3));
        geometry.setIndex(new BufferAttribute(new Uint16Array(this._indices), 1));
        (_a = this._spatialEntity.geometry) === null || _a === void 0 ? void 0 : _a.dispose();
        this._spatialEntity.geometry = geometry;
        geometry.computeVertexNormals();
        this._vertexCount = geometry.attributes.position.count;
        this._polygonCount = geometry.index
            ? geometry.index.count / 3
            : this._vertexCount / 3;
        geometry.computeBoundingBox();
        const boundingBox = geometry.boundingBox;
        this._dimensions = {
            x: boundingBox.max.x - boundingBox.min.x,
            y: boundingBox.max.y - boundingBox.min.y,
            z: boundingBox.max.z - boundingBox.min.z,
        };
    }
    get vertexCount() {
        return this._vertexCount;
    }
    get polygonCount() {
        return this._polygonCount;
    }
    get dimensions() {
        return this._dimensions;
    }
    get initData() {
        return {
            vertices: this._vertices,
            indices: this._indices,
        };
    }
    toJSON() {
        return {
            triangleMesh: this.initData,
        };
    }
}
//# sourceMappingURL=trianglemesh.js.map