{"version": 3, "file": "entity.js", "sourceRoot": "", "sources": ["../../src/internal/entity.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,OAAO,CAAC;AACnD,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,EAAE,MAAM,MAAM,CAAC;AACjE,OAAO,EAAE,iBAAiB,EAAqB,MAAM,kBAAkB,CAAC;AACxE,OAAO,EAEN,0BAA0B,GAC1B,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AAC/D,OAAO,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AAC/D,OAAO,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AAC/D,OAAO,EAAE,uBAAuB,EAAE,MAAM,gCAAgC,CAAC;AACzE,OAAO,EAAE,qBAAqB,EAAE,MAAM,8BAA8B,CAAC;AACrE,OAAO,EAAE,gBAAgB,EAAE,MAAM,MAAM,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAE3D,MAAM,CAAN,IAAY,iBAIX;AAJD,WAAY,iBAAiB;IAC5B,oCAAe,CAAA;IACf,gCAAW,CAAA;IACX,kCAAa,CAAA;AACd,CAAC,EAJW,iBAAiB,KAAjB,iBAAiB,QAI5B;AAED,MAAM,qBAAqB,GAAgD;IAC1E,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,OAAO;IACrD,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,gBAAgB,CAAC,IAAI;IACpD,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,EAAE,gBAAgB,CAAC,MAAM;IAChE,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE,gBAAgB,CAAC,IAAI;IAC1D,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,gBAAgB,CAAC,IAAI;IACrD,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC,MAAM;IACzD,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC,OAAO;IACtD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACnD,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,gBAAgB,CAAC,GAAG;IAC7C,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,gBAAgB,CAAC,IAAI;IAC/C,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,gBAAgB,CAAC,MAAM;IACnD,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,gBAAgB,CAAC,UAAU;IAC3D,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACjD,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,KAAK;IACnD,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE,gBAAgB,CAAC,KAAK;IAC3D,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,KAAK;CACnD,CAAC;AAEF,MAAM,OAAO,aAAc,SAAQ,IAAI;IAUtC,YAAY,OAAe,YAAY,EAAE;QACxC,KAAK,CACJ,SAAS,EACT,IAAI,oBAAoB,CAAC;YACxB,KAAK,EAAE,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE;YAC/B,WAAW,EAAE,IAAI;SACjB,CAAC,CACF,CAAC;QAhBI,SAAI,GAAG,wBAAwB,CAAC;QAChC,oBAAe,GAAG,IAAI,CAAC;QAEtB,kBAAa,GAGjB,IAAI,GAAG,EAAE,CAAC;QAWb,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,YAAY,CAAC;IAC1B,CAAC;IAED,iBAAiB,CAAC,KAAe;QAChC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED,YAAY,CAAC,aAAyC,EAAE,QAAa;QACpE,IAAI,SAAS,GAAkC,IAAI,CAAC;QACpD,QAAQ,aAAa,EAAE,CAAC;YACvB,KAAK,0BAA0B,CAAC,SAAS;gBACxC,SAAS,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACnD,MAAM;YACP,KAAK,0BAA0B,CAAC,SAAS;gBACxC,SAAS,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACnD,MAAM;YACP,KAAK,0BAA0B,CAAC,SAAS;gBACxC,SAAS,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACnD,MAAM;YACP,KAAK,0BAA0B,CAAC,YAAY;gBAC3C,SAAS,GAAG,IAAI,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACtD,MAAM;YACP,KAAK,0BAA0B,CAAC,cAAc;gBAC7C,SAAS,GAAG,IAAI,uBAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACxD,MAAM;QACR,CAAC;QACD,IAAI,SAAS,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAClD,CAAC;IACF,CAAC;IAED,YAAY,CAAC,aAAyC;QACrD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAED,SAAS;QACR,MAAM,KAAK,GAAG,IAAI,aAAa,EAAE,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;YAChD,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACd,CAAC;IAED,IAAI,YAAY;QACf,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAC5C;gBACC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClB,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClB,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClB,CAAC,EAAE,CAAC;aACJ,EACD;gBACC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACpB,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACpB,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACpB,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;aACpB,CACD,CAAC;YACF,MAAM,aAAa,GAClB,qBAAqB,CAEnB,IAAI,CAAC,YAAY,CAChB,0BAA0B,CAAC,cAAc,CAE1C,CAAC,aAAa,CACf,CAAC;YAEH,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBACjD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAC3C,0BAA0B,CAAC,SAAS,CACd,CAAC;gBACxB,MAAM,OAAO,GAAuB;oBACnC,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;oBAC3C,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;oBACtD,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;oBACjE,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;oBACtD,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;iBAC3C,CAAC;gBACF,IAAI,CAAC,aAAa,GAAG,IAAI,WAAW,CACnC,gBAAgB,EAChB,OAAO,EACP,aAAa,CACb,CAAC;YACH,CAAC;iBAAM,IACN,IAAI,CAAC,UAAU,KAAK,iBAAiB,CAAC,GAAG;gBACzC,IAAI,CAAC,UAAU,KAAK,iBAAiB,CAAC,IAAI,EACzC,CAAC;gBACF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC;qBACrD,KAAqB,CAAC;gBACxB,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAM,CAAC,KAAK,CAAC,CAAC;gBAC5D,IAAI,CAAC,aAAa,GAAG,IAAI,UAAU,CAClC,gBAAgB,EAChB,QAAQ,EACR,OAAO,EACP,aAAa,CACb,CAAC;YACH,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,IAAuB;QACtC,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;YACzC,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAChC,aAAa,CACb,CAAC,CAAC,CAA+B,CAAC;YACnC,IAAI,aAAa,EAAE,CAAC;gBACnB,MAAM,QAAQ,GAAI,aAAqB,CAAC,aAAa,CAAC,CAAC;gBACvD,IAAI,MAAM,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;oBACvE,aAAa,CAAC,YAAY,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;gBACrD,CAAC;YACF,CAAC;QACF,CAAC,CAAC,CAAC;QACH,OAAO,aAAa,CAAC;IACtB,CAAC;IAED,IAAI,UAAU;QACb,IAAI,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7D,OAAO,iBAAiB,CAAC,KAAK,CAAC;QAChC,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,SAAS,CAAC,EAAE,CAAC;YACpE,OAAO,iBAAiB,CAAC,GAAG,CAAC;QAC9B,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,YAAY,CAAC,EAAE,CAAC;YACvE,OAAO,iBAAiB,CAAC,IAAI,CAAC;QAC/B,CAAC;QACD,MAAM,IAAI,YAAY,CAAC,yBAAyB,EAAE,mBAAmB,CAAC,CAAC;IACxE,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,MAAqB;QAClC,MAAM,IAAI,GAAsB;YAC/B,IAAI,EAAE,MAAM,CAAC,WAAW;YACxB,UAAU,EAAE,EAAE;SACd,CAAC;QACF,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;YAC9C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACb,CAAC;CACD"}