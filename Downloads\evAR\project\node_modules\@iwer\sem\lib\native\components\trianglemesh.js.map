{"version": 3, "file": "trianglemesh.js", "sourceRoot": "", "sources": ["../../../src/native/components/trianglemesh.ts"], "names": [], "mappings": "AAAA,OAAO,EACN,eAAe,EACf,cAAc,GAGd,MAAM,OAAO,CAAC;AACf,OAAO,EACN,sBAAsB,EACtB,0BAA0B,GAC1B,MAAM,gBAAgB,CAAC;AAKxB,SAAS,uBAAuB,CAAC,GAAc;IAC9C,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAChD,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;QACvB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACzB,CAAC;IACD,OAAO,MAAM,CAAC;AACf,CAAC;AAED,MAAM,OAAO,qBAAsB,SAAQ,sBAAsB;IAQhE,YAAY,aAAmB,EAAE,QAA0B;QAC1D,KAAK,CAAC,aAAa,CAAC,CAAC;QANd,kBAAa,GAAW,CAAC,CAAC;QAC1B,iBAAY,GAAW,CAAC,CAAC;QACzB,gBAAW,GAAY,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACpD,SAAI,GAAG,0BAA0B,CAAC,YAAY,CAAC;QAI9C,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC;QACvC,MAAM,aAAa,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxD,MAAM,YAAY,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACrD,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACnD,SAAS,CAAC,IAAI,CAAC;gBACd,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;gBACvB,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aAC3B,CAAC,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;QAClC,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,MAAM,QAAQ,GAAG,aAAa,CAAC,QAA6B,CAAC;QAC7D,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9B,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC;QACjC,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACpC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAChC,aAAa,CAAC,WAAW,GAAG,GAAG,CAAC;IACjC,CAAC;IAEO,aAAa;;QACpB,MAAM,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;QACtC,MAAM,QAAQ,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,QAAQ,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;QACpE,QAAQ,CAAC,QAAQ,CAAC,IAAI,eAAe,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,0CAAE,OAAO,EAAE,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACxC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;QACvD,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK;YAClC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;YAC1B,CAAC,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACzB,QAAQ,CAAC,kBAAkB,EAAE,CAAC;QAC9B,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAY,CAAC;QAC1C,IAAI,CAAC,WAAW,GAAG;YAClB,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;YACxC,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;YACxC,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;SACxC,CAAC;IACH,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,YAAY,CAAC;IAC1B,CAAC;IAED,IAAI,YAAY;QACf,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAED,IAAI,UAAU;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAED,IAAI,QAAQ;QACX,OAAO;YACN,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;SACtB,CAAC;IACH,CAAC;IAED,IAAI,MAAM;QACT,MAAM,aAAa,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9D,MAAM,YAAY,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpD,OAAO;YACN,QAAQ,EAAE,IAAI,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC;YAC9C,OAAO,EAAE,IAAI,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC;SACxB,CAAC;IACvB,CAAC;CACD"}