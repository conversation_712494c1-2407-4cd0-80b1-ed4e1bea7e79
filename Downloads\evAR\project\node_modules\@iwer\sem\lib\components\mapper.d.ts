/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import React from 'react';
export type KeyMapType = Partial<Record<XRHandedness, {
    [key: string]: string;
}>>;
export declare const DEFAULT_KEYMAP: KeyMapType;
interface KeyMapMenuProps {
    keyMap: KeyMapType;
    setKeyMap: React.Dispatch<React.SetStateAction<KeyMapType>>;
}
export declare const KeyMapMenu: React.FC<KeyMapMenuProps>;
export {};
//# sourceMappingURL=mapper.d.ts.map