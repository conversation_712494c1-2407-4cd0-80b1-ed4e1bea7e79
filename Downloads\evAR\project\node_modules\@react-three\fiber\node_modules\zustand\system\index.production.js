System.register(["react"],function(O){"use strict";var y,f,v,E,p;return{setters:[function(d){y=d.useReducer,f=d.useRef,v=d.useDebugValue,E=d.useEffect,p=d.useLayoutEffect}],execute:function(){O("default",A);function d(g){let e;const c=new Set,u=(t,s)=>{const r=typeof t=="function"?t(e):t;if(r!==e){const a=e;e=s?r:Object.assign({},e,r),c.forEach(n=>n(e,a))}},o=()=>e,S=(t,s=o,r=Object.is)=>{console.warn("[DEPRECATED] Please use `subscribeWithSelector` middleware");let a=s(e);function n(){const l=s(e);if(!r(a,l)){const b=a;t(a=l,b)}}return c.add(n),()=>c.delete(n)},i={setState:u,getState:o,subscribe:(t,s,r)=>s||r?S(t,s,r):(c.add(t),()=>c.delete(t)),destroy:()=>c.clear()};return e=g(u,o,i),i}const x=typeof window>"u"||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent),R=x?E:p;function A(g){const e=typeof g=="function"?d(g):g,c=(u=e.getState,o=Object.is)=>{const[,S]=y(w=>w+1,0),i=e.getState(),t=f(i),s=f(u),r=f(o),a=f(!1),n=f();n.current===void 0&&(n.current=u(i));let l,b=!1;(t.current!==i||s.current!==u||r.current!==o||a.current)&&(l=u(i),b=!o(n.current,l)),R(()=>{b&&(n.current=l),t.current=i,s.current=u,r.current=o,a.current=!1});const T=f(i);R(()=>{const w=()=>{try{const D=e.getState(),j=s.current(D);r.current(n.current,j)||(t.current=D,n.current=j,S())}catch{a.current=!0,S()}},C=e.subscribe(w);return e.getState()!==T.current&&w(),C},[]);const h=b?l:n.current;return v(h),h};return Object.assign(c,e),c[Symbol.iterator]=function(){console.warn("[useStore, api] = create() is deprecated and will be removed in v4");const u=[c,e];return{next(){const o=u.length<=0;return{value:u.shift(),done:o}}}},c}}}});
