import { Mesh } from 'three';
export declare enum SpatialEntityComponentType {
    Locatable = "locatable",
    Bounded3D = "bounded3D",
    Bounded2D = "bounded2D",
    TriangleMesh = "triangleMesh",
    SemanticLabel = "semanticLabel"
}
export declare abstract class SpatialEntityComponent extends EventTarget {
    protected _spatialEntity: Mesh;
    constructor(_spatialEntity: Mesh);
    abstract get initData(): any;
    abstract get pbData(): any;
    abstract type: SpatialEntityComponentType;
}
//# sourceMappingURL=component.d.ts.map