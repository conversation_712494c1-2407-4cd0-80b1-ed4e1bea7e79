/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export const Environments = {
    living_room: () => import('./living_room.json'),
    meeting_room: () => import('./meeting_room.json'),
    music_room: () => import('./music_room.json'),
    office_large: () => import('./office_large.json'),
    office_small: () => import('./office_small.json'),
};
//# sourceMappingURL=registry.js.map