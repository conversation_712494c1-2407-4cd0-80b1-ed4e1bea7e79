import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Button, ButtonGroup } from './styled.js';
import { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { GamepadIcon } from './icons.js';
import { faBan } from '@fortawesome/free-solid-svg-icons';
import styled from 'styled-components';
const SettingsContainer = styled.div `
	display: flex;
	justify-content: space-between;
	pointer-events: all;
	position: fixed;
	display: flex;
	top: 40px;
	left: calc(50vw - 156px);
	width: 312px;
`;
const Column = styled.div `
	display: flex;
	flex-direction: column;
	width: 50%;
`;
const Row = styled.div `
	display: flex;
	height: 24px;
	align-items: center;
	margin-bottom: 2px;
`;
export const SettingsMenu = ({ keyMap, setKeyMap, }) => {
    const [currentMapping, setCurrentMapping] = useState(null);
    const startMapping = (controller, action) => {
        setCurrentMapping({ controller, action });
    };
    const unmapKey = (controller, action) => {
        setKeyMap((prevKeyMap) => ({
            ...prevKeyMap,
            [controller]: {
                ...prevKeyMap[controller],
                [action]: 'Unmapped',
            },
        }));
    };
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (currentMapping) {
                setKeyMap((prevKeyMap) => ({
                    ...prevKeyMap,
                    [currentMapping.controller]: {
                        ...prevKeyMap[currentMapping.controller],
                        [currentMapping.action]: event.code,
                    },
                }));
                setCurrentMapping(null);
            }
        };
        const handleMouseDown = (event) => {
            if (currentMapping) {
                const mouseButton = event.button === 0
                    ? 'MouseLeft'
                    : event.button === 2
                        ? 'MouseRight'
                        : null;
                if (mouseButton) {
                    setKeyMap((prevKeyMap) => ({
                        ...prevKeyMap,
                        [currentMapping.controller]: {
                            ...prevKeyMap[currentMapping.controller],
                            [currentMapping.action]: mouseButton,
                        },
                    }));
                    setCurrentMapping(null);
                }
            }
        };
        const preventDefaultContextMenu = (event) => {
            event.preventDefault();
        };
        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('mousedown', handleMouseDown);
        window.addEventListener('contextmenu', preventDefaultContextMenu);
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('mousedown', handleMouseDown);
            window.removeEventListener('contextmenu', preventDefaultContextMenu);
        };
    }, [currentMapping, setKeyMap]);
    return (_jsxs(SettingsContainer, { children: [_jsx(Column, { children: Object.keys(keyMap.left).map((action) => (_jsxs(Row, { children: [_jsx(GamepadIcon, { buttonName: action === 'up' ? 'thumbstick' : action, handedness: "left" }), _jsxs(ButtonGroup, { "$reverse": false, children: [_jsx(Button, { "$reverse": false, style: {
                                        width: '100px',
                                        backgroundColor: currentMapping &&
                                            currentMapping.controller === 'left' &&
                                            currentMapping.action === action
                                            ? 'rgba(255, 255, 255, 0.6)'
                                            : 'rgba(255, 255, 255, 0.3)',
                                    }, onClick: () => startMapping('left', action), onContextMenu: (e) => e.preventDefault(), children: keyMap.left[action] }), _jsx(Button, { style: { width: '24px' }, "$reverse": false, onClick: () => unmapKey('left', action), onContextMenu: (e) => e.preventDefault(), children: _jsx(FontAwesomeIcon, { icon: faBan }) })] })] }, action))) }), _jsx(Column, { children: Object.keys(keyMap.right).map((action) => (_jsxs(Row, { children: [_jsx(GamepadIcon, { buttonName: action === 'up' ? 'thumbstick' : action, handedness: "right" }), _jsxs(ButtonGroup, { "$reverse": false, children: [_jsx(Button, { "$reverse": false, style: {
                                        width: '100px',
                                        backgroundColor: currentMapping &&
                                            currentMapping.controller === 'right' &&
                                            currentMapping.action === action
                                            ? 'rgba(255, 255, 255, 0.6)'
                                            : 'rgba(255, 255, 255, 0.3)',
                                    }, onClick: () => startMapping('right', action), onContextMenu: (e) => e.preventDefault(), children: keyMap.right[action] }), _jsx(Button, { "$reverse": false, style: { width: '24px' }, onClick: () => unmapKey('right', action), onContextMenu: (e) => e.preventDefault(), children: _jsx(FontAwesomeIcon, { icon: faBan }) })] })] }, action))) })] }));
};
//# sourceMappingURL=settings.js.map