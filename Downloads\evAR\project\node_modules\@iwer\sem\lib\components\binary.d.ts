/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import React from 'react';
import { XRController } from 'iwer/lib/device/XRController';
interface BinaryButtonProps {
    xrController: XRController;
    buttonId: string;
    pointerLocked: boolean;
    mappedKey: string;
}
export declare const BinaryButton: React.FC<BinaryButtonProps>;
export {};
//# sourceMappingURL=binary.d.ts.map