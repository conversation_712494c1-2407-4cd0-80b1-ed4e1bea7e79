import { BinaryReader, BinaryWriter } from '@bufbuild/protobuf/wire';
export declare const protobufPackage = "openxr";
export interface Vector2 {
    x: number;
    y: number;
}
export interface Extent2 {
    width: number;
    height: number;
}
export interface Vector3 {
    x: number;
    y: number;
    z: number;
}
export interface Extent3 {
    width: number;
    height: number;
    depth: number;
}
export interface Quaternion {
    x: number;
    y: number;
    z: number;
    w: number;
}
export interface Rect2D {
    offset: Vector2 | undefined;
    extent: Extent2 | undefined;
}
export interface Rect3D {
    offset: Vector3 | undefined;
    extent: Extent3 | undefined;
}
/**
 * All angles are in radians and are the OpenXR FOV
 * https://registry.khronos.org/OpenXR/specs/1.0/html/xrspec.html#angles
 */
export interface Fov {
    /** negative */
    angleLeft: number;
    /** positive */
    angleRight: number;
    /** positive */
    angleUp: number;
    /** negative */
    angleDown: number;
}
/**
 * This is the OpenXR Coordinate System:
 *  https://registry.khronos.org/OpenXR/specs/1.0/html/xrspec.html#coordinate-system
 */
export interface Pose {
    orientation: Quaternion | undefined;
    position: Vector3 | undefined;
}
export declare const Vector2: MessageFns<Vector2>;
export declare const Extent2: MessageFns<Extent2>;
export declare const Vector3: MessageFns<Vector3>;
export declare const Extent3: MessageFns<Extent3>;
export declare const Quaternion: MessageFns<Quaternion>;
export declare const Rect2D: MessageFns<Rect2D>;
export declare const Rect3D: MessageFns<Rect3D>;
export declare const Fov: MessageFns<Fov>;
export declare const Pose: MessageFns<Pose>;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P : P & {
    [K in keyof P]: Exact<P[K], I[K]>;
} & {
    [K in Exclude<keyof I, KeysOfUnion<P>>]: never;
};
export interface MessageFns<T> {
    encode(message: T, writer?: BinaryWriter): BinaryWriter;
    decode(input: BinaryReader | Uint8Array, length?: number): T;
    fromJSON(object: any): T;
    toJSON(message: T): unknown;
    create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
    fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
export {};
//# sourceMappingURL=openxr_core.d.ts.map