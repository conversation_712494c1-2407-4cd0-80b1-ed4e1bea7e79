/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { LocatableComponentJSON, PoseJSON } from '../description.js';
import { SpatialEntityComponent, SpatialEntityComponentType } from './component.js';
import { Mesh } from 'three';
export declare class LocatableComponent extends SpatialEntityComponent {
    type: SpatialEntityComponentType;
    constructor(spatialEntity: Mesh, initData: PoseJSON);
    get position(): import("three").Vector3;
    get rotation(): import("three").Euler;
    get orientation(): import("three").Quaternion;
    get initData(): {
        position: {
            x: number;
            y: number;
            z: number;
        };
        orientation: {
            x: number;
            y: number;
            z: number;
            w: number;
        };
    };
    toJSON(): LocatableComponentJSON;
}
//# sourceMappingURL=locatable.d.ts.map