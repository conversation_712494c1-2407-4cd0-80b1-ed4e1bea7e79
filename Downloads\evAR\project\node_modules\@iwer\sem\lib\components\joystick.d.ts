/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import React from 'react';
import { XRController } from 'iwer/lib/device/XRController';
interface JoystickProps {
    xrController: XRController;
    pointerLocked: boolean;
    buttonId: string;
    mappedKeyUp: string;
    mappedKeyDown: string;
    mappedKeyLeft: string;
    mappedKeyRight: string;
    mappedKeyPressed: string;
}
export declare const Joystick: React.FC<JoystickProps>;
export {};
//# sourceMappingURL=joystick.d.ts.map